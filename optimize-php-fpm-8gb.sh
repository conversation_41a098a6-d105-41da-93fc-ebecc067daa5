#!/bin/bash

# PHP-FPM进程优化脚本 - 针对8GB内存主机
echo "🔧 优化PHP-FPM进程配置 (8GB内存主机)"
echo "======================================="

# 检查当前内存
echo "📊 当前系统内存:"
free -h

echo ""
echo "📋 当前PHP-FPM配置:"
POOL_CONFIG="/etc/php/8.3/fpm/pool.d/www.conf"

if [ -f "$POOL_CONFIG" ]; then
    echo "配置文件: $POOL_CONFIG"
    echo "当前进程管理配置:"
    grep -E "^(pm|pm\.max_children|pm\.start_servers|pm\.min_spare_servers|pm\.max_spare_servers|pm\.max_requests)" "$POOL_CONFIG" || echo "使用默认配置"
else
    echo "❌ 未找到PHP-FPM池配置文件"
    exit 1
fi

echo ""
echo "🎯 8GB内存主机推荐配置:"
echo "  pm = dynamic"
echo "  pm.max_children = 8        # 最大8个进程 (8 × 512M = 4GB)"
echo "  pm.start_servers = 2       # 启动时2个进程"
echo "  pm.min_spare_servers = 1   # 最少1个空闲进程"
echo "  pm.max_spare_servers = 3   # 最多3个空闲进程"
echo "  pm.max_requests = 500      # 每个进程处理500个请求后重启"

echo ""
read -p "是否应用这些优化配置? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔧 应用PHP-FPM优化配置..."
    
    # 备份原始配置
    cp "$POOL_CONFIG" "${POOL_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
    echo "✅ 配置文件已备份"
    
    # 应用新配置
    echo "修改进程管理配置..."
    
    # 如果配置项存在则修改，不存在则添加
    if grep -q "^pm =" "$POOL_CONFIG"; then
        sed -i 's/^pm = .*/pm = dynamic/' "$POOL_CONFIG"
    else
        echo "pm = dynamic" >> "$POOL_CONFIG"
    fi
    
    if grep -q "^pm.max_children" "$POOL_CONFIG"; then
        sed -i 's/^pm.max_children = .*/pm.max_children = 8/' "$POOL_CONFIG"
    else
        echo "pm.max_children = 8" >> "$POOL_CONFIG"
    fi
    
    if grep -q "^pm.start_servers" "$POOL_CONFIG"; then
        sed -i 's/^pm.start_servers = .*/pm.start_servers = 2/' "$POOL_CONFIG"
    else
        echo "pm.start_servers = 2" >> "$POOL_CONFIG"
    fi
    
    if grep -q "^pm.min_spare_servers" "$POOL_CONFIG"; then
        sed -i 's/^pm.min_spare_servers = .*/pm.min_spare_servers = 1/' "$POOL_CONFIG"
    else
        echo "pm.min_spare_servers = 1" >> "$POOL_CONFIG"
    fi
    
    if grep -q "^pm.max_spare_servers" "$POOL_CONFIG"; then
        sed -i 's/^pm.max_spare_servers = .*/pm.max_spare_servers = 3/' "$POOL_CONFIG"
    else
        echo "pm.max_spare_servers = 3" >> "$POOL_CONFIG"
    fi
    
    if grep -q "^pm.max_requests" "$POOL_CONFIG"; then
        sed -i 's/^pm.max_requests = .*/pm.max_requests = 500/' "$POOL_CONFIG"
    else
        echo "pm.max_requests = 500" >> "$POOL_CONFIG"
    fi
    
    echo "✅ 配置修改完成"
    
    echo ""
    echo "📋 新的配置:"
    grep -E "^(pm|pm\.max_children|pm\.start_servers|pm\.min_spare_servers|pm\.max_spare_servers|pm\.max_requests)" "$POOL_CONFIG"
    
    echo ""
    echo "🔄 重启PHP-FPM服务..."
    systemctl restart php8.3-fpm
    
    if [ $? -eq 0 ]; then
        echo "✅ PHP-FPM重启成功"
    else
        echo "❌ PHP-FPM重启失败"
        echo "恢复备份配置..."
        cp "${POOL_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)" "$POOL_CONFIG"
        systemctl restart php8.3-fpm
        exit 1
    fi
    
    echo ""
    echo "📊 验证配置:"
    sleep 2
    echo "当前PHP-FPM进程数:"
    ps aux | grep php-fpm | grep -v grep | wc -l
    
    echo ""
    echo "PHP-FPM进程详情:"
    ps aux | grep php-fpm | grep -v grep | head -5
    
else
    echo "❌ 跳过配置优化"
fi

echo ""
echo "💡 内存监控命令:"
echo "  实时内存监控: watch -n 1 'free -h'"
echo "  PHP-FPM进程监控: watch -n 1 'ps aux | grep php-fpm | grep -v grep'"
echo "  系统负载监控: htop"

echo ""
echo "⚠️  注意事项:"
echo "  1. 如果网站访问量很大，可能需要增加max_children"
echo "  2. 如果内存不足，可以减少max_children到6或更少"
echo "  3. 监控/var/log/php8.3-fpm.log查看是否有内存错误"
echo "  4. 如果出现502错误，可能是进程数不够，需要调整配置"

echo ""
echo "🎉 PHP-FPM优化完成！"
