#!/usr/bin/env php
<?php

/**
 * 重签队列处理器
 * 处理待处理的IPA重签任务
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\ResignService;
use App\Models\ResignRecord;
use App\Config\AppConfig;

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 日志函数
function logMessage($message, $level = 'INFO')
{
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    echo $logMessage;

    // 写入日志文件
    $logFile = '/data/logs/uploadipa/resign-queue.log';
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// 主处理函数
function processResignQueue()
{
    try {
        logMessage("开始处理重签队列");

        $resignService = new ResignService();
        $resignRecordModel = new ResignRecord();

        // 获取待处理的任务
        $pendingRecords = $resignRecordModel->getPendingRecords(5); // 一次处理5个任务

        if (empty($pendingRecords)) {
            logMessage("没有待处理的重签任务");
            return;
        }

        logMessage("找到 " . count($pendingRecords) . " 个待处理的重签任务");

        foreach ($pendingRecords as $record) {
            try {
                logMessage("开始处理重签任务: {$record['_id']} - {$record['original_filename']}");

                $result = $resignService->processResignTask($record['_id']);

                if ($result['success']) {
                    logMessage("重签任务处理成功: {$record['_id']}", 'SUCCESS');
                } else {
                    logMessage("重签任务处理失败: {$record['_id']} - {$result['error']}", 'ERROR');
                }

                // 添加延迟，避免过度占用系统资源
                sleep(2);
            } catch (\Exception $e) {
                logMessage("处理重签任务异常: {$record['_id']} - " . $e->getMessage(), 'ERROR');

                // 更新任务状态为失败
                $resignRecordModel->updateStatus($record['_id'], 'failed', [
                    'error_message' => '处理异常: ' . $e->getMessage()
                ]);
            }
        }

        logMessage("重签队列处理完成");
    } catch (\Exception $e) {
        logMessage("重签队列处理异常: " . $e->getMessage(), 'ERROR');
    }
}

// 清理过期的临时文件
function cleanupTempFiles()
{
    try {
        logMessage("开始清理过期临时文件");

        $config = AppConfig::getInstance();
        $basePath = $config->get('app_storage_path') ?? '/data/storage/';
        $tempPath = $basePath . '/temp';

        if (!is_dir($tempPath)) {
            logMessage("临时目录不存在: {$tempPath}");
            return;
        }

        $files = glob($tempPath . '/*');
        $cleanedCount = 0;
        $cutoffTime = time() - (24 * 60 * 60); // 24小时前

        foreach ($files as $file) {
            if (is_file($file) && filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $cleanedCount++;
                    logMessage("删除过期临时文件: " . basename($file));
                }
            }
        }

        logMessage("清理完成，删除了 {$cleanedCount} 个过期临时文件");
    } catch (\Exception $e) {
        logMessage("清理临时文件异常: " . $e->getMessage(), 'ERROR');
    }
}

// 检查系统资源
function checkSystemResources()
{
    try {
        // 检查磁盘空间
        $freeBytes = disk_free_space('/');
        $totalBytes = disk_total_space('/');
        $usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;

        logMessage(sprintf("磁盘使用率: %.2f%%", $usedPercent));

        if ($usedPercent > 90) {
            logMessage("警告: 磁盘空间不足", 'WARNING');
        }

        // 检查内存使用
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');

        logMessage("内存使用: " . formatBytes($memoryUsage));
    } catch (\Exception $e) {
        logMessage("检查系统资源异常: " . $e->getMessage(), 'ERROR');
    }
}

// 格式化字节数
function formatBytes($bytes, $precision = 2)
{
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

// 信号处理
$running = true;

// 处理SIGTERM和SIGINT信号
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGTERM, function () use (&$running) {
        global $running;
        logMessage("收到SIGTERM信号，准备退出");
        $running = false;
    });

    pcntl_signal(SIGINT, function () use (&$running) {
        global $running;
        logMessage("收到SIGINT信号，准备退出");
        $running = false;
    });
}

// 主循环
logMessage("重签队列处理器启动");
logMessage("PID: " . getmypid());

$lastCleanupTime = 0;
$cleanupInterval = 3600; // 每小时清理一次

while ($running) {
    try {
        // 处理重签队列
        processResignQueue();

        // 定期清理临时文件
        $currentTime = time();
        if ($currentTime - $lastCleanupTime > $cleanupInterval) {
            cleanupTempFiles();
            checkSystemResources();
            $lastCleanupTime = $currentTime;
        }

        // 处理信号
        if (function_exists('pcntl_signal_dispatch')) {
            pcntl_signal_dispatch();
        }

        // 如果没有任务，等待更长时间
        sleep(30); // 每30秒检查一次

    } catch (\Exception $e) {
        logMessage("主循环异常: " . $e->getMessage(), 'ERROR');
        sleep(60); // 出错时等待1分钟再继续
    }
}

logMessage("重签队列处理器退出");
exit(0);
