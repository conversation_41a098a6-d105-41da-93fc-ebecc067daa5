<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲闲iOS工具 - IPA重签</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }

        .nav {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
        }

        .nav a {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav a:hover,
        .nav a.active {
            background: #fff;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: block;
            padding: 20px;
            border: 2px dashed #667eea;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9ff;
        }

        .file-upload-label:hover {
            border-color: #5a67d8;
            background: #f0f2ff;
        }

        .file-upload-label.has-file {
            border-color: #48bb78;
            background: #f0fff4;
            color: #2d3748;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        .progress {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        .record-item {
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .record-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .record-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #cce7ff;
            color: #004085;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .record-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #6c757d;
        }

        .record-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .card {
                padding: 20px;
            }

            .nav {
                flex-direction: column;
                align-items: center;
            }

            .record-info {
                grid-template-columns: 1fr;
            }

            .record-actions {
                justify-content: center;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔐 IPA重签工具</h1>
            <p>快速重签您的iOS应用，自动生成安装页面和二维码</p>
        </div>

        <div class="nav">
            <a href="index.html">📱 上传IPA</a>
            <a href="resign.html" class="active">🔐 重签工具</a>
            <a href="csr.html">📋 CSR生成</a>
            <a href="cer-converter.html">🔄 CER转P12</a>
            <a href="#" onclick="logout()">🚪 退出</a>
        </div>

        <!-- 重签表单 -->
        <div class="card">
            <h2>📤 提交重签任务</h2>
            <div id="resign-alert"></div>

            <form id="resign-form" enctype="multipart/form-data">
                <div class="form-group">
                    <label>选择IPA文件</label>
                    <div class="file-upload">
                        <input type="file" id="ipa-file" name="ipa" accept=".ipa" required>
                        <label for="ipa-file" class="file-upload-label">
                            <div>📱 点击选择IPA文件或拖拽到此处</div>
                            <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">支持最大2GB的IPA文件</div>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label>新Bundle ID（可选）</label>
                    <input type="text" name="new_bundle_id" placeholder="com.yourcompany.newapp">
                    <small style="color: #6c757d;">留空则保持原Bundle ID</small>
                </div>

                <div class="form-group">
                    <label>新应用名称（可选）</label>
                    <input type="text" name="new_app_name" placeholder="新应用名称">
                    <small style="color: #6c757d;">留空则保持原应用名称</small>
                </div>



                <div class="form-group">
                    <label>
                        <input type="checkbox" name="force" checked style="width: auto; margin-right: 8px;">
                        强制重签（推荐）
                    </label>
                </div>

                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    🚀 开始重签
                </button>
            </form>
        </div>

        <!-- 重签记录 -->
        <div class="card">
            <h2>📋 我的重签记录</h2>
            <div id="records-list">
                <div class="loading">加载中...</div>
            </div>
        </div>
    </div>

    <!-- 简单可靠的二维码方案 -->
    <script>
        // 使用Google Charts API生成二维码
        window.generateQRCode = function (container, url) {
            const size = 250;
            const qrUrl = `https://chart.googleapis.com/chart?chs=${size}x${size}&cht=qr&chl=${encodeURIComponent(url)}`;

            container.innerHTML = `
                <img src="${qrUrl}"
                     alt="安装二维码"
                     style="width: ${size}px; height: ${size}px; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                     onload="console.log('二维码加载成功')"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <div style="
                    width: ${size}px;
                    height: ${size}px;
                    border: 2px dashed #ddd;
                    display: none;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    text-align: center;
                    padding: 20px;
                    box-sizing: border-box;
                    border-radius: 8px;
                ">
                    <div style="font-size: 48px; margin-bottom: 10px;">📱</div>
                    <div style="font-size: 14px; color: #2c3e50; margin-bottom: 10px; font-weight: bold;">
                        扫码安装应用
                    </div>
                    <div style="font-size: 12px; color: #6c757d; margin-bottom: 15px;">
                        二维码加载失败
                    </div>
                    <button onclick="copyInstallLink('${url}')" style="
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 12px;
                    ">📋 复制链接</button>
                </div>
            `;
        };
    </script>
    <script src="js/resign.js?v=20250818"></script>
</body>

</html>