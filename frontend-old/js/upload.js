// 上传中心JavaScript逻辑

let currentFile = null;
let uploadInProgress = false;

// 自动轮询相关变量
let pollInterval = null;
let pollCount = 0;
let currentUploadId = null;
const POLL_INTERVALS = {
    FAST: 1000,    // 1秒 - 上传进行中
    NORMAL: 2000,  // 2秒 - 处理中
    SLOW: 3000,    // 3秒 - 等待状态
    RETRY: 5000    // 5秒 - 重试间隔
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeUpload();
});

// 初始化上传功能
function initializeUpload() {
    // 认证方式切换
    const authRadios = document.querySelectorAll('input[name="auth-method"]');
    authRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            toggleAuthConfig(this.value);
            checkFormValidity();
        });
    });

    // 文件上传处理
    const fileInput = document.getElementById('ipa-file');
    const fileUploadArea = document.getElementById('file-upload-area');
    
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽上传
    fileUploadArea.addEventListener('dragover', handleDragOver);
    fileUploadArea.addEventListener('dragleave', handleDragLeave);
    fileUploadArea.addEventListener('drop', handleFileDrop);

    // 表单验证
    const inputs = document.querySelectorAll('#step-auth input, #step-auth textarea');
    inputs.forEach(input => {
        input.addEventListener('input', checkFormValidity);
    });

    // 初始化自动轮询
    initWebSocket();
}

// 切换认证配置显示
function toggleAuthConfig(authMethod) {
    const apiKeyConfig = document.getElementById('api-key-config');
    const appleIdConfig = document.getElementById('apple-id-config');
    
    if (authMethod === 'api_key') {
        apiKeyConfig.style.display = 'block';
        appleIdConfig.style.display = 'none';
    } else {
        apiKeyConfig.style.display = 'none';
        appleIdConfig.style.display = 'block';
    }
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        processFile(file);
    }
}

// 处理拖拽
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
}

function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.name.endsWith('.ipa')) {
            document.getElementById('ipa-file').files = files;
            processFile(file);
        } else {
            showAlert('请选择.ipa格式的文件', 'error');
        }
    }
}

// 处理文件
function processFile(file) {
    currentFile = file;
    
    // 更新文件上传区域显示
    const uploadPlaceholder = document.querySelector('.upload-placeholder');
    uploadPlaceholder.innerHTML = `
        <div class="upload-icon">✅</div>
        <div class="upload-text">
            <p><strong>${file.name}</strong></p>
            <p>文件大小: ${formatFileSize(file.size)}</p>
            <small>点击重新选择文件</small>
        </div>
    `;
    
    // 解析IPA文件信息
    parseIPAInfo(file);
    
    // 检查表单有效性
    checkFormValidity();
}

// 解析IPA文件信息
async function parseIPAInfo(file) {
    try {
        // 显示加载状态
        const ipaInfo = document.getElementById('ipa-info');
        ipaInfo.style.display = 'block';
        ipaInfo.innerHTML = `
            <h4>📋 应用信息</h4>
            <div class="loading">正在解析IPA文件...</div>
        `;

        // 创建FormData并发送到后端解析
        const formData = new FormData();
        formData.append('ipa_file', file);

        const response = await fetch(`${API_BASE}/api/upload/parse-ipa`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${getToken()}`
            },
            body: formData
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`解析IPA文件失败 (${response.status}): ${errorText}`);
        }

        const data = await response.json();

        if (data.success) {
            displayIPAInfo(data.info);
            // 保存文件路径信息，用于后续创建上传任务
            currentFile.serverPath = data.file_path;
            currentFile.serverName = data.file_name;
            currentFile.serverSize = data.file_size;
        } else {
            throw new Error(data.error || '解析失败');
        }
    } catch (error) {
        document.getElementById('ipa-info').innerHTML = `
            <h4>📋 应用信息</h4>
            <div class="error">解析失败: ${error.message}</div>
        `;
    }
}

// 显示IPA信息
function displayIPAInfo(info) {
    const ipaInfo = document.getElementById('ipa-info');
    ipaInfo.innerHTML = `
        <h4>📋 应用信息</h4>
        <div class="info-grid">
            <div class="info-item">
                <label>应用图标:</label>
                <div id="app-icon" class="app-icon">
                    ${info.icon ? `<img src="${info.icon}" alt="App Icon">` : '📱'}
                </div>
            </div>
            <div class="info-item">
                <label>应用名称:</label>
                <span id="app-name">${info.name || '-'}</span>
            </div>
            <div class="info-item">
                <label>Bundle ID:</label>
                <span id="bundle-id">${info.bundle_id || '-'}</span>
            </div>
            <div class="info-item">
                <label>版本号:</label>
                <span id="app-version">${info.version || '-'}</span>
            </div>
            <div class="info-item">
                <label>构建号:</label>
                <span id="build-number">${info.build || '-'}</span>
            </div>
            <div class="info-item">
                <label>文件大小:</label>
                <span id="file-size">${formatFileSize(currentFile.size)}</span>
            </div>
        </div>
    `;
}

// 检查表单有效性
function checkFormValidity() {
    const authMethod = document.querySelector('input[name="auth-method"]:checked').value;
    let isValid = false;
    
    if (authMethod === 'api_key') {
        const apiKeyId = document.getElementById('api-key-id').value.trim();
        const issuerId = document.getElementById('issuer-id').value.trim();
        const apiKeyContent = document.getElementById('api-key-content').value.trim();
        isValid = apiKeyId && issuerId && apiKeyContent;
    } else {
        const appleId = document.getElementById('apple-id').value.trim();
        const appPassword = document.getElementById('app-password').value.trim();
        isValid = appleId && appPassword;
    }
    
    // 还需要有文件
    isValid = isValid && currentFile;
    
    document.getElementById('upload-btn').disabled = !isValid;
}

// 验证API Key
function validateAPIKey() {
    const apiKeyId = document.getElementById('api-key-id').value.trim();
    const issuerId = document.getElementById('issuer-id').value.trim();
    const apiKeyContent = document.getElementById('api-key-content').value.trim();
    const resultDiv = document.getElementById('api-key-validation-result');

    const errors = [];

    // 验证API Key ID格式
    if (!apiKeyId) {
        errors.push('API Key ID 不能为空');
    } else if (!/^[A-Z0-9]{10}$/.test(apiKeyId)) {
        errors.push('API Key ID 格式错误，应该是10个大写字母和数字');
    }

    // 验证Issuer ID格式
    if (!issuerId) {
        errors.push('Issuer ID 不能为空');
    } else if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(issuerId)) {
        errors.push('Issuer ID 格式错误，应该是UUID格式');
    }

    // 验证私钥内容
    if (!apiKeyContent) {
        errors.push('API Key 私钥内容不能为空');
    } else {
        const lines = apiKeyContent.trim().split('\n');
        if (!lines[0].includes('-----BEGIN PRIVATE KEY-----')) {
            errors.push('私钥缺少开始标记');
        }
        if (!lines[lines.length - 1].includes('-----END PRIVATE KEY-----')) {
            errors.push('私钥缺少结束标记');
        }
    }

    // 显示验证结果
    if (errors.length === 0) {
        resultDiv.innerHTML = '<small style="color: green;">✅ API Key 格式验证通过</small>';
        resultDiv.className = 'validation-success';
    } else {
        resultDiv.innerHTML = '<small style="color: red;">❌ 错误：<br>' + errors.map(e => '• ' + e).join('<br>') + '</small>';
        resultDiv.className = 'validation-error';
    }
}

// 验证Apple ID
function validateAppleID() {
    const appleId = document.getElementById('apple-id').value.trim();
    const appPassword = document.getElementById('app-password').value.trim();
    const resultDiv = document.getElementById('apple-id-validation-result');

    const errors = [];

    // 验证Apple ID格式
    if (!appleId) {
        errors.push('Apple ID 不能为空');
    } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(appleId)) {
        errors.push('Apple ID 格式错误，应该是有效的邮箱地址');
    }

    // 验证专属密码格式
    if (!appPassword) {
        errors.push('专属密码不能为空');
    } else if (!/^[a-z]{4}-[a-z]{4}-[a-z]{4}-[a-z]{4}$/.test(appPassword)) {
        errors.push('专属密码格式错误，应该是xxxx-xxxx-xxxx-xxxx格式');
    }

    // 显示验证结果
    if (errors.length === 0) {
        resultDiv.innerHTML = '<small style="color: green;">✅ Apple ID 信息验证通过</small>';
        resultDiv.className = 'validation-success';
    } else {
        resultDiv.innerHTML = '<small style="color: red;">❌ 错误：<br>' + errors.map(e => '• ' + e).join('<br>') + '</small>';
        resultDiv.className = 'validation-error';
    }
}

// 开始上传
async function startUpload() {
    if (uploadInProgress) {
        return;
    }

    // 检查用户是否过期
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (user.is_expired) {
        showAlert('您的账户已过期，无法上传应用。请联系管理员续费。', 'error');
        return;
    }

    uploadInProgress = true;

    // 显示进度界面
    document.getElementById('upload-progress').style.display = 'block';
    document.getElementById('upload-result').style.display = 'none';
    document.getElementById('upload-btn').disabled = true;

    // 重置进度
    updateProgress(5);
    updateStatus('⏳ 创建上传任务...');

    try {
        // 收集上传数据
        const uploadData = collectUploadData();

        updateProgress(15);
        updateStatus('📋 验证上传信息...');

        // 创建上传任务
        const response = await fetch(`${API_BASE}/api/upload/ipa`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${getToken()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(uploadData)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`创建上传任务失败 (${response.status})`);
        }

        const result = await response.json();

        if (result.success) {
            const uploadId = typeof result.upload_id === 'string' ? result.upload_id : (result.upload_id && (result.upload_id.$oid || result.upload_id));

            updateProgress(25);
            updateStatus('🚀 任务已创建，开始处理...');

            // 开始自动轮询进度更新
            startProgressPolling(uploadId);
        } else {
            throw new Error(result.error || '创建上传任务失败');
        }

    } catch (error) {
        showUploadError(error.message);
        uploadInProgress = false;
        document.getElementById('upload-btn').disabled = false;
    }
}

// 注意：实际的文件上传将在后续的GitHub工作流中处理
// 这里只是创建上传任务记录


// 显示演示结果
function showDemoResult() {
    const uploadResult = document.getElementById('upload-result');
    const resultContent = document.getElementById('result-content');

    resultContent.innerHTML = `
        <div class="result-success">
            <h4>🎭 演示完成</h4>
            <p>这是上传流程的演示版本。</p>
            <p>实际上传需要：</p>
            <ul style="text-align: left; margin: 10px 0;">
                <li>✅ HTTP轮询（实时进度更新）</li>
                <li>✅ GitHub账号池管理</li>
                <li>✅ 后台工作流处理</li>
                <li>✅ 真实的TestFlight上传</li>
            </ul>
            <div class="result-actions">
                <button class="btn btn-primary" onclick="resetUpload()">重新演示</button>
                <button class="btn btn-secondary" onclick="showTab('records')">查看上传记录</button>
            </div>
        </div>
    `;

    uploadResult.style.display = 'block';
}

// 收集上传数据
function collectUploadData() {
    const authMethod = document.querySelector('input[name="auth-method"]:checked').value;
    const releaseNotes = document.getElementById('release-notes').value.trim();

    // 检查是否有文件
    if (!currentFile) {
        throw new Error('请先选择IPA文件');
    }

    const data = {
        auth_method: authMethod,
        release_notes: releaseNotes,
        file_name: currentFile.name,
        file_size: currentFile.size
    };

    // 如果有服务器端文件路径，添加到数据中
    if (currentFile.serverPath) {
        data.file_path = currentFile.serverPath;
    }

    if (authMethod === 'api_key') {
        data.api_key_id = document.getElementById('api-key-id').value.trim();
        data.issuer_id = document.getElementById('issuer-id').value.trim();
        data.api_key_content = document.getElementById('api-key-content').value.trim();

        // 验证API Key字段
        if (!data.api_key_id || !data.issuer_id || !data.api_key_content) {
            throw new Error('请填写完整的API Key信息');
        }
    } else {
        data.apple_id = document.getElementById('apple-id').value.trim();
        data.app_password = document.getElementById('app-password').value.trim();

        // 验证Apple ID字段
        if (!data.apple_id || !data.app_password) {
            throw new Error('请填写完整的Apple ID信息');
        }
    }

    return data;
}

// 工具函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showAlert(message, type = 'info') {
    // 创建提示框元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease-out;
    `;

    // 根据类型设置背景色
    const colors = {
        'success': '#10b981',
        'error': '#ef4444',
        'warning': '#f59e0b',
        'info': '#3b82f6'
    };
    alertDiv.style.backgroundColor = colors[type] || colors.info;

    alertDiv.textContent = message;

    // 添加到页面
    document.body.appendChild(alertDiv);

    // 3秒后自动移除
    setTimeout(() => {
        alertDiv.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    }, 3000);
}

// 自动轮询模式

/**
 * 开始智能轮询上传进度
 */
function startProgressPolling(uploadId) {
    currentUploadId = uploadId;
    pollCount = 0;

    // 显示自动刷新指示器
    const indicator = document.getElementById('auto-refresh-indicator');
    if (indicator) {
        indicator.classList.remove('hidden');
    }

    // 立即执行一次
    pollUploadProgress();

    // 设置定时轮询
    scheduleNextPoll(POLL_INTERVALS.FAST);
}

/**
 * 停止轮询
 */
function stopProgressPolling() {
    if (pollInterval) {
        clearTimeout(pollInterval);
        pollInterval = null;
    }

    // 隐藏自动刷新指示器
    const indicator = document.getElementById('auto-refresh-indicator');
    if (indicator) {
        indicator.classList.add('hidden');
    }
}

/**
 * 安排下次轮询
 */
function scheduleNextPoll(interval) {
    if (pollInterval) {
        clearTimeout(pollInterval);
    }

    pollInterval = setTimeout(() => {
        if (currentUploadId && uploadInProgress) {
            pollUploadProgress();
        }
    }, interval);
}

/**
 * 根据状态选择轮询间隔
 */
function getPollingInterval(status, progress) {
    if (status === 'uploading' || progress > 50) {
        return POLL_INTERVALS.FAST;   // 上传中，快速轮询
    } else if (status === 'processing') {
        return POLL_INTERVALS.NORMAL; // 处理中，正常轮询
    } else {
        return POLL_INTERVALS.SLOW;   // 其他状态，慢速轮询
    }
}

// 失败状态处理已简化，由工作流保证错误信息完整性

function initWebSocket() {
    // 使用自动轮询模式
}

// 停止监听进度（已在上面重新定义）

// 获取上传进度（自动轮询）
async function pollUploadProgress() {
    if (!currentUploadId) return;

    pollCount++;

    try {
        const response = await fetch(`${API_BASE}/api/upload/progress/${currentUploadId}`, {
            headers: {
                'Authorization': `Bearer ${getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();

            if (data.success && data.progress) {
                const { status, progress } = data.progress;
                updateUploadProgress(data.progress);

                // 根据状态决定是否继续轮询
                if (status === 'completed') {
                    // 完成，延迟停止轮询让用户看到最终状态
                    setTimeout(() => {
                        stopProgressPolling();
                    }, 2000);
                } else if (status === 'failed') {
                    // 失败状态，错误信息已经由工作流保证存在
                    setTimeout(() => {
                        stopProgressPolling();
                    }, 2000);
                } else if (uploadInProgress) {
                    // 继续轮询，根据状态调整间隔
                    const interval = getPollingInterval(status, progress);
                    scheduleNextPoll(interval);
                }
            } else {
                updateStatus('获取进度失败，正在重试...');
                // 获取失败，使用重试间隔继续轮询
                if (uploadInProgress) {
                    scheduleNextPoll(POLL_INTERVALS.RETRY);
                }
            }
        } else {
            updateStatus('网络错误，正在重试...');
            // 网络错误，使用重试间隔继续轮询
            if (uploadInProgress) {
                scheduleNextPoll(POLL_INTERVALS.RETRY);
            }
        }
    } catch (error) {
        updateStatus('网络错误，正在重试...');
        // 网络错误，使用重试间隔继续轮询
        if (uploadInProgress) {
            scheduleNextPoll(POLL_INTERVALS.RETRY);
        }
    }
}

// 更新上传进度
function updateUploadProgress(data) {
    const { progress, status, error, message } = data;

    // 更新进度条
    if (typeof progress === 'number') {
        updateProgress(progress);
    }

    // 更新状态文本（优先使用后端提供的 message）
    const statusText = message || getStatusDisplayText(status) || '';
    updateStatus(statusText);

    // 完成处理（失败状态由轮询逻辑中的 handleFailedStatus 处理）
    if (status === 'completed' || progress === 100) {
        updateProgress(100);
        updateStatus('🎉 上传完成！');
        showUploadSuccess(data);
        uploadInProgress = false;
        document.getElementById('upload-btn').disabled = false;
        // 注意：完成状态的停止轮询由轮询逻辑处理
    } else if (status === 'failed' && error && typeof error === 'object' &&
               error.title && error.title !== '上传失败') {
        // 如果在这里收到了详细的错误信息，直接显示
        updateStatus('❌ 上传失败');
        showUploadError(error);
        uploadInProgress = false;
        document.getElementById('upload-btn').disabled = false;
        // 注意：失败状态的停止轮询由轮询逻辑处理
    }
}

// 更新进度条
function updateProgress(percentage) {
    const progressFill = document.getElementById('progress-fill');
    const progressPercentage = document.getElementById('progress-percentage');

    progressFill.style.width = percentage + '%';
    progressPercentage.textContent = percentage + '%';
}

// updateStep 函数已移除，步骤显示已简化

// 获取状态显示文本
function getStatusDisplayText(status) {
    const statusMap = {
        'pending': '⏳ 等待处理...',
        'queued': '📋 任务已排队，等待处理...',
        'started': '🚀 任务已创建，开始处理...',
        'processing': '🔄 正在处理...',
        'uploading': '📤 正在上传到TestFlight...',
        'error_details': '📤 正在上传到TestFlight...',
        'completed': '🎉 上传完成！',
        'success': '🎉 上传完成！',
        'failed': '❌ 上传失败'
    };
    return statusMap[status] || `🔄 ${status}`;
}

// 更新状态文本
function updateStatus(status) {
    const statusText = document.getElementById('status-text');
    if (statusText) {
        statusText.textContent = status;
    }
}

// 显示上传错误
function showUploadError(error) {
    const uploadResult = document.getElementById('upload-result');
    const resultContent = document.getElementById('result-content');

    // 如果error是对象，说明有详细的错误信息
    let errorHtml = '';
    if (typeof error === 'object' && error !== null) {
        const errorTitle = error.title || '上传失败';
        const errorMessage = error.message || '未知错误';
        const errorSolution = error.solution || '请检查日志信息';
        const errorDetails = error.details || '';
        const errorStep = error.step || 'unknown';
        const errorCategory = error.category || 'unknown';

        // 根据错误类型设置图标
        const stepIcons = {
            'download': '📥',
            'validation': '🔍',
            'upload': '🚀',
            'authentication': '🔑'
        };
        const stepIcon = stepIcons[errorStep] || '❌';

        // 根据错误类别设置颜色
        const categoryColors = {
            'version_error': '#f59e0b',
            'auth_error': '#ef4444',
            'signing_error': '#8b5cf6',
            'file_error': '#f97316',
            'network_error': '#06b6d4',
            'download_error': '#10b981'
        };
        const categoryColor = categoryColors[errorCategory] || '#ef4444';

        errorHtml = `
            <div class="result-error">
                <div class="error-header" style="border-left: 4px solid ${categoryColor}; padding-left: 12px;">
                    <h4>${stepIcon} ${errorTitle}</h4>
                    <p class="error-step">失败步骤: ${getStepName(errorStep)}</p>
                </div>
                <div class="error-content">
                    <div class="error-message">
                        <strong>错误描述:</strong> ${errorMessage}
                    </div>
                    <div class="error-solution">
                        <strong>💡 解决方案:</strong> ${errorSolution}
                    </div>
                    ${errorDetails ? `
                        <div class="error-details">
                            <strong>详细信息:</strong>
                            <pre style="background: #f3f4f6; padding: 8px; border-radius: 4px; font-size: 12px; overflow-x: auto; white-space: pre-wrap; word-break: break-word;">${errorDetails}</pre>
                        </div>
                    ` : ''}
                    ${error.raw_error ? `
                        <div class="error-raw">
                            <details style="margin-top: 12px;">
                                <summary style="cursor: pointer; font-weight: bold; color: #374151;">🔍 Apple 完整错误信息</summary>
                                <pre style="background: #1f2937; color: #f9fafb; padding: 12px; border-radius: 6px; font-size: 11px; overflow-x: auto; white-space: pre-wrap; word-break: break-word; margin-top: 8px; border: 1px solid #374151;">${error.raw_error}</pre>
                            </details>
                        </div>
                    ` : ''}
                </div>
                <div class="result-actions">
                    ${error.is_retryable ?
                        '<button class="btn btn-primary" onclick="retryUpload()">重试上传</button>' :
                        '<button class="btn btn-secondary" onclick="resetUpload()">修复后重新上传</button>'
                    }
                    <button class="btn btn-secondary" onclick="resetUpload()">重新开始</button>
                </div>
            </div>
        `;
    } else {
        // 简单的错误信息
        errorHtml = `
            <div class="result-error">
                <h4>❌ 上传失败</h4>
                <p>${error}</p>
                <div class="result-actions">
                    <button class="btn btn-primary" onclick="retryUpload()">重试上传</button>
                    <button class="btn btn-secondary" onclick="resetUpload()">重新开始</button>
                </div>
            </div>
        `;
    }

    resultContent.innerHTML = errorHtml;
    uploadResult.style.display = 'block';
}

// 获取步骤名称
function getStepName(step) {
    const stepNames = {
        'download': 'IPA文件下载',
        'validation': '应用验证',
        'upload': '上传到TestFlight',
        'authentication': '身份认证',
        'unknown': '未知步骤'
    };
    return stepNames[step] || step;
}

// 获取步骤名称
function getStepName(step) {
    const stepNames = {
        'download': 'IPA文件下载',
        'validation': '应用验证',
        'upload': '上传到TestFlight',
        'authentication': '身份认证',
        'unknown': '未知步骤'
    };
    return stepNames[step] || step;
}

// 显示上传成功
function showUploadSuccess(data) {
    const uploadResult = document.getElementById('upload-result');
    const resultContent = document.getElementById('result-content');

    resultContent.innerHTML = `
        <div class="result-success">
            <h4>🎉 上传成功</h4>
            <p>您的应用已成功上传到TestFlight！</p>
            ${data.github_url ? `
                <p>
                    <a href="${data.github_url}" target="_blank" class="btn btn-link">
                        📱 查看GitHub Actions进度
                    </a>
                </p>
            ` : ''}
            ${data.workflow_ip || data.workflow_duration ? `
                <div class="workflow-info" style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px; padding: 12px; margin: 12px 0; font-size: 13px;">
                    <div style="font-weight: bold; color: #374151; margin-bottom: 8px;">📊 工作流执行信息</div>
                    ${data.workflow_ip ? `<div>🌐 执行IP: <code style="background: #e2e8f0; padding: 2px 6px; border-radius: 3px;">${data.workflow_ip}</code></div>` : ''}
                    ${data.workflow_duration ? `<div>⏱️ 执行时长: <code style="background: #e2e8f0; padding: 2px 6px; border-radius: 3px;">${data.workflow_duration}秒</code></div>` : ''}
                </div>
            ` : ''}
            <div class="result-actions">
                <button class="btn btn-primary" onclick="resetUpload()">上传新应用</button>
                <button class="btn btn-secondary" onclick="showTab('records')">查看上传记录</button>
            </div>
        </div>
    `;

    uploadResult.style.display = 'block';
}

// 重试上传
function retryUpload() {
    stopProgressPolling();
    document.getElementById('upload-result').style.display = 'none';
    startUpload();
}

// 重置上传
function resetUpload() {
    stopProgressPolling();
    uploadInProgress = false;
    currentFile = null;

    // 重置界面
    document.getElementById('upload-progress').style.display = 'none';
    document.getElementById('upload-result').style.display = 'none';
    document.getElementById('ipa-info').style.display = 'none';

    // 重置文件上传区域
    const uploadPlaceholder = document.querySelector('.upload-placeholder');
    uploadPlaceholder.innerHTML = `
        <div class="upload-icon">📱</div>
        <div class="upload-text">
            <p><strong>点击选择IPA文件</strong></p>
            <p>或拖拽文件到此处</p>
            <small>支持.ipa格式，最大2G</small>
        </div>
    `;

    // 重置表单
    document.getElementById('ipa-file').value = '';
    document.getElementById('release-notes').value = '';

    // 检查表单有效性
    checkFormValidity();
}
