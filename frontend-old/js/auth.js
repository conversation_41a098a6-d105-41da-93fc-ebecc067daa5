/**
 * 认证相关函数
 */

/**
 * 检查用户是否已登录
 */
function isLoggedIn() {
    return !!localStorage.getItem('token');
}

/**
 * 获取认证token
 */
function getToken() {
    return localStorage.getItem('token');
}

/**
 * 获取用户信息
 */
function getUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
}

/**
 * 设置认证信息
 */
function setAuth(token, user) {
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(user));
}

/**
 * 清除认证信息
 */
function clearAuth() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
}

/**
 * 退出登录
 */
function logout() {
    clearAuth();
    window.location.href = 'login.html';
}

/**
 * 检查登录状态并重定向
 */
function checkAuth() {
    if (!isLoggedIn()) {
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

/**
 * 验证token是否有效
 */
async function validateToken() {
    const token = getToken();
    if (!token) {
        return false;
    }

    try {
        const response = await fetch('/api/auth/validate', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            return true;
        } else {
            clearAuth();
            return false;
        }
    } catch (error) {
        console.error('Token validation failed:', error);
        return false;
    }
}

/**
 * 自动刷新token
 */
async function refreshToken() {
    const token = getToken();
    if (!token) {
        return false;
    }

    try {
        const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.token) {
                localStorage.setItem('token', data.token);
                return true;
            }
        }
        
        clearAuth();
        return false;
    } catch (error) {
        console.error('Token refresh failed:', error);
        clearAuth();
        return false;
    }
}
