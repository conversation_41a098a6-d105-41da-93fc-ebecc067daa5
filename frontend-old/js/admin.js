// 管理员后台JavaScript

const API_BASE = 'https://api.ios.xxyx.cn';

// 获取存储的token
function getToken() {
    return localStorage.getItem('token');
}

// 检查登录状态
async function checkAuth() {
    const token = getToken();
    if (!token) {
        alert('请先登录');
        window.location.href = '/login.html';
        return false;
    }

    // 验证token有效性
    try {
        const response = await fetch(`${API_BASE}/api/auth/verify`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            alert('登录已过期，请重新登录');
            window.location.href = '/login.html';
            return false;
        }

        const result = await response.json();
        if (!result.success || result.user.role !== 'admin') {
            alert('需要管理员权限');
            window.location.href = '/index.html';
            return false;
        }

        return true;
    } catch (error) {
        console.error('认证检查失败:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        alert('认证检查失败，请重新登录');
        window.location.href = '/login.html';
        return false;
    }
}

// API请求函数
async function apiRequest(endpoint, options = {}) {
    const token = getToken();
    if (!token) {
        throw new Error('未登录');
    }

    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    };

    const response = await fetch(`${API_BASE}${endpoint}`, {
        ...defaultOptions,
        ...options
    });

    // 如果是401错误，说明token失效
    if (response.status === 401) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        alert('登录已过期，请重新登录');
        window.location.href = '/login.html';
        return;
    }

    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '请求失败');
    }

    return response.json();
}

// 显示提示信息
function showAlert(containerId, message, type = 'success') {
    const container = document.getElementById(containerId);
    container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// 切换标签页
function showTab(tabName, event) {
    // 隐藏所有内容
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });

    // 移除所有标签的active类
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // 显示选中的内容
    document.getElementById(tabName).classList.add('active');

    // 添加选中标签的active类
    if (event && event.target) {
        event.target.classList.add('active');
    } else {
        // 如果没有event，通过tabName找到对应的标签
        document.querySelector(`[onclick*="showTab('${tabName}')"]`)?.classList.add('active');
    }

    // 加载对应数据
    switch (tabName) {
        case 'stats':
            loadStats();
            break;
        case 'users':
            loadUsers();
            break;
        case 'activation-codes':
            loadActivationCodes();
            break;
        case 'github-accounts':
            loadGitHubAccounts();
            break;
    }
}

// 加载系统统计
async function loadStats() {
    try {
        const stats = await apiRequest('/api/admin/stats');
        
        const statsGrid = document.getElementById('stats-grid');
        statsGrid.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${stats.stats.total_users}</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.active_users}</div>
                <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.total_activation_codes}</div>
                <div class="stat-label">激活码总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.active_activation_codes}</div>
                <div class="stat-label">有效激活码</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.total_github_accounts}</div>
                <div class="stat-label">GitHub账号</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.active_github_accounts}</div>
                <div class="stat-label">活跃账号</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.total_uploads}</div>
                <div class="stat-label">总上传数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.queued_uploads}</div>
                <div class="stat-label">排队中</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.stats.uploading_uploads}</div>
                <div class="stat-label">上传中</div>
            </div>
        `;
    } catch (error) {
        document.getElementById('stats-grid').innerHTML = `<div class="alert alert-error">加载统计信息失败: ${error.message}</div>`;
    }
}

// 加载用户列表
async function loadUsers() {
    try {
        const response = await apiRequest('/api/admin/users');
        
        const usersList = document.getElementById('users-list');
        if (response.users.length === 0) {
            usersList.innerHTML = '<p>暂无用户</p>';
            return;
        }

        const table = `
            <table class="table">
                <thead>
                    <tr>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>角色</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${response.users.map(user => {
                        // 计算激活状态和剩余时长
                        let statusDisplay = '';
                        if (user.status === 'active') {
                            if (user.expires_at) {
                                const expiresAt = new Date(user.expires_at);
                                const now = new Date();
                                const remainingMs = expiresAt.getTime() - now.getTime();

                                if (remainingMs > 0) {
                                    const remainingDays = Math.ceil(remainingMs / (1000 * 60 * 60 * 24));
                                    statusDisplay = `<span class="status active">✅ 已激活 (剩余${remainingDays}天)</span>`;
                                } else {
                                    const expiredDays = Math.ceil(Math.abs(remainingMs) / (1000 * 60 * 60 * 24));
                                    statusDisplay = `<span class="status expired">⏰ 已过期 (${expiredDays}天)</span>`;
                                }
                            } else {
                                statusDisplay = `<span class="status active">✅ 已激活 (永久)</span>`;
                            }
                        } else {
                            statusDisplay = `<span class="status inactive">❌ 未激活</span>`;
                        }

                        return `
                            <tr>
                                <td>${user.username}</td>
                                <td>${user.email}</td>
                                <td>${user.role}</td>
                                <td>${statusDisplay}</td>
                                <td>${new Date(user.created_at).toLocaleString()}</td>
                                <td>${user.last_login ? new Date(user.last_login).toLocaleString() : '从未登录'}</td>
                                <td>
                                    ${user.role !== 'admin' ? `
                                        <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                                            ${(() => {
                                                // 检查是否过期
                                                if (user.expires_at) {
                                                    const expiresAt = new Date(user.expires_at);
                                                    const now = new Date();
                                                    const isExpired = now.getTime() > expiresAt.getTime();

                                                    if (isExpired) {
                                                        return `
                                                            <button class="btn btn-success btn-sm" onclick="extendUserExpiry('${user._id}', '${user.username}', 30)" title="延长30天">
                                                                🔄 续期
                                                            </button>
                                                        `;
                                                    }
                                                }
                                                return '';
                                            })()}
                                            <button class="btn btn-warning btn-sm" onclick="resetUserPassword('${user._id}', '${user.username}')" title="重置密码">
                                                🔑 重置密码
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="deleteUser('${user._id}', '${user.username}')" title="删除用户">
                                                🗑️ 删除
                                            </button>
                                        </div>
                                    ` : '<span class="text-muted">管理员账户</span>'}
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;
        
        usersList.innerHTML = table;
    } catch (error) {
        document.getElementById('users-list').innerHTML = `<div class="alert alert-error">加载用户列表失败: ${error.message}</div>`;
    }
}

// 加载激活码列表
async function loadActivationCodes() {
    try {
        const response = await apiRequest('/api/admin/activation-codes');
        
        const codesList = document.getElementById('activation-codes-list');
        if (response.codes.length === 0) {
            codesList.innerHTML = '<p>暂无激活码</p>';
            return;
        }

        const table = `
            <table class="table">
                <thead>
                    <tr>
                        <th>激活码</th>
                        <th>状态</th>
                        <th>用户有效期</th>
                        <th>使用情况</th>
                        <th>创建时间</th>
                        <th>使用时间</th>
                    </tr>
                </thead>
                <tbody>
                    ${response.codes.map(code => {
                        // 获取用户激活后的有效期天数
                        const validityDays = code.user_validity_days || 30;
                        const usedAt = code.used_at ? new Date(code.used_at).toLocaleString() : '未使用';

                        return `
                            <tr>
                                <td><code>${code.code}</code></td>
                                <td><span class="status ${code.status}">${code.status}</span></td>
                                <td>${validityDays}天</td>
                                <td>${code.used_count}/${code.max_uses}</td>
                                <td>${new Date(code.created_at).toLocaleString()}</td>
                                <td>${usedAt}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;
        
        codesList.innerHTML = table;
    } catch (error) {
        document.getElementById('activation-codes-list').innerHTML = `<div class="alert alert-error">加载激活码列表失败: ${error.message}</div>`;
    }
}

// 加载GitHub账号列表
async function loadGitHubAccounts() {
    try {
        // 获取包含使用统计的账号数据
        const response = await apiRequest('/api/admin/github-usage-stats');

        // 调试：打印接收到的数据
        console.log('GitHub账号数据:', response);
        if (response.accounts && response.accounts.length > 0) {
            console.log('第一个账号的数据:', response.accounts[0]);
        }

        // 加载统计信息
        loadGitHubStats(response.accounts);

        const accountsList = document.getElementById('github-accounts-list');
        if (!response.accounts || response.accounts.length === 0) {
            accountsList.innerHTML = '<p>暂无GitHub账号</p>';
            return;
        }

        const maxUsageLimit = 2500; // 与后端保持一致

        const table = `
            <table class="table">
                <thead>
                    <tr>
                        <th>账号信息</th>
                        <th>使用状态</th>
                        <th>使用情况</th>
                        <th>使用统计</th>
                        <th>Token状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${response.accounts.map(account => {
                        // GitHub Actions分钟数使用情况（如果有的话）
                        const totalUsage = account.total_usage_minutes || 0;
                        const remainingMinutes = maxUsageLimit - totalUsage;
                        const usagePercentage = Math.min((totalUsage / maxUsageLimit) * 100, 100);
                        const isInUse = account.is_in_use;
                        const hasToken = account.has_token !== false;

                        // 工作流执行统计信息
                        const usageCount = account.usage_count || 0;
                        const totalUsageSeconds = account.total_usage_seconds || 0;
                        const averageDuration = account.average_duration || 0;

                        let statusClass = 'idle';
                        let statusText = '空闲';
                        if (isInUse) {
                            statusClass = 'in-use';
                            statusText = '使用中';
                        } else if (remainingMinutes < 60) {
                            statusClass = 'danger';
                            statusText = '时间不足';
                        } else if (remainingMinutes < 300) {
                            statusClass = 'warning';
                            statusText = '即将用完';
                        }

                        return `
                            <tr>
                                <td>
                                    <strong>${account.username}</strong><br>
                                    <small>${account.email}</small><br>
                                    <span class="status ${account.status}">${account.status}</span>
                                    <br><small>创建: ${new Date(account.created_at).toLocaleDateString()}</small>
                                    ${account.last_used_at ? `<br><small>最后使用: ${new Date(account.last_used_at).toLocaleString()}</small>` : ''}
                                </td>
                                <td>
                                    <span class="status-indicator ${statusClass}"></span>
                                    ${statusText}
                                </td>
                                <td>
                                    <div class="usage-bar">
                                        <div class="usage-fill" style="width: ${usagePercentage}%"></div>
                                    </div>
                                    <div class="usage-text">
                                        已使用: ${totalUsage} / ${maxUsageLimit} 分钟 (${usagePercentage.toFixed(1)}%)
                                        <br>剩余: ${remainingMinutes} 分钟
                                    </div>
                                </td>
                                <td>
                                    <div class="usage-stats">
                                        <div><strong>使用次数:</strong> ${usageCount}次</div>
                                        <div><strong>总时长:</strong> ${formatDuration(totalUsageSeconds)}</div>
                                        <div><strong>平均时长:</strong> ${formatDuration(averageDuration)}</div>
                                        ${account.last_used_at ? `<div><small>最后使用: ${new Date(account.last_used_at).toLocaleString('zh-CN')}</small></div>` : '<div><small>从未使用</small></div>'}
                                    </div>
                                </td>
                                <td>
                                    ${hasToken ? '✅ 正常' : '❌ 缺失'}
                                </td>
                                <td>
                                    <div class="account-actions">
                                        <button class="btn btn-${account.status === 'active' ? 'danger' : 'success'} btn-sm"
                                                onclick="toggleAccountStatus('${account._id}', '${account.status}')">
                                            ${account.status === 'active' ? '禁用' : '启用'}
                                        </button>
                                        <button class="btn btn-primary btn-sm"
                                                onclick="resetAccountUsage('${account._id}', '${account.username}')">
                                            重置使用
                                        </button>
                                        ${isInUse ? `
                                            <button class="btn btn-warning btn-sm"
                                                    onclick="releaseAccount('${account._id}', '${account.username}')">
                                                释放账号
                                            </button>
                                        ` : ''}
                                    </div>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;

        accountsList.innerHTML = table;
    } catch (error) {
        document.getElementById('github-accounts-list').innerHTML = `<div class="alert alert-error">加载GitHub账号列表失败: ${error.message}</div>`;
    }
}

// 加载GitHub账号统计
function loadGitHubStats(accounts) {
    const maxUsageLimit = 2500;
    const totalAccounts = accounts.length;
    const activeAccounts = accounts.filter(acc => acc.status === 'active').length;
    const inUseAccounts = accounts.filter(acc => acc.is_in_use).length;
    const idleAccounts = activeAccounts - inUseAccounts;

    let totalUsage = 0;
    let warningAccounts = 0;
    let dangerAccounts = 0;

    accounts.forEach(acc => {
        const usage = acc.total_usage_minutes || 0;
        totalUsage += usage;
        const remaining = maxUsageLimit - usage;

        if (remaining < 60) {
            dangerAccounts++;
        } else if (remaining < 300) {
            warningAccounts++;
        }
    });

    const avgUsage = totalAccounts > 0 ? Math.round(totalUsage / totalAccounts) : 0;

    const statsGrid = document.getElementById('github-stats');
    statsGrid.innerHTML = `
        <div class="stat-card">
            <div class="stat-number">${totalAccounts}</div>
            <div class="stat-label">总账号数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${activeAccounts}</div>
            <div class="stat-label">活跃账号</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${idleAccounts}</div>
            <div class="stat-label">空闲账号</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${inUseAccounts}</div>
            <div class="stat-label">使用中</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${avgUsage}</div>
            <div class="stat-label">平均使用(分钟)</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">${warningAccounts + dangerAccounts}</div>
            <div class="stat-label">需要关注</div>
        </div>
    `;
}

// 切换GitHub账号状态
async function toggleAccountStatus(accountId, currentStatus) {
    try {
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';

        await apiRequest(`/api/admin/github-accounts/${accountId}/status`, {
            method: 'PUT',
            body: JSON.stringify({ status: newStatus })
        });

        showAlert('github-alert', '账号状态更新成功');
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `更新账号状态失败: ${error.message}`, 'error');
    }
}

// 重置单个账号使用时长
async function resetAccountUsage(accountId, username) {
    if (!confirm(`确定要重置账号 "${username}" 的使用时长吗？`)) {
        return;
    }

    try {
        await apiRequest(`/api/admin/github-accounts/${accountId}/reset-usage`, {
            method: 'POST'
        });

        showAlert('github-alert', `账号 "${username}" 使用时长重置成功`);
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `重置使用时长失败: ${error.message}`, 'error');
    }
}

// 释放账号（标记为空闲）
async function releaseAccount(accountId, username) {
    if (!confirm(`确定要释放账号 "${username}" 吗？这将中断当前使用该账号的任务。`)) {
        return;
    }

    try {
        await apiRequest(`/api/admin/github-accounts/${accountId}/release`, {
            method: 'POST'
        });

        showAlert('github-alert', `账号 "${username}" 已释放`);
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `释放账号失败: ${error.message}`, 'error');
    }
}

// 刷新所有账号状态
async function refreshAllAccounts() {
    if (!confirm('确定要刷新所有账号状态吗？这可能需要一些时间。')) {
        return;
    }

    try {
        showAlert('github-alert', '正在刷新所有账号状态，请稍候...');

        await apiRequest('/api/admin/github-accounts/refresh-all', {
            method: 'POST'
        });

        showAlert('github-alert', '所有账号状态刷新成功');
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `刷新账号状态失败: ${error.message}`, 'error');
    }
}

// 重置所有账号使用时长
async function resetAllUsage() {
    if (!confirm('确定要重置所有账号的使用时长吗？此操作不可撤销！')) {
        return;
    }

    try {
        await apiRequest('/api/admin/github-accounts/reset-all-usage', {
            method: 'POST'
        });

        showAlert('github-alert', '所有账号使用时长重置成功');
        loadGitHubAccounts();
    } catch (error) {
        showAlert('github-alert', `重置所有使用时长失败: ${error.message}`, 'error');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    if (!(await checkAuth())) {
        return;
    }

    // 加载初始数据
    loadStats();

    // 绑定表单事件
    document.getElementById('create-user-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            const data = {
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password'),
                role: formData.get('role')
            };

            await apiRequest('/api/admin/users', {
                method: 'POST',
                body: JSON.stringify(data)
            });

            showAlert('user-alert', '用户创建成功');
            this.reset();
            loadUsers();
        } catch (error) {
            showAlert('user-alert', `创建用户失败: ${error.message}`, 'error');
        }
    });

    document.getElementById('create-activation-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            const expiresInDays = parseInt(formData.get('expires_in_days'));
            const data = {
                expires_in: expiresInDays * 24, // 转换为小时
                max_uses: 1, // 固定为1次使用
                expires_in_days: expiresInDays // 保存原始天数用于显示
            };

            const response = await apiRequest('/api/admin/activation-codes', {
                method: 'POST',
                body: JSON.stringify(data)
            });

            showAlert('activation-alert', `激活码创建成功: ${response.code}`);
            this.reset();
            loadActivationCodes();
        } catch (error) {
            showAlert('activation-alert', `创建激活码失败: ${error.message}`, 'error');
        }
    });

    document.getElementById('add-github-form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            const data = {
                username: formData.get('username'),
                email: formData.get('email'),
                token: formData.get('token')
            };

            await apiRequest('/api/admin/github-accounts', {
                method: 'POST',
                body: JSON.stringify(data)
            });

            showAlert('github-alert', 'GitHub账号添加成功');
            this.reset();
            loadGitHubAccounts();
        } catch (error) {
            showAlert('github-alert', `添加GitHub账号失败: ${error.message}`, 'error');
        }
    });
});

// 延长用户有效期
async function extendUserExpiry(userId, username, days = 30) {
    const customDays = prompt(`为用户 "${username}" 延长有效期多少天？`, days);
    if (!customDays || isNaN(customDays) || customDays <= 0) {
        return;
    }

    try {
        const response = await apiRequest('/api/admin/users/extend-expiry', {
            method: 'POST',
            body: JSON.stringify({
                user_id: userId,
                days: parseInt(customDays)
            })
        });

        showAlert('user-alert', `用户 "${username}" 有效期已延长 ${customDays} 天`);
        loadUsers(); // 重新加载用户列表
    } catch (error) {
        showAlert('user-alert', `延长有效期失败: ${error.message}`, 'error');
    }
}

// 重置用户密码
async function resetUserPassword(userId, username) {
    const newPassword = prompt(`为用户 "${username}" 设置新密码：`, '');
    if (!newPassword || newPassword.length < 6) {
        alert('密码长度至少6位');
        return;
    }

    if (!confirm(`确定要重置用户 "${username}" 的密码吗？`)) {
        return;
    }

    try {
        await apiRequest('/api/admin/users/reset-password', {
            method: 'POST',
            body: JSON.stringify({
                user_id: userId,
                new_password: newPassword
            })
        });

        showAlert('user-alert', `用户 "${username}" 密码重置成功`);
    } catch (error) {
        showAlert('user-alert', `重置密码失败: ${error.message}`, 'error');
    }
}

// 删除用户
async function deleteUser(userId, username) {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可撤销！`)) {
        return;
    }

    try {
        await apiRequest(`/api/admin/users/${userId}`, {
            method: 'DELETE'
        });

        showAlert('user-alert', `用户 "${username}" 删除成功`);
        loadUsers(); // 重新加载用户列表
    } catch (error) {
        showAlert('user-alert', `删除用户失败: ${error.message}`, 'error');
    }
}

// 格式化时长显示
function formatDuration(seconds) {
    if (!seconds || seconds === 0) return '0秒';

    if (seconds < 60) {
        return seconds + '秒';
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return minutes + '分' + (remainingSeconds > 0 ? remainingSeconds + '秒' : '');
    } else {
        const hours = Math.floor(seconds / 3600);
        const remainingMinutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        let result = hours + '小时';
        if (remainingMinutes > 0) {
            result += remainingMinutes + '分';
        }
        if (remainingSeconds > 0) {
            result += remainingSeconds + '秒';
        }
        return result;
    }
}

// ==================== 工具函数 ====================

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// ==================== 新功能：证书管理 ====================

// 加载证书管理数据
async function loadCertificatesData() {
    try {
        // 加载证书列表
        await loadCertificatesList();

        // 加载证书对
        await loadCertificatePairs();

        // 初始化证书上传表单
        initCertificateUploadForms();

    } catch (error) {
        console.error('加载证书数据失败:', error);
        showAlert('certificate-alert', '加载证书数据失败', 'error');
    }
}

// 加载证书列表
async function loadCertificatesList() {
    const token = getToken();
    const listContainer = document.getElementById('certificates-list');

    if (!listContainer) return;

    try {
        const response = await fetch(`${API_BASE}/api/certificates`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            displayCertificatesList(data.certificates);
        } else {
            listContainer.innerHTML = '<div class="alert alert-error">加载证书列表失败</div>';
        }
    } catch (error) {
        console.error('加载证书列表失败:', error);
        listContainer.innerHTML = '<div class="alert alert-error">网络错误</div>';
    }
}

// 显示证书列表
function displayCertificatesList(certificates) {
    const listContainer = document.getElementById('certificates-list');

    if (certificates.length === 0) {
        listContainer.innerHTML = '<div class="alert alert-info">暂无证书</div>';
        return;
    }

    const certificatesHtml = certificates.map(cert => {
        const createdAt = new Date(cert.created_at).toLocaleString();
        const expiresAt = cert.expires_at ? new Date(cert.expires_at).toLocaleString() : '无';
        const typeText = getTypeText(cert.type);

        return `
            <div class="certificate-item" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4>${cert.name}</h4>
                    <span class="badge badge-${cert.type}">${typeText}</span>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 14px;">
                    <div><strong>类型:</strong> ${typeText}</div>
                    <div><strong>文件大小:</strong> ${formatFileSize(cert.file_size)}</div>
                    <div><strong>创建时间:</strong> ${createdAt}</div>
                    <div><strong>过期时间:</strong> ${expiresAt}</div>
                    ${cert.team_name ? `<div><strong>团队:</strong> ${cert.team_name}</div>` : ''}
                    ${cert.bundle_id ? `<div><strong>Bundle ID:</strong> ${cert.bundle_id}</div>` : ''}
                    <div><strong>使用次数:</strong> ${cert.usage_count || 0}</div>
                </div>
                <div style="margin-top: 10px;">
                    <button class="btn btn-danger btn-sm" onclick="deleteCertificate('${cert._id}')">删除</button>
                </div>
            </div>
        `;
    }).join('');

    listContainer.innerHTML = `<h3>证书列表</h3>${certificatesHtml}`;
}

// 获取证书类型文本
function getTypeText(type) {
    const typeMap = {
        'p12': 'P12证书',
        'mobileprovision': 'mobileprovision',
        'csr': 'CSR文件'
    };
    return typeMap[type] || type;
}

// 加载证书对
async function loadCertificatePairs() {
    const token = getToken();
    const pairsContainer = document.getElementById('certificate-pairs');

    if (!pairsContainer) return;

    try {
        const response = await fetch(`${API_BASE}/api/certificates/pairs`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            displayCertificatePairs(data.certificate_pairs);
        } else {
            pairsContainer.innerHTML = '<div class="alert alert-error">加载证书对失败</div>';
        }
    } catch (error) {
        console.error('加载证书对失败:', error);
        pairsContainer.innerHTML = '<div class="alert alert-error">网络错误</div>';
    }
}

// 显示证书对
function displayCertificatePairs(pairs) {
    const pairsContainer = document.getElementById('certificate-pairs');

    if (pairs.length === 0) {
        pairsContainer.innerHTML = '<div class="alert alert-info">暂无可用证书对</div>';
        return;
    }

    const pairsHtml = pairs.map(pair => {
        const expiresAt = new Date(pair.expires_at).toLocaleString();

        return `
            <div class="certificate-pair" style="border: 1px solid #28a745; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f8fff9;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4>证书对 - ${pair.team_name}</h4>
                    <span class="badge badge-success">可用</span>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 14px;">
                    <div><strong>Bundle ID:</strong> ${pair.bundle_id}</div>
                    <div><strong>团队名称:</strong> ${pair.team_name}</div>
                    <div><strong>过期时间:</strong> ${expiresAt}</div>
                    <div><strong>P12证书:</strong> ${pair.p12.name}</div>
                    <div><strong>mobileprovision:</strong> ${pair.mobileprovision.name}</div>
                </div>
            </div>
        `;
    }).join('');

    pairsContainer.innerHTML = `<h3>可用证书对</h3>${pairsHtml}`;
}

// 初始化证书上传表单
function initCertificateUploadForms() {
    // P12证书上传表单
    const p12Form = document.getElementById('upload-p12-form');
    if (p12Form) {
        p12Form.addEventListener('submit', handleP12Upload);
    }

    // mobileprovision上传表单
    const provisionForm = document.getElementById('upload-provision-form');
    if (provisionForm) {
        provisionForm.addEventListener('submit', handleProvisionUpload);
    }
}

// 处理P12证书上传
async function handleP12Upload(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const alertDiv = document.getElementById('certificate-alert');

    submitBtn.disabled = true;
    submitBtn.textContent = '上传中...';

    try {
        const token = getToken();
        const response = await fetch(`${API_BASE}/api/certificates/p12`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('certificate-alert', 'P12证书上传成功', 'success');
            form.reset();
            await loadCertificatesData(); // 重新加载数据
        } else {
            showAlert('certificate-alert', data.error || 'P12证书上传失败', 'error');
        }
    } catch (error) {
        console.error('P12证书上传失败:', error);
        showAlert('certificate-alert', '网络错误，请重试', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = '上传P12证书';
    }
}

// 处理mobileprovision上传
async function handleProvisionUpload(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const alertDiv = document.getElementById('certificate-alert');

    submitBtn.disabled = true;
    submitBtn.textContent = '上传中...';

    try {
        const token = getToken();
        const response = await fetch(`${API_BASE}/api/certificates/mobileprovision`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('certificate-alert', 'mobileprovision文件上传成功', 'success');
            form.reset();
            await loadCertificatesData(); // 重新加载数据
        } else {
            showAlert('certificate-alert', data.error || 'mobileprovision文件上传失败', 'error');
        }
    } catch (error) {
        console.error('mobileprovision上传失败:', error);
        showAlert('certificate-alert', '网络错误，请重试', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = '上传mobileprovision';
    }
}

// 删除证书
async function deleteCertificate(certificateId) {
    if (!confirm('确定要删除这个证书吗？此操作不可撤销。')) {
        return;
    }

    try {
        const token = getToken();
        const response = await fetch(`${API_BASE}/api/certificates/${certificateId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            showAlert('certificate-alert', '证书删除成功', 'success');
            await loadCertificatesData(); // 重新加载数据
        } else {
            showAlert('certificate-alert', data.error || '证书删除失败', 'error');
        }
    } catch (error) {
        console.error('删除证书失败:', error);
        showAlert('certificate-alert', '网络错误，请重试', 'error');
    }
}

// ==================== 新功能：重签记录管理 ====================

// 加载重签记录
async function loadResignRecords() {
    const token = getToken();
    const listContainer = document.getElementById('resign-records-list');

    if (!listContainer) return;

    try {
        const response = await fetch(`${API_BASE}/api/admin/resign/records?limit=50`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            displayResignRecords(data.records);
        } else {
            listContainer.innerHTML = '<div class="alert alert-error">加载重签记录失败</div>';
        }
    } catch (error) {
        console.error('加载重签记录失败:', error);
        listContainer.innerHTML = '<div class="alert alert-error">网络错误</div>';
    }
}

// 显示重签记录
function displayResignRecords(records) {
    const listContainer = document.getElementById('resign-records-list');

    if (records.length === 0) {
        listContainer.innerHTML = '<div class="alert alert-info">暂无重签记录</div>';
        return;
    }

    const recordsHtml = records.map(record => {
        const createdAt = new Date(record.created_at).toLocaleString();
        const statusClass = getResignStatusClass(record.status);
        const statusText = getResignStatusText(record.status);

        return `
            <div class="resign-record" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4>${record.original_filename}</h4>
                    <span class="badge ${statusClass}">${statusText}</span>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 14px;">
                    <div><strong>应用名称:</strong> ${record.app_name || '未知'}</div>
                    <div><strong>Bundle ID:</strong> ${record.bundle_id || '未知'}</div>
                    <div><strong>版本:</strong> ${record.version || '未知'}</div>
                    <div><strong>创建时间:</strong> ${createdAt}</div>
                    <div><strong>文件大小:</strong> ${formatFileSize(record.original_file_size)}</div>
                    <div><strong>用户ID:</strong> ${record.user_id}</div>
                    ${record.error_message ? `<div style="color: #dc3545;"><strong>错误:</strong> ${record.error_message}</div>` : ''}
                </div>
                <div style="margin-top: 10px;">
                    <button class="btn btn-danger btn-sm" onclick="deleteResignRecord('${record._id}')">删除记录</button>
                </div>
            </div>
        `;
    }).join('');

    listContainer.innerHTML = `<h3>重签记录列表</h3>${recordsHtml}`;
}

// 获取重签状态样式
function getResignStatusClass(status) {
    const statusMap = {
        'pending': 'badge-warning',
        'processing': 'badge-info',
        'success': 'badge-success',
        'failed': 'badge-danger'
    };
    return statusMap[status] || 'badge-secondary';
}

// 获取重签状态文本
function getResignStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'processing': '处理中',
        'success': '成功',
        'failed': '失败'
    };
    return statusMap[status] || '未知';
}

// 删除重签记录
async function deleteResignRecord(recordId) {
    if (!confirm('确定要删除这个重签记录吗？此操作不可撤销。')) {
        return;
    }

    try {
        const token = getToken();
        const response = await fetch(`${API_BASE}/api/admin/resign/records/${recordId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            showAlert('resign-alert', '重签记录删除成功', 'success');
            await loadResignRecords(); // 重新加载数据
        } else {
            showAlert('resign-alert', data.error || '重签记录删除失败', 'error');
        }
    } catch (error) {
        console.error('删除重签记录失败:', error);
        showAlert('resign-alert', '网络错误，请重试', 'error');
    }
}

// ==================== 新功能：分发统计 ====================

// 加载分发统计
async function loadDistributionStats() {
    const token = getToken();
    const statsContainer = document.getElementById('distribution-stats-grid');
    const appsContainer = document.getElementById('popular-apps');

    if (!statsContainer || !appsContainer) return;

    try {
        const response = await fetch(`${API_BASE}/api/admin/distribution/stats`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            displayDistributionStats(data.stats);
            displayPopularApps(data.popular_apps);
        } else {
            statsContainer.innerHTML = '<div class="alert alert-error">加载分发统计失败</div>';
        }
    } catch (error) {
        console.error('加载分发统计失败:', error);
        statsContainer.innerHTML = '<div class="alert alert-error">网络错误</div>';
    }
}

// 显示分发统计
function displayDistributionStats(stats) {
    const statsContainer = document.getElementById('distribution-stats-grid');

    const statsHtml = `
        <div class="stat-card">
            <h3>${stats.total_distributions || 0}</h3>
            <p>总分发数</p>
        </div>
        <div class="stat-card">
            <h3>${stats.total_installs || 0}</h3>
            <p>总安装数</p>
        </div>
        <div class="stat-card">
            <h3>${stats.total_views || 0}</h3>
            <p>总查看数</p>
        </div>
        <div class="stat-card">
            <h3>${stats.active_distributions || 0}</h3>
            <p>活跃分发</p>
        </div>
    `;

    statsContainer.innerHTML = statsHtml;
}

// 显示热门应用
function displayPopularApps(apps) {
    const appsContainer = document.getElementById('popular-apps');

    if (apps.length === 0) {
        appsContainer.innerHTML = '<div class="alert alert-info">暂无热门应用</div>';
        return;
    }

    const appsHtml = apps.map((app, index) => {
        const createdAt = new Date(app.created_at).toLocaleString();

        return `
            <div class="popular-app" style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h4>#${index + 1} ${app.app_name}</h4>
                    <span class="badge badge-primary">${app.install_count} 次安装</span>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 14px;">
                    <div><strong>Bundle ID:</strong> ${app.bundle_id}</div>
                    <div><strong>版本:</strong> ${app.version}</div>
                    <div><strong>创建时间:</strong> ${createdAt}</div>
                </div>
            </div>
        `;
    }).join('');

    appsContainer.innerHTML = `<h3>热门应用</h3>${appsHtml}`;
}

// 更新showTab函数以支持新标签页
const originalShowTab = window.showTab;
window.showTab = function(tabName, event) {
    // 调用原始的showTab函数
    if (originalShowTab) {
        originalShowTab(tabName, event);
    }

    // 加载新标签页的数据
    switch (tabName) {
        case 'certificates':
            loadCertificatesData();
            break;
        case 'resign-records':
            loadResignRecords();
            break;
        case 'distribution-stats':
            loadDistributionStats();
            break;
    }
};