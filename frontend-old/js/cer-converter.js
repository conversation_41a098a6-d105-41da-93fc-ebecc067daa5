/**
 * CER转P12转换器JavaScript
 */

const API_BASE_URL = 'https://api.ios.xxyx.cn';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
    loadConversionGuide();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 检查用户登录状态
    if (!isLoggedIn()) {
        window.location.href = 'login.html';
        return;
    }
    
    console.log('CER转P12页面初始化完成');
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 文件上传区域事件
    setupFileUpload('cer-upload-area', 'cer-file', handleCerFileSelect);

    // 表单提交
    document.getElementById('cer-conversion-form').addEventListener('submit', handleFormSubmit);

    // 退出登录
    document.getElementById('logout-btn').addEventListener('click', logout);
}

/**
 * 设置文件上传区域
 */
function setupFileUpload(areaId, inputId, callback) {
    const uploadArea = document.getElementById(areaId);
    const fileInput = document.getElementById(inputId);
    
    // 点击上传区域
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // 文件选择
    fileInput.addEventListener('change', callback);
    
    // 拖拽上传
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            callback({ target: fileInput });
        }
    });
}

/**
 * 处理CER文件选择
 */
function handleCerFileSelect(event) {
    const file = event.target.files[0];
    const infoDiv = document.getElementById('cer-file-info');
    
    if (file) {
        // 验证文件类型
        if (!file.name.toLowerCase().endsWith('.cer')) {
            showError('请选择.cer格式的证书文件');
            return;
        }
        
        // 显示文件信息
        infoDiv.innerHTML = `
            <div class="file-selected">
                <span class="file-icon">📄</span>
                <span class="file-name">${file.name}</span>
                <span class="file-size">(${formatFileSize(file.size)})</span>
                <button type="button" class="remove-file" onclick="removeCerFile()">×</button>
            </div>
        `;
        infoDiv.style.display = 'block';
        
        // 自动填充证书名称
        const nameInput = document.getElementById('certificate-name');
        if (!nameInput.value) {
            const baseName = file.name.replace(/\.[^/.]+$/, "");
            nameInput.value = baseName;
        }
    }
}

/**
 * 移除CER文件
 */
function removeCerFile() {
    document.getElementById('cer-file').value = '';
    document.getElementById('cer-file-info').style.display = 'none';
}

/**
 * 处理表单提交
 */
async function handleFormSubmit(event) {
    event.preventDefault();
    
    const convertBtn = document.getElementById('convert-btn');
    const resultDiv = document.getElementById('conversion-result');
    
    // 验证表单
    if (!validateForm()) {
        return;
    }
    
    // 显示加载状态
    convertBtn.disabled = true;
    convertBtn.innerHTML = '🔄 转换中...';
    resultDiv.style.display = 'none';
    
    try {
        // 准备表单数据
        const formData = new FormData();
        const form = document.getElementById('cer-conversion-form');
        
        // 添加CER文件
        const cerFile = document.getElementById('cer-file').files[0];
        formData.append('cer_file', cerFile);

        // 添加转换参数
        formData.append('certificate_name', document.getElementById('certificate-name').value);
        formData.append('certificate_type', document.getElementById('certificate-type').value);
        formData.append('password', document.getElementById('p12-password').value);
        
        // 发送请求
        const response = await fetch(`${API_BASE_URL}/api/certificates/cer/convert`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${getToken()}`
            },
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showConversionSuccess(result);
        } else {
            if (result.allow_manual_selection && result.available_csrs) {
                showManualCsrSelection(result);
            } else {
                showConversionError(result.error, result.error_type, result.suggestion);
            }
        }
        
    } catch (error) {
        console.error('转换请求失败:', error);
        showConversionError('网络请求失败，请检查网络连接');
    } finally {
        // 恢复按钮状态
        convertBtn.disabled = false;
        convertBtn.innerHTML = '🔄 开始转换';
    }
}

/**
 * 验证表单
 */
function validateForm() {
    // 检查CER文件
    const cerFile = document.getElementById('cer-file').files[0];
    if (!cerFile) {
        showError('请选择CER证书文件');
        return false;
    }

    // 检查证书名称
    const certName = document.getElementById('certificate-name').value.trim();
    if (!certName) {
        showError('请输入证书名称');
        return false;
    }

    // 检查密码
    const password = document.getElementById('p12-password').value;
    if (!password || password.length < 6) {
        showError('P12密码长度至少6位');
        return false;
    }

    return true;
}

/**
 * 显示转换成功结果
 */
function showConversionSuccess(result) {
    const resultDiv = document.getElementById('conversion-result');
    const contentDiv = document.getElementById('result-content');
    
    contentDiv.innerHTML = `
        <div class="success-message">
            <h4>✅ 转换成功！</h4>
            <p>CER文件已成功转换为P12格式</p>
            <div class="download-section">
                <p><strong>证书名称:</strong> ${result.filename}</p>
                <p><strong>匹配的CSR:</strong> ${result.matched_csr}</p>
                <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                    <button onclick="downloadP12File('${result.certificate_id}', '${result.filename}')"
                            class="download-btn">
                        📥 立即下载
                    </button>
                    <a href="cer-records.html" class="download-btn" style="background: #28a745; text-decoration: none;">
                        📊 查看记录
                    </a>
                </div>
            </div>
            <div class="success-tips">
                <h5>📋 使用说明:</h5>
                <ul>
                    <li>系统已自动匹配对应的私钥并成功转换</li>
                    <li>下载的P12文件可以直接用于iOS应用签名</li>
                    <li>请妥善保管P12文件和密码</li>
                    <li>P12文件已自动添加到证书管理列表</li>
                </ul>
            </div>
        </div>
    `;
    
    resultDiv.className = 'conversion-result';
    resultDiv.style.display = 'block';
    
    // 清空表单
    document.getElementById('cer-conversion-form').reset();
    removeCerFile();
}

/**
 * 显示手动选择CSR界面
 */
function showManualCsrSelection(result) {
    const resultDiv = document.getElementById('conversion-result');
    const contentDiv = document.getElementById('result-content');

    let csrOptions = '';
    result.available_csrs.forEach(csr => {
        const createdDate = new Date(csr.created_at * 1000).toLocaleDateString();
        csrOptions += `
            <div class="csr-option" onclick="selectCsr('${csr.id}')">
                <div class="csr-info">
                    <h5>${csr.name}</h5>
                    <p><strong>Common Name:</strong> ${csr.common_name}</p>
                    <p><strong>组织:</strong> ${csr.organization || 'N/A'}</p>
                    <p><strong>创建时间:</strong> ${createdDate}</p>
                </div>
                <div class="csr-select-btn">选择此CSR</div>
            </div>
        `;
    });

    contentDiv.innerHTML = `
        <div class="manual-selection">
            <h4>🔍 智能匹配失败，请手动选择CSR</h4>
            <p>${result.error}</p>

            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;">
                <h5 style="margin: 0 0 10px 0; color: #856404;">💡 解决方案:</h5>
                <p style="margin: 0; color: #856404;">${result.suggestion}</p>
            </div>

            <div class="csr-selection-area">
                <h5>请选择对应的CSR记录:</h5>
                <div class="csr-options">
                    ${csrOptions}
                </div>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button onclick="retrySmartMatching()" class="btn" style="background: #6c757d; color: white; margin-right: 10px;">
                    🔄 重新智能匹配
                </button>
                <a href="csr.html" class="btn" style="background: #28a745; color: white; text-decoration: none;">
                    📋 生成新的CSR
                </a>
            </div>
        </div>

        <style>
            .csr-option {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .csr-option:hover {
                border-color: #667eea;
                background: #f8f9ff;
            }

            .csr-option.selected {
                border-color: #667eea;
                background: #e7f3ff;
            }

            .csr-info h5 {
                margin: 0 0 8px 0;
                color: #2c3e50;
            }

            .csr-info p {
                margin: 4px 0;
                color: #6c757d;
                font-size: 14px;
            }

            .csr-select-btn {
                background: #667eea;
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
            }

            .csr-option:hover .csr-select-btn {
                background: #5a6fd8;
            }
        </style>
    `;

    resultDiv.className = 'conversion-result';
    resultDiv.style.display = 'block';
}

/**
 * 选择CSR并重新转换
 */
function selectCsr(csrId) {
    // 标记选中的CSR
    document.querySelectorAll('.csr-option').forEach(option => {
        option.classList.remove('selected');
    });
    event.target.closest('.csr-option').classList.add('selected');

    // 确认选择
    if (confirm('确定使用此CSR进行转换吗？')) {
        performManualConversion(csrId);
    }
}

/**
 * 使用手动选择的CSR执行转换
 */
async function performManualConversion(csrId) {
    const convertBtn = document.getElementById('convert-btn');
    const resultDiv = document.getElementById('conversion-result');

    // 显示加载状态
    resultDiv.innerHTML = '<div style="text-align: center; padding: 40px;"><div style="color: #667eea;">🔄 正在使用选定的CSR进行转换...</div></div>';

    try {
        // 准备表单数据
        const formData = new FormData();
        const form = document.getElementById('cer-conversion-form');

        // 添加CER文件
        const cerFile = document.getElementById('cer-file').files[0];
        formData.append('cer_file', cerFile);

        // 添加转换参数
        formData.append('certificate_name', document.getElementById('certificate-name').value);
        formData.append('certificate_type', document.getElementById('certificate-type').value);
        formData.append('password', document.getElementById('p12-password').value);

        // 添加手动选择的CSR ID
        formData.append('manual_csr_id', csrId);

        // 发送请求
        const response = await fetch(`${API_BASE_URL}/api/certificates/cer/convert`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${getToken()}`
            },
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showConversionSuccess(result);
        } else {
            showConversionError(result.error, result.error_type, result.suggestion);
        }

    } catch (error) {
        console.error('手动转换请求失败:', error);
        showConversionError('网络请求失败，请检查网络连接');
    }
}

/**
 * 重新尝试智能匹配
 */
function retrySmartMatching() {
    const resultDiv = document.getElementById('conversion-result');
    resultDiv.style.display = 'none';

    // 重新提交表单进行智能匹配
    document.getElementById('cer-conversion-form').dispatchEvent(new Event('submit'));
}

/**
 * 显示转换错误
 */
function showConversionError(error, errorType, suggestion) {
    const resultDiv = document.getElementById('conversion-result');
    const contentDiv = document.getElementById('result-content');

    let errorContent = `
        <div class="error-message">
            <h4>❌ 转换失败</h4>
            <p>${error}</p>
    `;

    if (suggestion) {
        errorContent += `
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;">
                <h5 style="margin: 0 0 10px 0; color: #856404;">💡 建议解决方案:</h5>
                <p style="margin: 0; color: #856404;">${suggestion}</p>
            </div>
        `;
    }

    if (errorType === 'no_matching_key') {
        errorContent += `
            <div class="error-tips">
                <h5>🔄 推荐流程:</h5>
                <ol>
                    <li><a href="csr.html" style="color: #0066cc;">点击这里生成CSR</a> - 在本系统创建证书签名请求</li>
                    <li>使用生成的CSR文件到Apple Developer申请证书</li>
                    <li>下载CER文件并返回本页面进行转换</li>
                </ol>
            </div>
        `;
    } else {
        errorContent += `
            <div class="error-tips">
                <h5>💡 可能的解决方案:</h5>
                <ul>
                    <li>检查CER文件是否有效</li>
                    <li>确认CER文件是使用本系统CSR申请的</li>
                    <li>验证文件格式是否正确</li>
                    <li>如果问题持续，请联系管理员</li>
                </ul>
            </div>
        `;
    }

    errorContent += '</div>';
    contentDiv.innerHTML = errorContent;

    resultDiv.className = 'conversion-result error';
    resultDiv.style.display = 'block';
}

/**
 * 加载转换指南
 */
async function loadConversionGuide() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/certificates/cer/guide`);
        const result = await response.json();
        
        if (result.success) {
            displayConversionGuide(result.guide);
        }
    } catch (error) {
        console.error('加载转换指南失败:', error);
    }
}

/**
 * 显示转换指南
 */
function displayConversionGuide(guide) {
    const stepsContainer = document.getElementById('conversion-steps');
    
    let stepsHtml = '';
    guide.steps.forEach(step => {
        stepsHtml += `
            <div class="step-item">
                <div class="step-number">${step.step}</div>
                <div class="step-content">
                    <h4>${step.title}</h4>
                    <p>${step.description}</p>
                    <div class="step-note">${step.note}</div>
                </div>
            </div>
        `;
    });
    
    stepsContainer.innerHTML = stepsHtml;
}



/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 显示错误消息
 */
function showError(message) {
    alert('错误: ' + message);
}

/**
 * 下载P12文件
 */
async function downloadP12File(certificateId, filename) {
    try {
        const token = getToken();
        if (!token) {
            alert('请先登录');
            window.location.href = 'login.html';
            return;
        }

        const response = await fetch(`${API_BASE_URL}/api/certificates/cer/${certificateId}/download`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                alert('登录已过期，请重新登录');
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = 'login.html';
                return;
            }

            const errorData = await response.json();
            throw new Error(errorData.error || '下载失败');
        }

        // 获取文件内容
        const blob = await response.blob();

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;

        // 触发下载
        document.body.appendChild(a);
        a.click();

        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        console.log('P12文件下载成功');

    } catch (error) {
        console.error('下载P12文件失败:', error);
        alert('下载失败: ' + error.message);
    }
}

// 认证相关函数已内联到HTML中
