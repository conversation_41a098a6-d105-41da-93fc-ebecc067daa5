// CSR生成功能JavaScript

// API基础URL
const API_BASE_URL = 'https://api.ios.xxyx.cn';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 初始化CSR表单
    initCsrForm();
    
    // 加载CSR列表
    loadCsrList();
});

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = 'login.html';
        return;
    }
    
    // 验证token有效性
    fetch(`${API_BASE_URL}/api/auth/verify`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Token invalid');
        }
        return response.json();
    })
    .catch(error => {
        console.error('Auth check failed:', error);
        localStorage.removeItem('token');
        window.location.href = 'login.html';
    });
}

// 初始化CSR表单
function initCsrForm() {
    const form = document.getElementById('csr-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        generateCsr();
    });
}

// 生成CSR文件
function generateCsr() {
    const form = document.getElementById('csr-form');
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const alertDiv = document.getElementById('csr-alert');
    
    // 获取表单数据
    const csrData = {
        common_name: formData.get('common_name'),
        country: formData.get('country'),
        organization: formData.get('organization'),
        organizational_unit: formData.get('organizational_unit')
    };
    
    // 验证必填字段
    if (!csrData.common_name || !csrData.country || !csrData.organization) {
        showAlert(alertDiv, 'error', '请填写所有必填字段');
        return;
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(csrData.common_name)) {
        showAlert(alertDiv, 'error', 'Common Name必须是有效的邮箱地址');
        return;
    }
    
    // 禁用提交按钮
    submitBtn.disabled = true;
    submitBtn.innerHTML = '🔄 生成中...';
    
    const token = localStorage.getItem('token');
    
    fetch(`${API_BASE_URL}/api/certificates/csr/generate`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(csrData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(alertDiv, 'success', 'CSR文件生成成功！');
            form.reset();
            // 刷新CSR列表
            loadCsrList();
            
            // 自动下载CSR文件
            if (data.csr_content) {
                downloadCsrContent(data.csr_content, data.filename + '.csr');
            }
        } else {
            showAlert(alertDiv, 'error', data.error || 'CSR生成失败');
        }
    })
    .catch(error => {
        console.error('Generate CSR error:', error);
        showAlert(alertDiv, 'error', '网络错误，请重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '🔐 生成CSR文件';
    });
}

// 加载CSR列表
function loadCsrList() {
    const token = localStorage.getItem('token');
    const csrList = document.getElementById('csr-list');
    
    fetch(`${API_BASE_URL}/api/certificates/csr`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCsrList(data.csr_list);
        } else {
            csrList.innerHTML = '<div class="alert alert-error">加载CSR列表失败</div>';
        }
    })
    .catch(error => {
        console.error('Load CSR list error:', error);
        csrList.innerHTML = '<div class="alert alert-error">网络错误</div>';
    });
}

// 显示CSR列表
function displayCsrList(csrList) {
    const container = document.getElementById('csr-list');
    
    if (csrList.length === 0) {
        container.innerHTML = '<div class="alert alert-info">暂无CSR文件</div>';
        return;
    }
    
    const csrHtml = csrList.map(csr => {
        const createdAt = new Date(csr.created_at).toLocaleString();
        
        return `
            <div class="csr-item">
                <div class="csr-header">
                    <div class="csr-title">📋 ${csr.name}</div>
                </div>
                <div class="csr-info">
                    <div><strong>Common Name:</strong> ${csr.common_name}</div>
                    <div><strong>国家:</strong> ${csr.country}</div>
                    <div><strong>组织:</strong> ${csr.organization}</div>
                    <div><strong>组织单位:</strong> ${csr.organizational_unit || '无'}</div>
                    <div><strong>创建时间:</strong> ${createdAt}</div>
                    <div><strong>文件大小:</strong> ${formatFileSize(csr.file_size)}</div>
                </div>
                <div class="csr-actions">
                    <button class="btn btn-success" onclick="downloadCsr('${csr._id}')">
                        📥 下载CSR文件
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = csrHtml;
}

// 下载CSR文件
function downloadCsr(certificateId) {
    const token = localStorage.getItem('token');
    const url = `${API_BASE_URL}/api/certificates/csr/${certificateId}/download`;
    
    fetch(url, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('下载失败');
    })
    .then(blob => {
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `csr_${certificateId}.csr`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
    })
    .catch(error => {
        console.error('Download CSR error:', error);
        alert('下载失败，请重试');
    });
}

// 下载CSR内容（用于生成后立即下载）
function downloadCsrContent(content, filename) {
    const blob = new Blob([content], { type: 'application/x-pem-file' });
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 显示提示信息
function showAlert(container, type, message) {
    container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        window.location.href = 'login.html';
    }
}
