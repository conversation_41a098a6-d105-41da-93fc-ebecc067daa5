/**
 * CER转换记录页面JavaScript
 */

const API_BASE_URL = 'https://api.ios.xxyx.cn';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('CER转换记录页面初始化');
    
    // 检查登录状态
    if (!isLoggedIn()) {
        window.location.href = 'login.html';
        return;
    }

    // 设置退出登录按钮
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }

    // 加载记录
    loadRecords();
});

/**
 * 加载转换记录
 */
async function loadRecords() {
    try {
        const token = getToken();
        if (!token) {
            window.location.href = 'login.html';
            return;
        }

        // 显示加载状态
        const container = document.getElementById('records-container');
        container.innerHTML = '<div class="loading-state"><div>🔄 加载转换记录中...</div></div>';

        // 获取转换记录
        const response = await fetch(`${API_BASE_URL}/api/certificates/cer/records`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                alert('登录已过期，请重新登录');
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = 'login.html';
                return;
            }
            throw new Error('获取记录失败');
        }

        const data = await response.json();
        
        if (data.success) {
            displayRecords(data.records);
            displayStats(data.stats);
        } else {
            throw new Error(data.error || '获取记录失败');
        }

    } catch (error) {
        console.error('加载转换记录失败:', error);
        const container = document.getElementById('records-container');
        container.innerHTML = `
            <div class="error-state">
                <div>❌ 加载失败: ${error.message}</div>
                <button onclick="loadRecords()" class="refresh-btn" style="margin-top: 15px;">重试</button>
            </div>
        `;
    }
}

/**
 * 显示转换记录
 */
function displayRecords(records) {
    const container = document.getElementById('records-container');
    
    if (!records || records.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📄</div>
                <h4>暂无转换记录</h4>
                <p>您还没有进行过CER转P12转换</p>
                <a href="cer-converter.html" class="download-btn" style="margin-top: 15px;">
                    🔄 开始转换
                </a>
            </div>
        `;
        return;
    }

    const recordsHtml = records.map(record => {
        const createdAt = new Date(record.created_at * 1000).toLocaleString();
        const statusClass = record.status === 'completed' ? 'status-completed' : 'status-failed';
        const statusText = record.status === 'completed' ? '✅ 成功' : '❌ 失败';
        const methodText = record.conversion_method === 'auto' ? '🤖 智能匹配' : '👤 手动选择';
        
        return `
            <div class="record-item">
                <div class="record-header">
                    <div class="record-info">
                        <h4>${record.certificate_name}</h4>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <div class="record-actions">
                        ${record.status === 'completed' ? `
                            <button class="download-btn" onclick="downloadP12Record('${record.certificate_id}', '${record.target_file_name}')">
                                📥 下载P12
                            </button>
                        ` : ''}
                    </div>
                </div>
                
                <div class="record-meta">
                    <div class="meta-item">
                        <span class="meta-icon">📁</span>
                        <span>源文件: ${record.source_file_name}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">🔄</span>
                        <span>转换方式: ${methodText}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">📋</span>
                        <span>CSR: ${record.source_csr_name || 'N/A'}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">📊</span>
                        <span>文件大小: ${formatFileSize(record.file_size)}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">🕒</span>
                        <span>转换时间: ${createdAt}</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-icon">🏷️</span>
                        <span>证书类型: ${record.certificate_type === 'development' ? '开发证书' : '分发证书'}</span>
                    </div>
                </div>
                
                ${record.error_message ? `
                    <div class="error-message" style="margin-top: 15px; padding: 10px; background: #f8d7da; border-radius: 6px; color: #721c24;">
                        <strong>错误信息:</strong> ${record.error_message}
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');
    
    container.innerHTML = `<div class="records-list">${recordsHtml}</div>`;
}

/**
 * 显示统计信息
 */
function displayStats(stats) {
    if (!stats || stats.length === 0) {
        return;
    }

    const cerStats = stats.find(stat => stat.conversion_type === 'cer_to_p12') || {
        total_count: 0,
        success_count: 0,
        success_rate: 0,
        total_size: 0
    };

    document.getElementById('total-conversions').textContent = cerStats.total_count;
    document.getElementById('success-rate').textContent = cerStats.success_rate + '%';
    document.getElementById('total-size').textContent = formatFileSize(cerStats.total_size);
    
    // 计算最近7天的转换次数（这里简化处理，实际应该从后端获取）
    document.getElementById('recent-conversions').textContent = Math.min(cerStats.total_count, 5);
}

/**
 * 下载P12文件
 */
async function downloadP12Record(certificateId, filename) {
    try {
        const token = getToken();
        if (!token) {
            alert('请先登录');
            window.location.href = 'login.html';
            return;
        }

        // 显示下载状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '⏳ 下载中...';
        button.disabled = true;

        const response = await fetch(`${API_BASE_URL}/api/certificates/cer/${certificateId}/download`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            if (response.status === 401) {
                alert('登录已过期，请重新登录');
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = 'login.html';
                return;
            }
            
            const errorData = await response.json();
            throw new Error(errorData.error || '下载失败');
        }

        // 获取文件内容
        const blob = await response.blob();
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        
        // 触发下载
        document.body.appendChild(a);
        a.click();
        
        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        console.log('P12文件下载成功');
        
        // 恢复按钮状态
        button.innerHTML = originalText;
        button.disabled = false;
        
    } catch (error) {
        console.error('下载P12文件失败:', error);
        alert('下载失败: ' + error.message);
        
        // 恢复按钮状态
        const button = event.target;
        button.innerHTML = '📥 下载P12';
        button.disabled = false;
    }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
