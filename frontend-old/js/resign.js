// 重签功能JavaScript

// API基础URL
const API_BASE_URL = 'https://api.ios.xxyx.cn';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 初始化文件上传
    initFileUpload();
    
    // 初始化表单提交
    initResignForm();
    
    // 加载重签记录
    loadResignRecords();
    
    // 定期刷新记录状态
    setInterval(loadResignRecords, 10000); // 每10秒刷新一次
});

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = 'login.html';
        return;
    }
    
    // 验证token有效性
    fetch(`${API_BASE_URL}/api/auth/verify`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Token invalid');
        }
        return response.json();
    })
    .catch(error => {
        console.error('Auth check failed:', error);
        localStorage.removeItem('token');
        window.location.href = 'login.html';
    });
}

// 初始化文件上传
function initFileUpload() {
    const fileInput = document.getElementById('ipa-file');
    const fileLabel = fileInput.nextElementSibling;
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            fileLabel.classList.add('has-file');
            fileLabel.innerHTML = `
                <div>📱 ${file.name}</div>
                <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                    文件大小: ${formatFileSize(file.size)}
                </div>
            `;
        } else {
            fileLabel.classList.remove('has-file');
            fileLabel.innerHTML = `
                <div>📱 点击选择IPA文件或拖拽到此处</div>
                <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">支持最大2GB的IPA文件</div>
            `;
        }
    });
    
    // 拖拽上传
    fileLabel.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileLabel.style.borderColor = '#5a67d8';
        fileLabel.style.background = '#f0f2ff';
    });
    
    fileLabel.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileLabel.style.borderColor = '#667eea';
        fileLabel.style.background = '#f8f9ff';
    });
    
    fileLabel.addEventListener('drop', function(e) {
        e.preventDefault();
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
        fileLabel.style.borderColor = '#667eea';
        fileLabel.style.background = '#f8f9ff';
    });
}

// 初始化重签表单
function initResignForm() {
    const form = document.getElementById('resign-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        submitResignTask();
    });
}

// 提交重签任务
function submitResignTask() {
    const form = document.getElementById('resign-form');
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const alertDiv = document.getElementById('resign-alert');
    
    // 验证文件
    const ipaFile = formData.get('ipa');
    if (!ipaFile || ipaFile.size === 0) {
        showAlert(alertDiv, 'error', '请选择IPA文件');
        return;
    }
    
    // 禁用提交按钮
    submitBtn.disabled = true;
    submitBtn.innerHTML = '🔄 提交中...';
    
    const token = localStorage.getItem('token');
    
    fetch(`${API_BASE_URL}/api/resign/submit`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(alertDiv, 'success', '重签任务提交成功！正在处理中...');
            form.reset();
            // 重置文件上传显示
            const fileLabel = document.querySelector('.file-upload-label');
            fileLabel.classList.remove('has-file');
            fileLabel.innerHTML = `
                <div>📱 点击选择IPA文件或拖拽到此处</div>
                <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">支持最大2GB的IPA文件</div>
            `;
            // 刷新记录列表
            loadResignRecords();
        } else {
            showAlert(alertDiv, 'error', data.error || '提交失败');
        }
    })
    .catch(error => {
        console.error('Submit error:', error);
        showAlert(alertDiv, 'error', '网络错误，请重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '🚀 开始重签';
    });
}

// 加载重签记录
function loadResignRecords() {
    const token = localStorage.getItem('token');
    const recordsList = document.getElementById('records-list');
    
    fetch(`${API_BASE_URL}/api/resign/records`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayResignRecords(data.records);
        } else {
            recordsList.innerHTML = '<div class="alert alert-error">加载记录失败</div>';
        }
    })
    .catch(error => {
        console.error('Load records error:', error);
        recordsList.innerHTML = '<div class="alert alert-error">网络错误</div>';
    });
}

// 显示重签记录
function displayResignRecords(records) {
    const recordsList = document.getElementById('records-list');
    
    if (records.length === 0) {
        recordsList.innerHTML = '<div class="alert alert-info">暂无重签记录</div>';
        return;
    }
    
    const recordsHtml = records.map(record => {
        const statusClass = getStatusClass(record.status);
        const statusText = getStatusText(record.status);
        const createdAt = new Date(record.created_at).toLocaleString();
        
        let actionsHtml = '';
        if (record.status === 'success') {
            actionsHtml = `
                <button class="btn btn-success" onclick="downloadResignedIpa('${record._id}')">
                    📥 下载重签IPA
                </button>
            `;
            // 所有成功的重签都有分发链接
            if (record.install_url) {
                actionsHtml += `
                    <button class="btn btn-info" onclick="copyInstallLink('${record.install_url}')">
                        📱 复制安装链接
                    </button>
                    <button class="btn btn-info" onclick="viewQRCode('${record.install_url}')">
                        📱 查看二维码
                    </button>
                `;
            }
        } else if (record.status === 'failed') {
            actionsHtml = `
                <button class="btn btn-secondary" onclick="retryResignTask('${record._id}')">
                    🔄 重试
                </button>
            `;
        }
        
        return `
            <div class="record-item">
                <div class="record-header">
                    <div class="record-title">${record.original_filename}</div>
                    <div class="status-badge ${statusClass}">${statusText}</div>
                </div>
                <div class="record-info">
                    <div><strong>应用名称:</strong> ${record.app_name || '未知'}</div>
                    <div><strong>Bundle ID:</strong> ${record.bundle_id || '未知'}</div>
                    <div><strong>版本:</strong> ${record.version || '未知'}</div>
                    <div><strong>创建时间:</strong> ${createdAt}</div>
                    <div><strong>文件大小:</strong> ${formatFileSize(record.original_file_size)}</div>
                    ${record.error_message ? `<div style="color: #dc3545;"><strong>错误:</strong> ${record.error_message}</div>` : ''}
                </div>
                ${actionsHtml ? `<div class="record-actions">${actionsHtml}</div>` : ''}
            </div>
        `;
    }).join('');
    
    recordsList.innerHTML = recordsHtml;
}

// 下载重签后的IPA
function downloadResignedIpa(recordId) {
    const token = localStorage.getItem('token');
    const url = `${API_BASE_URL}/api/resign/download/${recordId}`;
    
    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = url;
    link.style.display = 'none';
    
    // 添加认证头部（通过URL参数，因为下载链接不能设置headers）
    fetch(url, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('下载失败');
    })
    .then(blob => {
        const downloadUrl = window.URL.createObjectURL(blob);
        link.href = downloadUrl;
        link.download = `resigned_${recordId}.ipa`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
    })
    .catch(error => {
        console.error('Download error:', error);
        alert('下载失败，请重试');
    });
}

// 重试重签任务
function retryResignTask(recordId) {
    const token = localStorage.getItem('token');
    
    if (!confirm('确定要重试这个重签任务吗？')) {
        return;
    }
    
    fetch(`${API_BASE_URL}/api/resign/retry/${recordId}`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('重试任务已提交');
            loadResignRecords();
        } else {
            alert(data.error || '重试失败');
        }
    })
    .catch(error => {
        console.error('Retry error:', error);
        alert('网络错误，请重试');
    });
}

// 复制安装链接
function copyInstallLink(installUrl) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(installUrl).then(() => {
            alert('安装链接已复制到剪贴板！\n\n可以通过Safari浏览器打开此链接安装应用。');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(installUrl);
        });
    } else {
        fallbackCopyTextToClipboard(installUrl);
    }
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            alert('安装链接已复制到剪贴板！\n\n可以通过Safari浏览器打开此链接安装应用。');
        } else {
            prompt('请手动复制以下链接:', text);
        }
    } catch (err) {
        console.error('复制失败:', err);
        prompt('请手动复制以下链接:', text);
    }

    document.body.removeChild(textArea);
}

// 查看二维码
function viewQRCode(installUrl) {
    // 创建模态框显示二维码
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        max-width: 400px;
        width: 90%;
    `;

    modalContent.innerHTML = `
        <h3 style="margin-bottom: 20px; color: #2c3e50;">📱 扫码安装应用</h3>
        <div id="qr-container" style="margin-bottom: 20px; display: flex; justify-content: center; align-items: center; min-height: 250px;">
            <div style="color: #6c757d;">正在生成二维码...</div>
        </div>
        <p style="color: #6c757d; font-size: 14px; margin-bottom: 20px;">
            使用iPhone的相机或Safari浏览器扫描二维码安装应用
        </p>
        <button onclick="this.closest('.modal').remove()" style="
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        ">关闭</button>
    `;

    modal.className = 'modal';
    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // 使用简单的二维码生成
    const qrContainer = modalContent.querySelector('#qr-container');
    if (typeof generateQRCode === 'function') {
        generateQRCode(qrContainer, installUrl);
    } else {
        qrContainer.innerHTML = `
            <div style="color: #e74c3c; padding: 20px; text-align: center;">
                <div style="margin-bottom: 15px;">
                    <i style="font-size: 48px;">📱</i>
                </div>
                <div style="margin-bottom: 10px; font-weight: bold;">
                    二维码功能不可用
                </div>
                <div style="margin-bottom: 15px; font-size: 14px; color: #6c757d;">
                    请直接复制安装链接使用
                </div>
                <button onclick="copyInstallLink('${installUrl}')" style="
                    background: #667eea;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                ">📋 复制安装链接</button>
            </div>
        `;
    }

    // 点击背景关闭模态框
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// 获取状态样式类
function getStatusClass(status) {
    const statusMap = {
        'pending': 'status-pending',
        'processing': 'status-processing',
        'success': 'status-success',
        'failed': 'status-failed'
    };
    return statusMap[status] || 'status-pending';
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'processing': '处理中',
        'success': '成功',
        'failed': '失败'
    };
    return statusMap[status] || '未知';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 显示提示信息
function showAlert(container, type, message) {
    container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        window.location.href = 'login.html';
    }
}
