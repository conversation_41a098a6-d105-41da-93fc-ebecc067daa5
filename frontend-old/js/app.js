// app.js - 前端功能逻辑

// 注意：基础函数已在index.html中定义，这里只包含具体功能

// GitHub账号选择在后端自动完成，前端不需要处理

// 文件选择处理已移至upload.js中的新上传流程

// 旧的表单验证和上传函数已被upload.js中的新流程替代



// 加载上传记录
async function loadRecords() {
    const recordsList = document.getElementById('records-list');
    const statusFilter = document.getElementById('status-filter').value;

    recordsList.innerHTML = '<div class="loading">加载中...</div>';

    try {
        let url = '/api/upload/records';
        const params = new URLSearchParams();

        if (statusFilter) {
            params.append('status', statusFilter);
        }

        if (isAdmin()) {
            params.append('all', '1');
        }

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await fetch(`${API_BASE}${url}`, {
            headers: {
                'Authorization': `Bearer ${getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.status === 401) {
            clearAuthAndRedirect();
            return;
        }

        if (!response.ok) throw new Error('获取上传记录失败');

        const data = await response.json();

        if (data.success && data.records && data.records.length > 0) {
            let html = '';
            data.records.forEach(record => {
                const createdAt = new Date(record.created_at.$date || record.created_at).toLocaleString();
                const statusClass = `status-${record.status}`;
                const statusText = getStatusText(record.status);

                // 处理错误信息显示
                let errorDisplay = '';
                // 检查多个可能的错误信息字段
                const errorInfo = record.error_details || record.upload_result || null;
                if (record.status === 'failed' && errorInfo) {
                    const error = errorInfo;
                    const errorTitle = error.title || '上传失败';
                    const errorMessage = error.message || '未知错误';
                    const errorSolution = error.solution || '请检查配置';

                    errorDisplay = `
                        <div class="record-error">
                            <div class="error-summary">
                                <strong>❌ ${errorTitle}:</strong> ${errorMessage}
                            </div>
                            <div class="error-solution">
                                <strong>💡 解决方案:</strong> ${errorSolution}
                            </div>
                            ${error.details ? `
                                <details class="error-details">
                                    <summary>🔍 查看详细信息</summary>
                                    <pre>${error.details}</pre>
                                </details>
                            ` : ''}
                            ${error.raw_error ? `
                                <details class="error-raw">
                                    <summary>🔍 Apple 完整错误信息</summary>
                                    <pre>${error.raw_error}</pre>
                                </details>
                            ` : ''}
                        </div>
                    `;
                } else if (record.status === 'failed') {
                    // 简单错误信息 - 从多个可能的字段中获取
                    const simpleError = record.error ||
                                      (record.upload_result && record.upload_result.message) ||
                                      (record.error_details && record.error_details.message) ||
                                      '上传失败';
                    errorDisplay = `
                        <div class="record-error">
                            <div class="error-summary">
                                <strong>❌ 上传失败:</strong> ${simpleError}
                            </div>
                        </div>
                    `;
                }

                html += `
                    <div class="record-item">
                        <div class="record-header">
                            <div class="record-title">${record.app_name || '未知应用'}</div>
                            <div class="record-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="record-details">
                            <div><strong>Bundle ID:</strong> ${record.bundle_id || 'N/A'}</div>
                            <div><strong>版本:</strong> ${record.version || 'N/A'}</div>
                            <div><strong>上传时间:</strong> ${createdAt}</div>
                            ${isAdmin() ? `<div><strong>用户:</strong> ${record.username || record.user_id || 'N/A'}</div>` : ''}
                            ${record.description ? `<div><strong>说明:</strong> ${record.description}</div>` : ''}
                            ${record.workflow_ip ? `<div><strong>🌐 执行IP:</strong> <code style="background: #f3f4f6; padding: 2px 6px; border-radius: 3px;">${record.workflow_ip}</code></div>` : ''}
                            ${record.workflow_duration ? `<div><strong>⏱️ 执行时长:</strong> <code style="background: #f3f4f6; padding: 2px 6px; border-radius: 3px;">${formatDuration(record.workflow_duration)}</code></div>` : ''}
                            ${errorDisplay}
                        </div>
                    </div>
                `;
            });
            recordsList.innerHTML = html;
        } else {
            recordsList.innerHTML = '<div class="loading">暂无上传记录</div>';
        }
    } catch (error) {
        console.error('加载记录失败:', error);
        recordsList.innerHTML = `<div class="loading" style="color: #dc3545;">加载失败: ${error.message}</div>`;
    }
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
    };
    return statusMap[status] || status;
}

// 格式化时长显示
function formatDuration(seconds) {
    if (!seconds || seconds === 0) return '0秒';

    if (seconds < 60) {
        return seconds + '秒';
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return minutes + '分' + (remainingSeconds > 0 ? remainingSeconds + '秒' : '');
    } else {
        const hours = Math.floor(seconds / 3600);
        const remainingMinutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        let result = hours + '小时';
        if (remainingMinutes > 0) {
            result += remainingMinutes + '分';
        }
        if (remainingSeconds > 0) {
            result += remainingSeconds + '秒';
        }
        return result;
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始加载记录列表
    loadRecords();
});