<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XIOS - iOS应用管理工具</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/upload.css">
</head>

<body>
    <!-- 头部 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">📱 XIOS</a>
                <div class="user-info">
                    <span id="user-info">加载中...</span>
                    <button class="logout-btn" onclick="logout()">退出登录</button>
                </div>
            </div>
        </div>
    </header>

    <!-- 标签页导航 -->
    <nav class="tabs">
        <div class="container">
            <div class="tabs-container">
                <button class="tab active" id="tab-upload" onclick="showTab('upload')">📤 上传中心</button>
                <button class="tab" id="tab-records" onclick="showTab('records')">📋 上传记录</button>
                <a href="resign.html" class="tab">🔐 重签工具</a>
                <a href="csr.html" class="tab">📋 CSR生成</a>
                <a href="cer-converter.html" class="tab">🔄 CER转P12</a>
                <button class="tab" id="tab-admin" onclick="showTab('admin')" style="display:none;">⚙️ 管理后台</button>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="content">
        <!-- 上传中心 -->
        <section id="upload" class="section active">
            <div class="card">
                <h2>📱 IPA文件上传</h2>

                <!-- 步骤1: 选择认证方式 -->
                <div class="upload-step" id="step-auth">
                    <h3>🔐 选择认证方式</h3>
                    <div class="auth-methods">
                        <div class="auth-method">
                            <input type="radio" id="auth-api-key" name="auth-method" value="api_key" checked>
                            <label for="auth-api-key" class="auth-label">
                                <div class="auth-icon">🔑</div>
                                <div class="auth-info">
                                    <h4>API Key 认证</h4>
                                    <p>使用App Store Connect API Key（推荐）</p>
                                </div>
                            </label>
                        </div>
                        <div class="auth-method">
                            <input type="radio" id="auth-apple-id" name="auth-method" value="apple_id">
                            <label for="auth-apple-id" class="auth-label">
                                <div class="auth-icon">🍎</div>
                                <div class="auth-info">
                                    <h4>Apple ID 认证</h4>
                                    <p>使用Apple ID和专属密码</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- API Key 配置 -->
                    <div id="api-key-config" class="auth-config">
                        <div class="form-group">
                            <label for="api-key-id">API Key ID:</label>
                            <input type="text" id="api-key-id" class="form-control" placeholder="10位字符，如：ABC123DEFG">
                            <small class="form-text">在App Store Connect中创建的API Key ID</small>
                        </div>
                        <div class="form-group">
                            <label for="issuer-id">Issuer ID:</label>
                            <input type="text" id="issuer-id" class="form-control"
                                placeholder="UUID格式，如：12345678-1234-1234-1234-123456789012">
                            <small class="form-text">您的开发者账号Issuer ID</small>
                        </div>
                        <div class="form-group">
                            <label for="api-key-content">API Key 私钥内容:</label>
                            <textarea id="api-key-content" class="form-control" rows="8" placeholder="-----BEGIN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg...
-----END PRIVATE KEY-----"></textarea>
                            <small class="form-text">完整的.p8私钥文件内容</small>
                        </div>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="validateAPIKey()">验证API
                            Key</button>
                        <div id="api-key-validation-result"></div>
                    </div>

                    <!-- Apple ID 配置 -->
                    <div id="apple-id-config" class="auth-config" style="display:none;">
                        <div class="form-group">
                            <label for="apple-id">Apple ID:</label>
                            <input type="email" id="apple-id" class="form-control"
                                placeholder="<EMAIL>">
                            <small class="form-text">您的Apple ID邮箱地址</small>
                        </div>
                        <div class="form-group">
                            <label for="app-password">专属密码:</label>
                            <input type="text" id="app-password" class="form-control" placeholder="xxxx-xxxx-xxxx-xxxx">
                            <small class="form-text">在Apple ID管理页面生成的专属密码</small>
                        </div>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="validateAppleID()">验证Apple
                            ID</button>
                        <div id="apple-id-validation-result"></div>
                    </div>
                </div>

                <!-- 步骤2: 上传IPA文件 -->
                <div class="upload-step" id="step-file">
                    <h3>📱 上传IPA文件</h3>
                    <div class="file-upload-area" id="file-upload-area">
                        <input type="file" id="ipa-file" accept=".ipa" style="display:none;">
                        <div class="upload-placeholder" onclick="document.getElementById('ipa-file').click()">
                            <div class="upload-icon">📱</div>
                            <div class="upload-text">
                                <p><strong>点击选择IPA文件</strong></p>
                                <p>或拖拽文件到此处</p>
                                <small>支持.ipa格式，最大500MB</small>
                            </div>
                        </div>
                    </div>

                    <!-- IPA信息展示 -->
                    <div id="ipa-info" class="ipa-info" style="display:none;">
                        <h4>📋 应用信息</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>应用图标:</label>
                                <div id="app-icon" class="app-icon"></div>
                            </div>
                            <div class="info-item">
                                <label>应用名称:</label>
                                <span id="app-name">-</span>
                            </div>
                            <div class="info-item">
                                <label>Bundle ID:</label>
                                <span id="bundle-id">-</span>
                            </div>
                            <div class="info-item">
                                <label>版本号:</label>
                                <span id="app-version">-</span>
                            </div>
                            <div class="info-item">
                                <label>构建号:</label>
                                <span id="build-number">-</span>
                            </div>
                            <div class="info-item">
                                <label>文件大小:</label>
                                <span id="file-size">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 步骤3: 发布说明 -->
                <div class="upload-step" id="step-notes">
                    <h3>📝 发布说明（可选）</h3>
                    <div class="form-group">
                        <textarea id="release-notes" class="form-control" rows="4"
                            placeholder="输入本次更新的发布说明..."></textarea>
                        <small class="form-text">发布说明将显示在TestFlight中</small>
                    </div>
                </div>

                <!-- 上传按钮 -->
                <div class="upload-actions">
                    <button type="button" id="upload-btn" class="btn btn-primary btn-block" onclick="startUpload()"
                        disabled>
                        <span id="upload-text">🚀 开始上传到TestFlight</span>
                    </button>
                </div>

                <!-- 上传进度 -->
                <div class="upload-progress" id="upload-progress" style="display:none;">
                    <div class="progress-header">
                        <h4>📤 上传进度</h4>
                        <div style="display:flex;align-items:center;gap:8px;">
                            <span id="progress-percentage">0%</span>
                            <span id="auto-refresh-indicator" class="auto-refresh-indicator">🔄 自动刷新中</span>
                        </div>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" id="progress-fill"></div>
                    </div>
                    <div class="status-text" id="status-text">准备开始上传...</div>
                </div>

                <!-- 上传结果 -->
                <div class="upload-result" id="upload-result" style="display:none;">
                    <div class="result-content" id="result-content"></div>
                </div>
            </div>
        </section>

        <!-- 上传记录 -->
        <section id="records" class="section">
            <div class="card">
                <h2>📋 上传记录</h2>
                <div class="toolbar">
                    <div class="toolbar-left">
                        <select id="status-filter" class="form-control">
                            <option value="">全部状态</option>
                            <option value="pending">等待中</option>
                            <option value="processing">处理中</option>
                            <option value="completed">已完成</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-secondary" onclick="loadRecords()">🔄 刷新列表</button>
                    </div>
                </div>
                <div class="records-list" id="records-list">
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </section>

        <!-- 管理后台 -->
        <section id="admin" class="section">
            <div class="card">
                <div id="admin-content" style="width:100%;height:800px;border:none;border-radius:6px;">
                    <div style="text-align:center;padding:50px;color:#666;">
                        <h3>管理后台</h3>
                        <p>正在加载...</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script>
        // API基础地址
        const API_BASE = 'https://api.ios.xxyx.cn';

        // 基础函数
        function getToken() {
            return localStorage.getItem('token');
        }

        function getUser() {
            try {
                return JSON.parse(localStorage.getItem('user'));
            } catch {
                return null;
            }
        }

        function clearAuthAndRedirect() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '/login.html';
        }

        function isAdmin() {
            const user = getUser();
            return user && user.role === 'admin';
        }

        // 显示标签页
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            const targetSection = document.getElementById(tabName);
            const targetTab = document.getElementById(`tab-${tabName}`);

            if (targetSection) {
                targetSection.classList.add('active');
            }
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 根据标签页加载相应内容
            if (tabName === 'records') {
                loadRecords();
            }
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                clearAuthAndRedirect();
            }
        }

        // 加载管理后台界面（仅管理员）
        function loadAdminInterface() {
            const adminContent = document.getElementById('admin-content');
            if (adminContent) {
                adminContent.innerHTML = '<iframe src="admin.html" style="width:100%;height:800px;border:none;border-radius:6px;"></iframe>';
            }
        }

        // 权限判断与页面渲染
        document.addEventListener('DOMContentLoaded', async function () {
            const user = getUser();
            const token = localStorage.getItem('token');

            // 如果没有用户信息或token，直接跳转登录
            if (!user || !token) {
                window.location.href = '/login.html';
                return;
            }

            // 验证token是否有效
            try {
                const response = await fetch(`${API_BASE}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    window.location.href = '/login.html';
                    return;
                }

                const result = await response.json();

                if (!result.success) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    window.location.href = '/login.html';
                    return;
                }

                // token有效，更新用户信息（可能有变化）
                localStorage.setItem('user', JSON.stringify(result.user));

                // 渲染页面
                const userInfoElement = document.getElementById('user-info');
                const adminTabElement = document.getElementById('tab-admin');

                // 检查用户状态并显示相应信息
                let userStatusText = '';
                if (result.user.role === 'admin') {
                    userStatusText = '管理员';
                } else {
                    if (result.user.is_expired) {
                        userStatusText = '普通用户（已过期）';
                    } else {
                        userStatusText = '普通用户';
                    }
                }

                if (userInfoElement) {
                    userInfoElement.textContent = `欢迎，${result.user.username}（${userStatusText}）`;
                }

                if (adminTabElement) {
                    if (result.user.role === 'admin') {
                        adminTabElement.style.display = 'block';
                        // 为管理员加载管理后台iframe
                        loadAdminInterface();
                    } else {
                        adminTabElement.style.display = 'none';
                    }
                }

                // 默认显示上传中心
                showTab('upload');

            } catch (error) {
                // 网络错误或其他异常，清除本地存储并跳转登录
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                window.location.href = '/login.html';
            }
        });
    </script>
    <script src="js/app.js"></script>
    <script src="js/upload.js"></script>
</body>

</html>