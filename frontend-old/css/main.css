/* XIOS - 主样式文件 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
    text-decoration: none;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #666;
}

.logout-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.logout-btn:hover {
    background: #c82333;
}

/* 标签页样式 */
.tabs {
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
}

.tabs-container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.tab {
    padding: 15px 25px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.tab:hover {
    color: #007bff;
    background: #f8f9fa;
}

.tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9fa;
}

/* 内容区域 */
.content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

/* 卡片样式 */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.card h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-control:disabled {
    background: #f8f9fa;
    color: #6c757d;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s;
    line-height: 1;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #1e7e34;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-block {
    width: 100%;
}

/* 状态样式 */
.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-processing {
    background: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar {
    height: 100%;
    background: #007bff;
    width: 0%;
    transition: width 0.3s;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* 列表样式 */
.list-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    transition: box-shadow 0.3s;
}

.list-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.list-item-title {
    font-weight: 500;
    color: #333;
}

.list-item-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    font-size: 14px;
    color: #666;
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading::before {
    content: "⏳";
    margin-right: 8px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .tabs-container {
        flex-direction: column;
    }
    
    .tab {
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        border-right: none;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .list-item-details {
        grid-template-columns: 1fr;
    }
}

/* 提示框动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 上传记录样式 */
.record-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #e5e7eb;
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.record-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.record-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.record-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    font-size: 14px;
    color: #6b7280;
}

/* 上传记录错误信息样式 */
.record-error {
    margin-top: 15px;
    padding: 12px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    grid-column: 1 / -1; /* 占满整行 */
}

.record-error .error-summary {
    color: #dc2626;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
}

.record-error .error-solution {
    color: #059669;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
}

.record-error details {
    margin-top: 8px;
}

.record-error summary {
    cursor: pointer;
    color: #6b7280;
    font-size: 13px;
    padding: 4px 0;
    user-select: none;
    font-weight: 500;
}

.record-error summary:hover {
    color: #374151;
}

.record-error pre {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 8px;
    font-size: 11px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 200px;
    overflow-y: auto;
    margin: 8px 0 0 0;
    font-family: 'Courier New', monospace;
    line-height: 1.4;
}

/* 自动刷新指示器 */
.auto-refresh-indicator {
    font-size: 12px;
    color: #6b7280;
    opacity: 0.8;
    animation: pulse 2s infinite;
}

.auto-refresh-indicator.hidden {
    display: none;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.4; }
}

/* GitHub使用统计样式 */
.stats-summary {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-summary h4 {
    margin-bottom: 16px;
    color: #374151;
    font-size: 18px;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.stat-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 16px;
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
}

.usage-table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.usage-table table {
    width: 100%;
    border-collapse: collapse;
}

.usage-table th,
.usage-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.usage-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.usage-table td {
    font-size: 14px;
    color: #4b5563;
}

.usage-table tr:hover {
    background: #f9fafb;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-active {
    background: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background: #fee2e2;
    color: #991b1b;
}

/* GitHub账号使用统计样式 */
.usage-stats {
    font-size: 13px;
    line-height: 1.4;
}

.usage-stats div {
    margin-bottom: 4px;
}

.usage-stats div:last-child {
    margin-bottom: 0;
}

.usage-stats strong {
    color: #374151;
    font-weight: 600;
}

.usage-stats small {
    color: #6b7280;
    font-size: 12px;
}

/* 用户状态样式 */
.status.active {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
    font-weight: 600;
}

.status.expired {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
    font-weight: 600;
    animation: pulse-red 2s infinite;
}

.status.inactive {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
    font-weight: 500;
}

/* 过期状态闪烁动画 */
@keyframes pulse-red {
    0%, 100% {
        background: #fef2f2;
        border-color: #fecaca;
    }
    50% {
        background: #fee2e2;
        border-color: #fca5a5;
    }
}

/* 表单帮助文本样式 */
.form-help {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #6b7280;
    line-height: 1.4;
}

/* GitHub账号使用统计样式 */
.usage-stats {
    font-size: 13px;
    line-height: 1.4;
}

.usage-stats div {
    margin-bottom: 4px;
}

.usage-stats div:last-child {
    margin-bottom: 0;
}

.usage-stats strong {
    color: #374151;
    font-weight: 600;
}

.usage-stats small {
    color: #6b7280;
    font-size: 12px;
}
