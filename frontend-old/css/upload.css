/* 上传中心专用样式 */

/* 上传步骤 */
.upload-step {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
}

.upload-step h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
}

/* 认证方式选择 */
.auth-methods {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.auth-method {
    position: relative;
}

.auth-method input[type="radio"] {
    display: none;
}

.auth-label {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.auth-label:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.auth-method input[type="radio"]:checked + .auth-label {
    border-color: #007bff;
    background: #e7f3ff;
}

.auth-icon {
    font-size: 24px;
    margin-right: 15px;
}

.auth-info h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #333;
}

.auth-info p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

/* 认证配置 */
.auth-config {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
}

.form-text {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

/* 文件上传区域 */
.file-upload-area {
    margin-bottom: 20px;
}

.upload-placeholder {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.upload-placeholder:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.upload-placeholder.dragover {
    border-color: #007bff;
    background: #e7f3ff;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.upload-text p {
    margin: 5px 0;
    font-size: 16px;
}

.upload-text strong {
    color: #333;
}

.upload-text small {
    color: #666;
    font-size: 14px;
}

/* IPA信息展示 */
.ipa-info {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
}

.ipa-info h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item label {
    font-weight: bold;
    color: #555;
    margin-bottom: 5px;
    font-size: 14px;
}

.info-item span {
    color: #333;
    font-size: 14px;
}

.app-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.app-icon img {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    object-fit: cover;
}

/* 上传进度 */
.upload-progress {
    margin-top: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-header h4 {
    margin: 0;
    color: #333;
}

#progress-percentage {
    font-weight: bold;
    color: #007bff;
}

.progress {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    width: 0%;
    transition: width 0.3s ease;
}

/* 进度步骤 */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 100px;
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    font-size: 16px;
}

.step-item.active .step-icon {
    background: #007bff;
    color: white;
}

.step-item.completed .step-icon {
    background: #28a745;
    color: white;
}

.step-item.error .step-icon {
    background: #dc3545;
    color: white;
}

.step-text {
    font-size: 12px;
    text-align: center;
    color: #666;
}

.step-item.active .step-text {
    color: #007bff;
    font-weight: bold;
}

.status-text {
    text-align: center;
    color: #666;
    font-size: 14px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

/* 上传结果 */
.upload-result {
    margin-top: 30px;
}

.result-content {
    padding: 20px;
    border-radius: 8px;
}

.result-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.result-error {
    background: #fee2e2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 20px;
    color: #991b1b;
}

.error-header {
    margin-bottom: 16px;
}

.error-header h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
}

.error-step {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

.error-content {
    margin-bottom: 20px;
}

.error-message,
.error-solution,
.error-details {
    margin-bottom: 12px;
    padding: 12px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.7);
}

.error-message {
    border-left: 3px solid #ef4444;
}

.error-solution {
    border-left: 3px solid #10b981;
    background: #f0fdf4;
    color: #166534;
}

.error-details {
    border-left: 3px solid #6b7280;
    background: #f9fafb;
    color: #374151;
}

.error-details pre {
    margin: 8px 0 0 0;
    white-space: pre-wrap;
    word-break: break-word;
}

.result-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-methods {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .progress-steps {
        flex-direction: column;
        align-items: center;
    }
    
    .step-item {
        flex-direction: row;
        justify-content: flex-start;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .step-icon {
        margin-right: 10px;
        margin-bottom: 0;
    }
    
    .step-text {
        text-align: left;
    }
}

/* 验证结果样式 */
#api-key-validation-result,
#apple-id-validation-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
}

#api-key-validation-result small,
#apple-id-validation-result small {
    display: block;
    line-height: 1.4;
}

/* 动画效果 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.step-item.active .step-icon {
    animation: pulse 2s infinite;
}

/* 文件拖拽效果 */
.file-upload-area.dragover .upload-placeholder {
    border-color: #007bff;
    background: #e7f3ff;
    transform: scale(1.02);
}
