<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲闲iOS工具 - CER转P12</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 导航栏样式 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px 30px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .nav-brand h1 {
            color: #667eea;
            font-size: 24px;
            font-weight: 700;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-link {
            color: #333;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .nav-link.active {
            background: #667eea;
            color: white;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .btn-primary {
            background: #667eea;
            color: white;
            font-size: 16px;
            padding: 12px 30px;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        /* 页面布局 */
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .page-header h2 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .page-header p {
            color: #6c757d;
            font-size: 16px;
            margin-bottom: 20px;
        }

        .guide-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .guide-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 20px;
        }

        /* 步骤指南样式 */
        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
            font-size: 16px;
        }

        .step-content h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 16px;
        }

        .step-content p {
            margin: 0 0 10px 0;
            color: #6c757d;
            line-height: 1.5;
        }

        .step-note {
            font-size: 14px;
            color: #856404;
            background: #fff3cd;
            padding: 10px 15px;
            border-radius: 8px;
            border-left: 3px solid #ffc107;
            margin-top: 10px;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 25px;
        }

        .form-group.full-width {
            width: 100%;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .text-muted {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        /* 文件上传样式 */
        .file-upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: #fafbfc;
        }

        .file-upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
            transform: translateY(-2px);
        }

        .file-upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
            border-style: solid;
        }

        .upload-icon {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 15px;
        }

        .file-upload-area p {
            margin: 5px 0;
            color: #6c757d;
        }

        .file-upload-area p:first-of-type {
            color: #2c3e50;
            font-weight: 500;
            font-size: 16px;
        }

        .file-info {
            margin-top: 15px;
        }

        .file-selected {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            color: #0066cc;
        }

        .file-icon {
            font-size: 20px;
        }

        .file-name {
            flex: 1;
            font-weight: 500;
        }

        .file-size {
            color: #6c757d;
            font-size: 12px;
        }

        .remove-file {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .remove-file:hover {
            background: #c82333;
        }

        /* 结果和提示样式 */
        .requirements-list {
            background: #e7f3ff;
            border-radius: 12px;
            padding: 25px;
            margin-top: 25px;
            border-left: 4px solid #0066cc;
        }

        .requirements-list h4 {
            margin: 0 0 15px 0;
            color: #0066cc;
            font-size: 16px;
        }

        .requirements-list ul {
            margin: 0;
            padding-left: 20px;
        }

        .requirements-list li {
            margin-bottom: 10px;
            color: #495057;
            line-height: 1.5;
        }

        .conversion-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 12px;
            padding: 25px;
            margin-top: 25px;
            display: none;
        }

        .conversion-result.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .success-message h4,
        .error-message h4 {
            margin-bottom: 15px;
            font-size: 18px;
        }

        .download-section {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 20px 0;
        }

        .download-btn {
            background: #28a745;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .success-tips,
        .error-tips {
            margin-top: 20px;
        }

        .success-tips h5,
        .error-tips h5 {
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .success-tips ul,
        .error-tips ul,
        .error-tips ol {
            margin: 0;
            padding-left: 20px;
        }

        .success-tips li,
        .error-tips li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .form-actions {
            text-align: center;
            margin-top: 30px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .navbar {
                padding: 15px 20px;
            }

            .nav-menu {
                flex-wrap: wrap;
                gap: 10px;
            }

            .page-header,
            .guide-section,
            .form-section {
                padding: 20px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .file-upload-area {
                padding: 30px 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-brand">
                <h1>🍎 闲闲iOS工具</h1>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">📱 上传IPA</a>
                <a href="resign.html" class="nav-link">🔐 IPA重签</a>
                <a href="csr.html" class="nav-link">📋 CSR生成</a>
                <a href="cer-converter.html" class="nav-link active">🔄 CER转P12</a>
                <a href="cer-records.html" class="nav-link">📊 转换记录</a>
                <a href="admin.html" class="nav-link">⚙️ 管理后台</a>
                <button id="logout-btn" class="btn btn-outline">退出登录</button>
            </div>
        </nav>

        <!-- 页面标题 -->
        <div class="page-header">
            <h2>🔄 CER转P12智能转换器</h2>
            <p>自动检测并转换使用本系统CSR申请的CER证书文件为P12格式</p>
            <div
                style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin-top: 15px; border-left: 4px solid #0066cc;">
                <strong>💡 重要提示：</strong>只有使用本系统生成的CSR申请的证书才能转换。如果您还没有生成CSR，请先访问
                <a href="csr.html" style="color: #0066cc;">CSR生成页面</a> 创建证书签名请求。
            </div>
        </div>

        <!-- 转换指南 -->
        <div class="guide-section">
            <h3>📖 转换指南</h3>
            <div id="conversion-steps">
                <!-- 步骤将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 转换表单 -->
        <div class="form-section">
            <h3>📁 文件上传与转换</h3>

            <form id="cer-conversion-form" enctype="multipart/form-data">
                <!-- CER文件上传 -->
                <div class="form-group full-width">
                    <label for="cer-file">CER证书文件 *</label>
                    <div class="file-upload-area" id="cer-upload-area">
                        <div class="upload-icon">📄</div>
                        <p>点击选择或拖拽CER文件到此处</p>
                        <p class="text-muted">支持.cer格式文件</p>
                        <input type="file" id="cer-file" name="cer_file" accept=".cer" style="display: none;">
                    </div>
                    <div id="cer-file-info" class="file-info" style="display: none;"></div>
                </div>

                <!-- 智能检测说明 -->
                <div class="form-group full-width">
                    <div
                        style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                        <h4 style="margin: 0 0 10px 0; color: #28a745;">🤖 智能私钥检测</h4>
                        <p style="margin: 0; color: #6c757d;">
                            系统会自动检测您上传的CER文件是否由本系统生成的CSR申请，如果匹配成功，将自动使用对应的私钥进行转换。
                            <strong>无需手动上传私钥文件。</strong>
                        </p>
                    </div>
                </div>

                <!-- 转换参数 -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="certificate-name">证书名称 *</label>
                        <input type="text" id="certificate-name" name="certificate_name" class="form-control"
                            placeholder="例如：iOS Distribution Certificate" required>
                    </div>
                    <div class="form-group">
                        <label for="certificate-type">证书类型</label>
                        <select id="certificate-type" name="certificate_type" class="form-control">
                            <option value="distribution">分发证书</option>
                            <option value="development">开发证书</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="p12-password">P12密码 *</label>
                    <input type="password" id="p12-password" name="password" class="form-control"
                        placeholder="设置P12文件的保护密码（至少6位）" required minlength="6">
                    <small class="text-muted">此密码用于保护导出的P12文件，请妥善保管</small>
                </div>

                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="submit" id="convert-btn" class="btn btn-primary">
                        🔄 开始转换
                    </button>
                </div>
            </form>

            <!-- 转换结果 -->
            <div id="conversion-result" class="conversion-result">
                <div id="result-content"></div>
            </div>

            <!-- 要求和提示 -->
            <div class="requirements-list">
                <h4>📋 转换要求</h4>
                <ul>
                    <li>CER文件必须是从Apple Developer下载的有效证书</li>
                    <li>私钥文件必须与CER证书对应（同一个CSR生成的）</li>
                    <li>P12密码长度至少6位，建议使用强密码</li>
                    <li>支持的证书类型：开发证书、分发证书</li>
                </ul>

                <h4 style="margin-top: 20px;">💡 使用提示</h4>
                <ul>
                    <li>只有使用本系统生成的CSR申请的证书才能转换</li>
                    <li>系统会自动匹配证书的Common Name和组织信息</li>
                    <li>转换后的P12文件包含完整的证书链和私钥</li>
                    <li>如果转换失败，请先使用CSR生成功能创建证书请求</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 加载JavaScript -->
    <script src="js/auth.js"></script>
    <script src="js/cer-converter.js"></script>
</body>

</html>