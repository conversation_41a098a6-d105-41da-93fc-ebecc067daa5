<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲闲iOS工具 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .tabs {
            display: flex;
            background: #fff;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 14px;
            font-weight: 500;
        }

        .tab.active {
            background: #007bff;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        .content {
            background: #fff;
            padding: 20px;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            min-height: 400px;
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status.active {
            background: #d4edda;
            color: #155724;
        }

        .status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        /* 新功能样式 */
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 600;
            border-radius: 12px;
            text-transform: uppercase;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-info {
            background: #cce7ff;
            color: #004085;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-primary {
            background: #cce7ff;
            color: #004085;
        }

        .badge-secondary {
            background: #e2e3e5;
            color: #383d41;
        }

        .badge-p12 {
            background: #e7f3ff;
            color: #0066cc;
        }

        .badge-mobileprovision {
            background: #fff2e7;
            color: #cc6600;
        }

        .badge-csr {
            background: #f0e7ff;
            color: #6600cc;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card h3 {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 8px;
        }

        .stat-card p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }

        #distribution-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .usage-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }

        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.3s ease;
        }

        .usage-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            margin: 2px;
        }

        .account-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-indicator.idle {
            background: #28a745;
        }

        .status-indicator.in-use {
            background: #ffc107;
        }

        .status-indicator.warning {
            background: #fd7e14;
        }

        .status-indicator.danger {
            background: #dc3545;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>闲闲iOS工具 - 管理后台</h1>
            <p>系统管理和监控</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('stats', event)">系统统计</button>
            <button class="tab" onclick="showTab('users', event)">用户管理</button>
            <button class="tab" onclick="showTab('activation-codes', event)">激活码管理</button>
            <button class="tab" onclick="showTab('github-accounts', event)">GitHub账号</button>
            <button class="tab" onclick="showTab('certificates', event)">证书管理</button>
            <button class="tab" onclick="showTab('resign-records', event)">重签记录</button>
            <button class="tab" onclick="showTab('distribution-stats', event)">分发统计</button>
        </div>

        <div class="content">
            <!-- 系统统计 -->
            <div id="stats" class="section active">
                <h2>系统统计</h2>
                <div id="stats-grid" class="stats-grid">
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 用户管理 -->
            <div id="users" class="section">
                <h2>用户管理</h2>
                <div id="user-alert"></div>

                <form id="create-user-form">
                    <h3>创建新用户</h3>
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" name="username" required>
                    </div>
                    <div class="form-group">
                        <label>邮箱</label>
                        <input type="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label>角色</label>
                        <select name="role">
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">创建用户</button>
                </form>

                <div id="users-list">
                    <h3>用户列表</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 激活码管理 -->
            <div id="activation-codes" class="section">
                <h2>激活码管理</h2>
                <div id="activation-alert"></div>

                <form id="create-activation-form">
                    <h3>创建激活码</h3>
                    <div class="form-group">
                        <label>有效期（天）</label>
                        <input type="number" name="expires_in_days" value="30" min="1" max="365" placeholder="输入天数">
                        <small class="form-help">激活码的有效期，单位为天</small>
                    </div>
                    <button type="submit" class="btn btn-primary">生成激活码</button>
                </form>

                <div id="activation-codes-list">
                    <h3>激活码列表</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- GitHub账号管理 -->
            <div id="github-accounts" class="section">
                <h2>GitHub账号管理</h2>
                <div id="github-alert"></div>

                <!-- 账号统计 -->
                <div id="github-stats" class="stats-grid" style="margin-bottom: 30px;">
                    <div class="loading">加载统计中...</div>
                </div>

                <!-- 批量操作 -->
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-success" onclick="refreshAllAccounts()">🔄 刷新所有账号状态</button>
                    <button class="btn btn-danger" onclick="resetAllUsage()">🔄 重置所有使用时长</button>
                </div>

                <form id="add-github-form">
                    <h3>添加GitHub账号</h3>
                    <div class="form-group">
                        <label>GitHub用户名</label>
                        <input type="text" name="username" required>
                    </div>
                    <div class="form-group">
                        <label>邮箱</label>
                        <input type="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label>Personal Access Token</label>
                        <textarea name="token" placeholder="请输入GitHub Personal Access Token" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">添加账号</button>
                </form>

                <div id="github-accounts-list">
                    <h3>GitHub账号列表</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 证书管理 -->
            <div id="certificates" class="section">
                <h2>证书管理</h2>
                <div id="certificate-alert"></div>

                <!-- 上传P12证书 -->
                <form id="upload-p12-form" enctype="multipart/form-data">
                    <h3>上传P12证书</h3>
                    <div class="form-group">
                        <label>证书名称</label>
                        <input type="text" name="name" required placeholder="请输入证书名称">
                    </div>
                    <div class="form-group">
                        <label>证书文件</label>
                        <input type="file" name="certificate" accept=".p12,.pfx" required>
                    </div>
                    <div class="form-group">
                        <label>证书密码</label>
                        <input type="password" name="password" required placeholder="请输入证书密码">
                    </div>
                    <div class="form-group">
                        <label>证书类型</label>
                        <select name="certificate_type">
                            <option value="distribution">发布证书</option>
                            <option value="development">开发证书</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">上传P12证书</button>
                </form>

                <!-- 上传mobileprovision文件 -->
                <form id="upload-provision-form" enctype="multipart/form-data" style="margin-top: 30px;">
                    <h3>上传mobileprovision文件</h3>
                    <div class="form-group">
                        <label>mobileprovision文件</label>
                        <input type="file" name="provision" accept=".mobileprovision" required>
                    </div>
                    <button type="submit" class="btn btn-primary">上传mobileprovision</button>
                </form>

                <!-- 证书列表 -->
                <div id="certificates-list" style="margin-top: 30px;">
                    <h3>证书列表</h3>
                    <div class="loading">加载中...</div>
                </div>

                <!-- 可用证书对 -->
                <div id="certificate-pairs" style="margin-top: 30px;">
                    <h3>可用证书对</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 重签记录 -->
            <div id="resign-records" class="section">
                <h2>重签记录管理</h2>
                <div id="resign-alert"></div>

                <div id="resign-records-list">
                    <h3>重签记录列表</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>

            <!-- 分发统计 -->
            <div id="distribution-stats" class="section">
                <h2>分发统计</h2>
                <div id="distribution-alert"></div>

                <!-- 统计概览 -->
                <div id="distribution-stats-grid" class="stats-grid" style="margin-bottom: 30px;">
                    <div class="loading">加载统计中...</div>
                </div>

                <!-- 热门应用 -->
                <div id="popular-apps">
                    <h3>热门应用</h3>
                    <div class="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
</body>

</html>