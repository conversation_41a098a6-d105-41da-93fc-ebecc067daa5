<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闲闲iOS工具 - CSR生成</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }

        .nav {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
        }

        .nav a {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav a:hover,
        .nav a.active {
            background: #fff;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }

        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .info-box h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .info-box p {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .csr-item {
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .csr-item:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .csr-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .csr-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .csr-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #6c757d;
        }

        .csr-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .card {
                padding: 20px;
            }

            .nav {
                flex-direction: column;
                align-items: center;
            }

            .csr-info {
                grid-template-columns: 1fr;
            }

            .csr-actions {
                justify-content: center;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>📋 CSR生成工具</h1>
            <p>生成证书签名请求文件，用于申请苹果开发者证书</p>
        </div>

        <div class="nav">
            <a href="index.html">📱 上传IPA</a>
            <a href="resign.html">🔐 重签工具</a>
            <a href="csr.html" class="active">📋 CSR生成</a>
            <a href="cer-converter.html">🔄 CER转P12</a>
            <a href="#" onclick="logout()">🚪 退出</a>
        </div>

        <!-- CSR生成说明 -->
        <div class="card">
            <div class="info-box">
                <h3>📖 什么是CSR文件？</h3>
                <p>CSR（Certificate Signing Request）是证书签名请求文件，用于向苹果申请开发者证书。</p>
                <p>生成CSR文件后，您可以在苹果开发者中心上传此文件来获取证书。</p>
                <p>⚠️ 请妥善保管生成的私钥文件，证书安装时需要对应的私钥。</p>
            </div>
        </div>

        <!-- CSR生成表单 -->
        <div class="card">
            <h2>🔐 生成CSR文件</h2>
            <div id="csr-alert"></div>

            <form id="csr-form">
                <div class="form-group">
                    <label>Common Name *</label>
                    <input type="email" name="common_name" required placeholder="<EMAIL>">
                    <small style="color: #6c757d;">通常使用您的邮箱地址</small>
                </div>

                <div class="form-group">
                    <label>国家代码 *</label>
                    <select name="country" required>
                        <option value="">请选择国家</option>
                        <option value="CN">中国 (CN)</option>
                        <option value="US">美国 (US)</option>
                        <option value="HK">香港 (HK)</option>
                        <option value="TW">台湾 (TW)</option>
                        <option value="JP">日本 (JP)</option>
                        <option value="KR">韩国 (KR)</option>
                        <option value="SG">新加坡 (SG)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>组织名称 *</label>
                    <input type="text" name="organization" required placeholder="Your Company Name">
                    <small style="color: #6c757d;">您的公司或组织名称</small>
                </div>

                <div class="form-group">
                    <label>组织单位（可选）</label>
                    <input type="text" name="organizational_unit" placeholder="IT Department">
                    <small style="color: #6c757d;">部门名称，可以留空</small>
                </div>

                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    🔐 生成CSR文件
                </button>
            </form>
        </div>

        <!-- CSR文件列表 -->
        <div class="card">
            <h2>📋 我的CSR文件</h2>
            <div id="csr-list">
                <div class="loading">加载中...</div>
            </div>
        </div>
    </div>

    <script src="js/csr.js"></script>
</body>

</html>