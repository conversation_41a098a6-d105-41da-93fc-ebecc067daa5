<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CER转换记录 - 闲闲iOS工具</title>
    <link rel="stylesheet" href="css/main.css">
    <style>
        .records-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
        }

        .page-header h2 {
            margin: 0 0 10px 0;
            font-size: 28px;
        }

        .page-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .records-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .section-title {
            font-size: 20px;
            color: #2c3e50;
            margin: 0;
        }

        .refresh-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .records-list {
            display: grid;
            gap: 15px;
        }

        .record-item {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .record-item:hover {
            border-color: #667eea;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .record-info h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
            font-size: 16px;
        }

        .record-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #6c757d;
        }

        .meta-icon {
            font-size: 16px;
        }

        .record-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .error-state {
            text-align: center;
            padding: 40px;
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .records-container {
                padding: 10px;
            }

            .page-header {
                padding: 30px 20px;
            }

            .record-header {
                flex-direction: column;
                gap: 10px;
            }

            .record-meta {
                grid-template-columns: 1fr;
            }

            .record-actions {
                justify-content: center;
            }
        }
    </style>
</head>

<body>
    <div class="records-container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-brand">
                <h1>🍎 闲闲iOS工具</h1>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">📱 上传IPA</a>
                <a href="resign.html" class="nav-link">🔐 IPA重签</a>
                <a href="csr.html" class="nav-link">📋 CSR生成</a>
                <a href="cer-converter.html" class="nav-link">🔄 CER转P12</a>
                <a href="cer-records.html" class="nav-link active">📊 转换记录</a>
                <a href="admin.html" class="nav-link">⚙️ 管理后台</a>
                <button id="logout-btn" class="btn btn-outline">退出登录</button>
            </div>
        </nav>

        <!-- 页面标题 -->
        <div class="page-header">
            <h2>📊 CER转P12转换记录</h2>
            <p>查看和管理您的证书转换历史记录</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section" id="stats-section">
            <div class="stat-card">
                <div class="stat-number" id="total-conversions">-</div>
                <div class="stat-label">总转换次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="success-rate">-</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-size">-</div>
                <div class="stat-label">总文件大小</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="recent-conversions">-</div>
                <div class="stat-label">最近7天</div>
            </div>
        </div>

        <!-- 转换记录 -->
        <div class="records-section">
            <div class="section-header">
                <h3 class="section-title">转换记录</h3>
                <button class="refresh-btn" onclick="loadRecords()">
                    🔄 刷新
                </button>
            </div>
            
            <div id="records-container">
                <div class="loading-state">
                    <div>🔄 加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载JavaScript -->
    <script src="js/auth.js"></script>
    <script src="js/cer-records.js"></script>
</body>

</html>
