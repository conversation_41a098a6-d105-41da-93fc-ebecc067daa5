<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - XIOS</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #7f8c8d;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #2c3e50;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert .btn {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            color: white;
            text-decoration: none;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .alert .btn:hover {
            opacity: 0.9;
        }

        .loading {
            display: none;
            text-align: center;
            color: #7f8c8d;
            margin-top: 10px;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-header">
            <h1>📱 XIOS</h1>
            <p>iOS应用管理工具</p>
        </div>
        <div id="alert"></div>
        <form id="login-form">
            <div class="form-group">
                <label>用户名</label>
                <input type="text" name="username" required>
            </div>
            <div class="form-group">
                <label>密码</label>
                <input type="password" name="password" required>
            </div>
            <button type="submit" class="btn" id="login-btn">登录</button>
        </form>
        <div class="loading" id="loading">登录中...</div>
    </div>
    <script>
        const API_BASE = 'https://api.ios.xxyx.cn';
        function showAlert(message, type = 'error') {
            const alertDiv = document.getElementById('alert');
            alertDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            setTimeout(() => {
                alertDiv.innerHTML = '';
            }, 5000);
        }

        function showActivationAlert(username, errorMessage = '') {
            const alertDiv = document.getElementById('alert');

            // 统一处理未激活和过期用户
            const isExpired = errorMessage.includes('过期') || errorMessage.includes('激活码');
            const title = isExpired ? '账号已过期' : '账号未激活';
            const description = '请使用激活码激活您的账号。';

            alertDiv.innerHTML = `
                <div class="alert alert-warning">
                    <div style="margin-bottom: 15px;">
                        <strong>${title}</strong><br>
                        ${description}
                        ${errorMessage ? '<br><small style="color: #666; font-size: 12px;">详情: ' + errorMessage + '</small>' : ''}
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="goToActivation('${username}')" class="btn" style="background: #28a745; padding: 8px 16px; font-size: 14px;">
                            🔑 去激活账号
                        </button>
                        <button onclick="clearAlert()" class="btn" style="background: #6c757d; padding: 8px 16px; font-size: 14px;">
                            取消
                        </button>
                    </div>
                </div>
            `;
        }

        function goToActivation(username) {
            // 将用户名保存到sessionStorage，方便激活页面使用
            if (username) {
                sessionStorage.setItem('activation_username', username);
            }
            window.location.href = '/activate.html';
        }

        function clearAlert() {
            document.getElementById('alert').innerHTML = '';
        }
        function setLoading(loading) {
            const btn = document.getElementById('login-btn');
            const loadingDiv = document.getElementById('loading');
            if (loading) {
                btn.disabled = true;
                btn.textContent = '登录中...';
                loadingDiv.style.display = 'block';
            } else {
                btn.disabled = false;
                btn.textContent = '登录';
                loadingDiv.style.display = 'none';
            }
        }
        async function login(username, password) {
            const response = await fetch(`${API_BASE}/api/auth/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password })
            });
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || '登录失败');
            }
            return response.json();
        }
        document.getElementById('login-form').addEventListener('submit', async function (e) {
            e.preventDefault();
            const formData = new FormData(this);
            const username = formData.get('username');
            const password = formData.get('password');
            if (!username || !password) {
                showAlert('请填写用户名和密码');
                return;
            }
            setLoading(true);
            try {
                const result = await login(username, password);
                localStorage.setItem('token', result.token);
                localStorage.setItem('user', JSON.stringify(result.user));
                showAlert('登录成功，正在跳转...', 'success');
                setTimeout(() => {
                    window.location.href = '/index.html';
                }, 1000);
            } catch (error) {
                // 检查是否是账号未激活或过期错误
                if (error.message.includes('账号未激活') || error.message.includes('未激活') ||
                    error.message.includes('过期') || error.message.includes('激活码')) {
                    showActivationAlert(username, error.message);
                } else {
                    showAlert(error.message);
                }
            } finally {
                setLoading(false);
            }
        });
        document.addEventListener('DOMContentLoaded', function () {
            const token = localStorage.getItem('token');
            if (token) {
                fetch(`${API_BASE}/api/auth/verify`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                }).then(response => {
                    if (response.ok) {
                        window.location.href = '/index.html';
                    } else {
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                }).catch(() => {
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                });
            }
        });
    </script>
</body>

</html>