#!/usr/bin/env php
<?php

/**
 * 创建超级管理员脚本
 * 用于创建指定的超级管理员账户
 * 
 * 使用方法：
 * php scripts/create_super_admin.php
 */

// 设置脚本路径
$scriptDir = dirname(__FILE__);
$rootDir = dirname($scriptDir);

// 加载自动加载器
require_once $rootDir . '/vendor/autoload.php';

use App\Models\User;
use App\Database\MongoDB;

try {
    echo "===========================================\n";
    echo "🔧 XIOS 超级管理员创建脚本\n";
    echo "===========================================\n\n";

    // 初始化数据库连接
    echo "📡 连接数据库...\n";
    $db = MongoDB::getInstance();
    echo "✅ 数据库连接成功\n\n";

    $userModel = new User();

    // 超级管理员信息
    $superAdminData = [
        'username' => 'zhmbo',
        'email' => '<EMAIL>',
        'password' => '123456',
        'role' => 'super_admin',
        'status' => 'active'
    ];

    echo "👤 创建超级管理员账户...\n";
    echo "用户名: {$superAdminData['username']}\n";
    echo "邮箱: {$superAdminData['email']}\n";
    echo "角色: 超级管理员\n\n";

    // 检查用户名是否已存在
    $existingUser = $userModel->findByUsername($superAdminData['username']);
    if ($existingUser) {
        echo "⚠️  用户名 '{$superAdminData['username']}' 已存在\n";
        
        // 如果已经是超级管理员，跳过
        if ($existingUser['role'] === 'super_admin') {
            echo "✅ 该用户已经是超级管理员，无需重复创建\n";
            exit(0);
        }
        
        // 询问是否更新为超级管理员
        echo "是否将现有用户升级为超级管理员？(y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 'y') {
            echo "❌ 操作已取消\n";
            exit(1);
        }
        
        // 更新现有用户为超级管理员
        $collection = $db->getCollection('users');
        $result = $collection->updateOne(
            ['_id' => new \MongoDB\BSON\ObjectId($existingUser['_id'])],
            [
                '$set' => [
                    'role' => 'super_admin',
                    'status' => 'active',
                    'updated_at' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        
        if ($result->getModifiedCount() > 0) {
            echo "✅ 用户 '{$superAdminData['username']}' 已升级为超级管理员！\n";
        } else {
            echo "❌ 升级失败\n";
            exit(1);
        }
    } else {
        // 检查邮箱是否已存在
        $existingEmail = $userModel->findByEmail($superAdminData['email']);
        if ($existingEmail) {
            echo "❌ 邮箱 '{$superAdminData['email']}' 已被使用\n";
            exit(1);
        }

        // 创建新的超级管理员账户
        $userId = $userModel->create($superAdminData);

        if ($userId) {
            echo "✅ 超级管理员账户创建成功！\n";
            echo "用户ID: {$userId}\n";
        } else {
            echo "❌ 超级管理员账户创建失败\n";
            exit(1);
        }
    }

    echo "\n===========================================\n";
    echo "🎉 超级管理员配置完成！\n";
    echo "===========================================\n\n";
    echo "📋 账户信息：\n";
    echo "- 用户名: {$superAdminData['username']}\n";
    echo "- 邮箱: {$superAdminData['email']}\n";
    echo "- 密码: {$superAdminData['password']}\n";
    echo "- 角色: 超级管理员\n";
    echo "- 状态: 已激活\n\n";
    echo "⚠️  重要提示：\n";
    echo "1. 请立即登录并修改默认密码\n";
    echo "2. 超级管理员拥有系统最高权限，请妥善保管账户信息\n";
    echo "3. 可以删除其他管理员和所有用户（除了自己）\n\n";

} catch (Exception $e) {
    echo "❌ 创建超级管理员时出错: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
