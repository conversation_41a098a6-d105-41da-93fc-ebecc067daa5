<?php
/**
 * 调试脚本 - 检查数据库中的图标数据
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Database\MongoDB;
use App\Config\AppConfig;

try {
    // 初始化配置
    $config = AppConfig::getInstance();
    
    // 连接数据库
    $db = MongoDB::getInstance();
    
    echo "=== 检查上传记录中的图标数据 ===\n";
    $uploadCollection = $db->getCollection('upload_records');
    $uploadRecords = $uploadCollection->find(['icon_base64' => ['$exists' => true, '$ne' => null]], ['limit' => 5]);
    
    foreach ($uploadRecords as $record) {
        $iconData = $record['icon_base64'] ?? null;
        echo "上传记录 ID: " . $record['_id'] . "\n";
        echo "应用名称: " . ($record['app_name'] ?? 'Unknown') . "\n";
        echo "图标数据长度: " . (is_string($iconData) ? strlen($iconData) : 'null') . "\n";
        
        if (is_string($iconData)) {
            $iconData = trim($iconData);
            echo "是否包含data URL前缀: " . (strpos($iconData, 'data:image/') === 0 ? 'Yes' : 'No') . "\n";
            echo "前50个字符: " . substr($iconData, 0, 50) . "...\n";
            
            // 验证base64格式
            if (strpos($iconData, 'data:image/') === 0) {
                $base64Part = substr($iconData, strpos($iconData, ',') + 1);
            } else {
                $base64Part = $iconData;
            }
            
            $isValidBase64 = preg_match('/^[A-Za-z0-9+\/]*={0,2}$/', $base64Part);
            echo "Base64格式有效: " . ($isValidBase64 ? 'Yes' : 'No') . "\n";
        }
        echo "---\n";
    }
    
    echo "\n=== 检查重签记录中的图标数据 ===\n";
    $resignCollection = $db->getCollection('resign_records');
    $resignRecords = $resignCollection->find(['icon_base64' => ['$exists' => true, '$ne' => null]], ['limit' => 5]);
    
    foreach ($resignRecords as $record) {
        $iconData = $record['icon_base64'] ?? null;
        echo "重签记录 ID: " . $record['_id'] . "\n";
        echo "应用名称: " . ($record['app_name'] ?? 'Unknown') . "\n";
        echo "图标数据长度: " . (is_string($iconData) ? strlen($iconData) : 'null') . "\n";
        
        if (is_string($iconData)) {
            $iconData = trim($iconData);
            echo "是否包含data URL前缀: " . (strpos($iconData, 'data:image/') === 0 ? 'Yes' : 'No') . "\n";
            echo "前50个字符: " . substr($iconData, 0, 50) . "...\n";
            
            // 验证base64格式
            if (strpos($iconData, 'data:image/') === 0) {
                $base64Part = substr($iconData, strpos($iconData, ',') + 1);
            } else {
                $base64Part = $iconData;
            }
            
            $isValidBase64 = preg_match('/^[A-Za-z0-9+\/]*={0,2}$/', $base64Part);
            echo "Base64格式有效: " . ($isValidBase64 ? 'Yes' : 'No') . "\n";
        }
        echo "---\n";
    }
    
    echo "\n=== 检查分发记录中的图标数据 ===\n";
    $distributionCollection = $db->getCollection('distribution_records');
    $distributionRecords = $distributionCollection->find(['icon_base64' => ['$exists' => true, '$ne' => null]], ['limit' => 5]);
    
    foreach ($distributionRecords as $record) {
        $iconData = $record['icon_base64'] ?? null;
        echo "分发记录 ID: " . $record['_id'] . "\n";
        echo "应用名称: " . ($record['app_name'] ?? 'Unknown') . "\n";
        echo "图标数据长度: " . (is_string($iconData) ? strlen($iconData) : 'null') . "\n";
        
        if (is_string($iconData)) {
            $iconData = trim($iconData);
            echo "是否包含data URL前缀: " . (strpos($iconData, 'data:image/') === 0 ? 'Yes' : 'No') . "\n";
            echo "前50个字符: " . substr($iconData, 0, 50) . "...\n";
            
            // 验证base64格式
            if (strpos($iconData, 'data:image/') === 0) {
                $base64Part = substr($iconData, strpos($iconData, ',') + 1);
            } else {
                $base64Part = $iconData;
            }
            
            $isValidBase64 = preg_match('/^[A-Za-z0-9+\/]*={0,2}$/', $base64Part);
            echo "Base64格式有效: " . ($isValidBase64 ? 'Yes' : 'No') . "\n";
        }
        echo "---\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
