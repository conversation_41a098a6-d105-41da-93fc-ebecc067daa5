<?php

namespace App\Config;

use Dotenv\Dotenv;

class AppConfig
{
    private static ?AppConfig $instance = null;
    private array $config = [];

    private function __construct()
    {
        $this->loadEnvironmentVariables();
        $this->setDefaults();
    }

    public static function getInstance(): AppConfig
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadEnvironmentVariables(): void
    {
        $dotenv = Dotenv::createImmutable(__DIR__ . '/../../');
        $dotenv->load();
    }

    private function setDefaults(): void
    {
        $this->config = [
            // 数据库配置
            'mongodb_uri' => $_ENV['MONGODB_URI'] ?? 'mongodb://localhost:27017/xios',
            'redis_uri' => $_ENV['REDIS_URI'] ?? 'redis://localhost:6379',

            // GitHub配置
            'github_api_url' => $_ENV['GITHUB_API_URL'] ?? 'https://api.github.com',

            // 文件存储路径
            'storage_path' => $_ENV['STORAGE_PATH'] ?? '/data/storage',
            'upload_tmp_path' => $_ENV['UPLOAD_TMP_PATH'] ?? '/data/storage/uploads/upload_tmp',
            'ipa_storage_path' => $_ENV['IPA_STORAGE_PATH'] ?? '/data/storage/uploads/ipa',
            'icon_storage_path' => $_ENV['ICON_STORAGE_PATH'] ?? '/data/storage/uploads/icons',

            // 并发控制
            'max_concurrent_uploads' => (int)($_ENV['MAX_CONCURRENT_UPLOADS'] ?? 3),
            'max_queue_size' => (int)($_ENV['MAX_QUEUE_SIZE'] ?? 100),

            // 安全配置
            'jwt_secret' => $_ENV['JWT_SECRET'] ?? 'default_jwt_secret',
            'encryption_key' => $_ENV['ENCRYPTION_KEY'] ?? 'default_encryption_key',

            // 应用配置
            'app_env' => $_ENV['APP_ENV'] ?? 'development',
            'app_debug' => filter_var($_ENV['APP_DEBUG'] ?? 'true', FILTER_VALIDATE_BOOLEAN),
            'app_url' => $_ENV['APP_URL'] ?? 'https://ios.xxyx.cn',
            'api_url' => $_ENV['API_URL'] ?? 'https://api.ios.xxyx.cn',
            'download_url' => $_ENV['DOWNLOAD_URL'] ?? 'https://download.ios.xxyx.cn',

            // 日志配置
            'log_level' => $_ENV['LOG_LEVEL'] ?? 'debug',
            'log_path' => $_ENV['LOG_PATH'] ?? '/data/logs/uploadipa',

            // 文件上传配置
            'max_file_size' => (int)($_ENV['MAX_FILE_SIZE'] ?? 2147483648), // 2GB
            'allowed_extensions' => explode(',', $_ENV['ALLOWED_EXTENSIONS'] ?? 'ipa'),

            // 存储路径配置
            'app_storage_path' => $_ENV['APP_STORAGE_PATH'] ?? '/data/storage/',
            'ipa_storage_path' => $_ENV['IPA_STORAGE_PATH'] ?? '/data/storage/uploadipa',

        ];
    }

    public function get(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }

    public function getAll(): array
    {
        return $this->config;
    }

    public function isDevelopment(): bool
    {
        return $this->config['app_env'] === 'development';
    }

    public function isDebug(): bool
    {
        return $this->config['app_debug'];
    }
}
