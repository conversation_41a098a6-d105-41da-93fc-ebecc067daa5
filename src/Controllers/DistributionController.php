<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Services\DistributionService;
use App\Models\DistributionRecord;
use App\Models\ResignRecord;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class DistributionController
{
    private AuthService $authService;
    private DistributionService $distributionService;
    private DistributionRecord $distributionModel;
    private ResignRecord $resignRecordModel;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->distributionService = new DistributionService();
        $this->distributionModel = new DistributionRecord();
        $this->resignRecordModel = new ResignRecord();
    }

    /**
     * 创建分发链接
     */
    public function createDistribution(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $data = json_decode($request->getContent(), true);
        if (!$data || !isset($data['resign_record_id'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少重签记录ID'
            ], 400);
        }

        $resignRecordId = $data['resign_record_id'];
        $resignRecord = $this->resignRecordModel->findById($resignRecordId);

        if (!$resignRecord) {
            return new JsonResponse([
                'success' => false,
                'error' => '重签记录不存在'
            ], 404);
        }

        // 检查权限
        if ($user['role'] !== 'admin' && $resignRecord['user_id'] !== $user['_id']) {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $distributionConfig = [
            'title' => $data['title'] ?? null,
            'subtitle' => $data['subtitle'] ?? null,
            'description' => $data['description'] ?? null,
            'minimum_os_version' => $data['minimum_os_version'] ?? '10.0',
            'expires_in_days' => $data['expires_in_days'] ?? null
        ];

        $result = $this->distributionService->createDistribution($resignRecordId, $distributionConfig);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 安装页面（显示安装按钮和应用信息）
     */
    public function installPage(Request $request): Response
    {
        $distributionId = $request->attributes->get('distributionId');
        $distribution = $this->distributionService->getDistribution($distributionId);

        if (!$distribution || $distribution['status'] !== 'active') {
            return new Response('分发链接不存在或已失效', 404);
        }

        // 记录查看统计
        $this->distributionService->recordView($distributionId);

        // 检查是否过期
        if ($distribution['expires_at'] && strtotime($distribution['expires_at']) < time()) {
            return new Response('分发链接已过期', 410);
        }

        // 生成安装页面HTML
        $html = $this->generateInstallPageHtml($distribution);

        return new Response($html, 200, [
            'Content-Type' => 'text/html; charset=utf-8'
        ]);
    }

    /**
     * 获取plist文件
     */
    public function getPlist(Request $request): Response
    {
        $distributionId = $request->attributes->get('distributionId');
        $distribution = $this->distributionService->getDistribution($distributionId);

        if (!$distribution || $distribution['status'] !== 'active') {
            return new JsonResponse(['error' => '分发链接不存在或已失效'], 404);
        }

        // 检查是否过期
        if ($distribution['expires_at'] && strtotime($distribution['expires_at']) < time()) {
            return new JsonResponse(['error' => '分发链接已过期'], 410);
        }

        $plistResult = $this->distributionService->getPlistContent($distributionId);

        if (!$plistResult['success']) {
            return new JsonResponse(['error' => $plistResult['error']], 404);
        }

        return new Response($plistResult['content'], 200, [
            'Content-Type' => 'application/x-plist',
            'Content-Disposition' => 'attachment; filename="manifest.plist"'
        ]);
    }

    /**
     * 下载IPA文件
     */
    public function downloadIpa(Request $request): Response
    {
        $distributionId = $request->attributes->get('distributionId');
        $distribution = $this->distributionService->getDistribution($distributionId);

        if (!$distribution || $distribution['status'] !== 'active') {
            return new JsonResponse(['error' => '分发链接不存在或已失效'], 404);
        }

        // 检查是否过期
        if ($distribution['expires_at'] && strtotime($distribution['expires_at']) < time()) {
            return new JsonResponse(['error' => '分发链接已过期'], 410);
        }

        // 获取重签记录
        $resignRecord = $this->resignRecordModel->findById($distribution['resign_record_id']);
        if (!$resignRecord || !$resignRecord['resigned_ipa_path']) {
            return new JsonResponse(['error' => 'IPA文件不存在'], 404);
        }

        if (!file_exists($resignRecord['resigned_ipa_path'])) {
            return new JsonResponse(['error' => 'IPA文件不存在'], 404);
        }

        // 记录安装统计
        $metadata = [
            'user_agent' => $request->headers->get('User-Agent'),
            'ip' => $request->getClientIp()
        ];
        $this->distributionService->recordInstall($distributionId, $metadata);

        // 返回文件
        $response = new Response(file_get_contents($resignRecord['resigned_ipa_path']));
        $response->headers->set('Content-Type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $distribution['app_name'] . '.ipa"');
        $response->headers->set('Content-Length', (string)$resignRecord['resigned_file_size']);

        return $response;
    }



    /**
     * 获取用户的分发记录
     */
    public function getUserDistributions(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $limit = (int)$request->query->get('limit', 20);
        $skip = (int)$request->query->get('skip', 0);

        $distributions = $this->distributionService->getUserDistributions($user['_id'], $limit, $skip);
        $totalCount = $this->distributionModel->countUserRecords($user['_id']);

        return new JsonResponse([
            'success' => true,
            'distributions' => $distributions,
            'total_count' => $totalCount,
            'limit' => $limit,
            'skip' => $skip
        ]);
    }

    /**
     * 获取分发统计（管理员）
     */
    public function getDistributionStats(Request $request): JsonResponse
    {
        // 验证管理员权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user || $user['role'] !== 'admin') {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $stats = $this->distributionService->getDistributionStats();
        $popularApps = $this->distributionService->getPopularApps(10);

        return new JsonResponse([
            'success' => true,
            'stats' => $stats,
            'popular_apps' => $popularApps
        ]);
    }

    /**
     * 生成安装页面HTML
     */
    private function generateInstallPageHtml(array $distribution): string
    {
        // 重签记录数据已经在 getDistribution 中合并
        $resignRecord = $distribution['resign_record'] ?? null;

        $appName = htmlspecialchars($distribution['app_name']);
        $subtitle = htmlspecialchars($distribution['subtitle'] ?? '');
        $description = htmlspecialchars($distribution['description'] ?? '');
        $version = htmlspecialchars($distribution['version'] ?? '');
        $build = htmlspecialchars($distribution['build'] ?? '');
        $bundleId = htmlspecialchars($distribution['bundle_id'] ?? '');
        $minimumOsVersion = htmlspecialchars($distribution['minimum_os_version'] ?? '10.0');
        $installUrl = "itms-services://?action=download-manifest&url=" . urlencode($distribution['plist_url']);

        // 处理应用图标 - 优先使用分发记录中的图标，然后是重签记录中的图标
        $iconHtml = $this->generateIconHtml($distribution, $resignRecord);

        // 调试信息（生产环境应该移除）
        error_log("Distribution icon_base64 length: " . (isset($distribution['icon_base64']) ? strlen($distribution['icon_base64']) : 'null'));
        error_log("ResignRecord icon_base64 length: " . (isset($resignRecord['icon_base64']) ? strlen($resignRecord['icon_base64']) : 'null'));

        // 格式化时间
        $createdAt = '';
        $expiresAt = '';
        if (!empty($distribution['created_at'])) {
            $createdAt = date('Y-m-d H:i', strtotime($distribution['created_at']));
        }
        if (!empty($distribution['expires_at'])) {
            $expiresAt = date('Y-m-d H:i', strtotime($distribution['expires_at']));
        }

        // 统计信息
        $installCount = $distribution['install_count'] ?? 0;
        $viewCount = $distribution['view_count'] ?? 0;

        // 准备条件显示的HTML片段
        $subtitleHtml = $subtitle ? "<div class=\"app-subtitle\">{$subtitle}</div>" : "";
        $versionDisplay = $version . ($build ? " ({$build})" : "");
        $createdAtHtml = $createdAt ? "<div class=\"info-row\"><span class=\"info-label\">发布时间</span><span class=\"info-value\">{$createdAt}</span></div>" : "";
        $expiresAtHtml = $expiresAt ? "<div class=\"info-row\"><span class=\"info-label\">过期时间</span><span class=\"info-value\">{$expiresAt}</span></div>" : "";
        $descriptionHtml = $description ? "<div class=\"description\">{$description}</div>" : "";

        return <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$appName} - 应用安装</title>
    <meta name="description" content="{$description}">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <style>
        * {
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 420px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007AFF, #5856D6, #AF52DE);
        }
        .app-icon {
            width: 100px;
            height: 100px;
            border-radius: 20px;
            margin: 0 auto 24px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        .app-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1d1d1f;
            letter-spacing: -0.5px;
        }
        .app-subtitle {
            color: #86868b;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 400;
        }
        .app-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 16px;
            margin: 20px 0;
            text-align: left;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e5e7;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            color: #86868b;
            font-size: 14px;
            font-weight: 500;
        }
        .info-value {
            color: #1d1d1f;
            font-size: 14px;
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            word-break: break-all;
        }
        .install-btn {
            background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
            color: white;
            border: none;
            padding: 18px 32px;
            border-radius: 14px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin: 24px 0;
            text-decoration: none;
            display: inline-block;
            box-sizing: border-box;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
        }
        .install-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 122, 255, 0.4);
        }
        .install-btn:active {
            transform: translateY(0);
        }
        .description {
            color: #86868b;
            font-size: 15px;
            line-height: 1.6;
            margin: 20px 0;
            text-align: left;
            background: #f8f9fa;
            padding: 16px;
            border-radius: 12px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: #007AFF;
            display: block;
        }
        .stat-label {
            font-size: 12px;
            color: #86868b;
            margin-top: 4px;
        }
        .warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            border-radius: 12px;
            padding: 16px;
            margin-top: 24px;
            font-size: 13px;
            color: #856404;
            line-height: 1.5;
        }
        .warning-icon {
            font-size: 16px;
            margin-right: 8px;
        }
        .footer {
            margin-top: 24px;
            padding-top: 20px;
            border-top: 1px solid #e5e5e7;
            color: #86868b;
            font-size: 12px;
        }
        @media (max-width: 480px) {
            body {
                padding: 16px;
            }
            .container {
                padding: 24px;
            }
            .app-name {
                font-size: 24px;
            }
            .app-icon {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-icon">{$iconHtml}</div>
        <div class="app-name">{$appName}</div>
        {$subtitleHtml}

        <div class="app-info">
            <div class="info-row">
                <span class="info-label">版本</span>
                <span class="info-value">{$versionDisplay}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Bundle ID</span>
                <span class="info-value">{$bundleId}</span>
            </div>
            <div class="info-row">
                <span class="info-label">最低系统版本</span>
                <span class="info-value">iOS {$minimumOsVersion}+</span>
            </div>
            {$createdAtHtml}
            {$expiresAtHtml}
        </div>

        <a href="{$installUrl}" class="install-btn">📱 立即安装</a>

        <div class="stats">
            <div class="stat-item">
                <span class="stat-number">{$installCount}</span>
                <div class="stat-label">安装次数</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">{$viewCount}</span>
                <div class="stat-label">查看次数</div>
            </div>
        </div>

        {$descriptionHtml}

        <div class="warning">
            <span class="warning-icon">⚠️</span>
            <strong>安装提示：</strong><br>
            1. 请确保您的设备已信任企业证书<br>
            2. 安装后可能需要在"设置 > 通用 > 设备管理"中信任开发者<br>
            3. 如遇到安装问题，请尝试重新下载或联系技术支持
        </div>

        <div class="footer">
            由 XIOS 提供技术支持 • 安全可靠的应用分发服务
        </div>
    </div>

    <script>
        // 检测设备类型并优化体验
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/.test(navigator.userAgent);

        if (!isIOS) {
            const installBtn = document.querySelector('.install-btn');
            if (installBtn) {
                installBtn.innerHTML = '⚠️ 仅支持 iOS 设备安装';
                installBtn.style.background = '#ff9500';
                installBtn.onclick = function(e) {
                    e.preventDefault();
                    alert('此应用仅支持 iOS 设备安装，请使用 iPhone 或 iPad 访问此链接。');
                };
            }
        }

        // 添加安装统计
        document.querySelector('.install-btn').addEventListener('click', function() {
            // 发送安装统计（异步，不影响安装流程）
            fetch(window.location.href.replace('/install/', '/api/distribution/install-stat/'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_agent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                })
            }).catch(() => {}); // 忽略错误，不影响安装
        });
    </script>
</body>
</html>
HTML;
    }



    /**
     * 记录安装统计（用于前端JavaScript调用）
     */
    public function recordInstallStat(Request $request): JsonResponse
    {
        $distributionId = $request->attributes->get('distributionId');
        $data = json_decode($request->getContent(), true);

        $metadata = [
            'user_agent' => $data['user_agent'] ?? $request->headers->get('User-Agent'),
            'ip' => $request->getClientIp(),
            'timestamp' => $data['timestamp'] ?? date('c')
        ];

        $success = $this->distributionService->recordInstall($distributionId, $metadata);

        return new JsonResponse([
            'success' => $success
        ]);
    }

    /**
     * 生成图标HTML
     */
    private function generateIconHtml(array $distribution, ?array $resignRecord): string
    {
        $iconBase64 = null;

        // 优先使用分发记录中的图标
        if (!empty($distribution['icon_base64'])) {
            $iconBase64 = $distribution['icon_base64'];
        } elseif (!empty($resignRecord['icon_base64'])) {
            $iconBase64 = $resignRecord['icon_base64'];
        }

        if ($iconBase64) {
            // 验证和清理base64数据
            $iconBase64 = trim($iconBase64);

            // 检查是否已经包含data URL前缀
            if (strpos($iconBase64, 'data:image/') === 0) {
                $iconSrc = htmlspecialchars($iconBase64);
            } else {
                // 验证base64格式
                if (preg_match('/^[A-Za-z0-9+\/]*={0,2}$/', $iconBase64) && strlen($iconBase64) > 10) {
                    $iconSrc = 'data:image/png;base64,' . $iconBase64;
                } else {
                    // base64数据无效，使用默认图标
                    return '<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-size: 32px; color: #666;">📱</div>';
                }
            }

            return '<img src="' . $iconSrc . '" alt="App Icon" style="width: 100%; height: 100%; border-radius: 16px; object-fit: cover;" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">
                    <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; font-size: 32px; color: #666;">📱</div>';
        }

        // 没有图标数据，使用默认图标
        return '<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-size: 32px; color: #666;">📱</div>';
    }

    /**
     * 获取认证用户信息
     */
    private function getAuthenticatedUser(Request $request): ?array
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return null;
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        return $this->authService->verifyToken($token);
    }
}
