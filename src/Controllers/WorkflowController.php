<?php

namespace App\Controllers;

use App\Models\UploadRecord;
use App\Models\GitHubAccount;
use App\Services\UploadService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class WorkflowController
{
    private UploadRecord $uploadRecordModel;
    private GitHubAccount $githubAccountModel;
    private UploadService $uploadService;

    public function __construct()
    {
        $this->uploadRecordModel = new UploadRecord();
        $this->githubAccountModel = new GitHubAccount();
        $this->uploadService = new UploadService();
    }

    /**
     * 处理工作流回调
     */
    public function callback(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $recordId = $data['record_id'] ?? '';
        $status = $data['status'] ?? '';
        $result = $data['result'] ?? [];
        $error = $data['error'] ?? null;
        $workflowIp = $data['workflow_ip'] ?? null;
        $workflowDuration = $data['workflow_duration'] ?? null;

        if (empty($recordId)) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少记录ID'
            ], 400);
        }

        // 查找上传记录
        $record = $this->uploadRecordModel->findById($recordId);
        if (!$record) {
            return new JsonResponse([
                'success' => false,
                'error' => '记录不存在'
            ], 404);
        }

        // 更新记录状态
        $updateData = [];
        $logs = $data['logs'] ?? '';

        // 添加工作流执行信息
        if ($workflowIp) {
            $updateData['workflow_ip'] = $workflowIp;
        }
        if ($workflowDuration !== null) {
            $updateData['workflow_duration'] = (int)$workflowDuration;
        }

        // 解码base64编码的日志
        if (!empty($logs)) {
            $decodedLogs = base64_decode($logs);
            if ($decodedLogs !== false) {
                $logs = $decodedLogs;
            }
        }

        // 检查是否有隐藏的错误（即使状态显示成功）
        $hasHiddenErrors = false;
        if ($status === 'success' && !empty($logs)) {
            $hasHiddenErrors = \App\Services\AppleErrorAnalyzer::hasHiddenErrors($logs);
            if ($hasHiddenErrors) {
                // 重新分析错误
                $hiddenError = \App\Services\AppleErrorAnalyzer::analyzeError($logs);
                $status = 'failed';
                $error = $hiddenError;

                // 提取版本信息
                $versionInfo = \App\Services\AppleErrorAnalyzer::extractVersionInfo($logs);
                if ($versionInfo['previous_version']) {
                    $error['message'] .= " (之前版本: {$versionInfo['previous_version']})";
                    $error['solution'] = "请将Bundle Version设置为大于 {$versionInfo['previous_version']} 的数字";
                }
            }
        }

        if ($status === 'success' && !$hasHiddenErrors) {
            $updateData['upload_result'] = $result;
            $this->uploadRecordModel->updateStatus($recordId, 'success', $updateData);
        } elseif ($status === 'failed') {
            // 最终失败状态
            // 检查是否已经有错误信息，如果没有则创建通用错误信息
            $existingRecord = $this->uploadRecordModel->findById($recordId);
            if (!$existingRecord || empty($existingRecord['upload_result'])) {
                $updateData['upload_result'] = [
                    'type' => 'unknown',
                    'category' => 'upload_error',
                    'title' => '上传失败',
                    'message' => '上传过程中发生错误',
                    'solution' => '请检查应用配置和网络连接，或联系技术支持',
                    'step' => 'upload',
                    'details' => '工作流执行失败，未能获取详细错误信息',
                    'raw_error' => ''
                ];
            }
            $this->uploadRecordModel->updateStatus($recordId, 'failed', $updateData);
        } elseif ($status === 'processing' && $error && is_array($error)) {
            // 处理中状态但包含错误信息，说明是工作流发送的错误详情
            // 使用特殊状态 'error_details' 避免与正常的 processing 状态混淆
            $errorData = [
                'message' => $error['message'] ?? '上传失败',
                'type' => $error['type'] ?? 'unknown'
            ];

            // 保存详细的错误信息
            $errorData = array_merge($errorData, [
                'category' => $error['category'] ?? 'unknown',
                'title' => $error['title'] ?? '上传失败',
                'solution' => $error['solution'] ?? '请检查日志信息',
                'step' => $error['step'] ?? 'unknown',
                'is_retryable' => $error['is_retryable'] ?? false
            ]);

            // 处理Base64编码的详细信息
            if (isset($error['details_base64'])) {
                $decodedDetails = base64_decode($error['details_base64']);
                $errorData['details'] = $decodedDetails !== false ? $decodedDetails : ($error['details'] ?? '');
            } else {
                $errorData['details'] = $error['details'] ?? '';
            }

            if (isset($error['raw_error_base64'])) {
                $decodedRawError = base64_decode($error['raw_error_base64']);
                $errorData['raw_error'] = $decodedRawError !== false ? $decodedRawError : ($error['raw_error'] ?? '');
            } else {
                $errorData['raw_error'] = $error['raw_error'] ?? '';
            }

            // 保存错误信息，使用特殊状态避免进度倒退
            $updateData['upload_result'] = $errorData;
            $this->uploadRecordModel->updateStatus($recordId, 'error_details', $updateData);
        } else {
            // 其他状态更新
            $this->uploadRecordModel->updateStatus($recordId, $status, $updateData);
        }

        // 更新GitHub账号使用统计（如果有时长信息）
        if ($workflowDuration !== null && ($status === 'success' || $status === 'failed')) {
            $this->updateGitHubAccountUsage($recordId, (int)$workflowDuration);
        }

        // 处理队列中的下一个任务
        $this->uploadService->processUploadQueue();

        return new JsonResponse([
            'success' => true,
            'message' => '回调处理成功'
        ], 200);
    }

    /**
     * 更新GitHub账号使用统计
     */
    private function updateGitHubAccountUsage(string $recordId, int $durationSeconds): void
    {
        try {
            // 获取上传记录以找到使用的GitHub账号
            $record = $this->uploadRecordModel->findById($recordId);
            if (!$record || !isset($record['github_account'])) {
                return;
            }

            $githubAccountId = $record['github_account'];
            if (is_array($githubAccountId) && isset($githubAccountId['$oid'])) {
                $githubAccountId = $githubAccountId['$oid'];
            }

            // 更新GitHub账号的使用统计
            $success = $this->githubAccountModel->updateUsageStats($githubAccountId, $durationSeconds);

            if ($success) {
                error_log("GitHub账号使用统计更新成功: {$githubAccountId}, 时长: {$durationSeconds}秒");
            } else {
                error_log("GitHub账号使用统计更新失败: {$githubAccountId}");
            }
        } catch (\Exception $e) {
            error_log("更新GitHub账号使用统计时发生错误: " . $e->getMessage());
        }
    }

    /**
     * 下载IPA文件（工作流专用）
     */
    public function downloadIPA(Request $request, string $recordId): Response
    {
        // 验证记录ID格式
        if (!preg_match('/^[a-f0-9]{24}$/', $recordId)) {
            return new Response('Invalid record ID', 400);
        }

        // 查找上传记录
        $record = $this->uploadRecordModel->findById($recordId);
        if (!$record) {
            return new Response('Record not found', 404);
        }

        // 检查文件是否存在
        $filePath = $this->getIPAFilePath($record['stored_filename']);
        if (!file_exists($filePath)) {
            return new Response('File not found', 404);
        }

        // 设置响应头
        $response = new Response();
        $response->headers->set('Content-Type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $record['filename'] . '"');
        $response->headers->set('Content-Length', filesize($filePath));

        // 读取文件内容
        $content = file_get_contents($filePath);
        $response->setContent($content);

        return $response;
    }

    /**
     * 获取IPA文件路径
     */
    private function getIPAFilePath(string $storedFilename): string
    {
        $config = \App\Config\AppConfig::getInstance();
        return $config->get('ipa_storage_path') . '/' . $storedFilename;
    }
}
