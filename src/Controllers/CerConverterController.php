<?php

namespace App\Controllers;

use App\Services\CerConverterService;
use App\Services\AuthService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class CerConverterController
{
    private CerConverterService $cerConverterService;
    private AuthService $authService;

    public function __construct()
    {
        $this->cerConverterService = new CerConverterService();
        $this->authService = new AuthService();
    }

    /**
     * 获取认证用户信息
     */
    private function getAuthenticatedUser(Request $request): ?array
    {
        $token = $request->headers->get('Authorization');
        if (!$token || !str_starts_with($token, 'Bearer ')) {
            return null;
        }

        $token = substr($token, 7);
        $user = $this->authService->verifyToken($token);

        // 临时调试：如果token验证失败，记录详细信息
        if (!$user) {
            error_log("Token verification failed for token: " . substr($token, 0, 20) . "...");
            error_log("Request headers: " . json_encode($request->headers->all()));
        }

        return $user;
    }

    /**
     * 转换CER文件为P12格式
     */
    public function convertCerToP12(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        // 检查是否有CER文件上传
        $files = $request->files->all();
        if (!isset($files['cer_file'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '未找到CER文件'
            ], 400);
        }

        // 获取转换参数
        $conversionData = [
            'certificate_name' => $request->request->get('certificate_name'),
            'password' => $request->request->get('password'),
            'certificate_type' => $request->request->get('certificate_type', 'distribution')
        ];

        // 准备CER文件数据
        $cerFile = $files['cer_file'];
        $fileData = [
            'name' => $cerFile->getClientOriginalName(),
            'tmp_name' => $cerFile->getPathname(),
            'size' => $cerFile->getSize(),
            'error' => $cerFile->getError()
        ];

        // 检查是否手动选择了CSR
        $manualCsrId = $request->request->get('manual_csr_id');

        if ($manualCsrId) {
            // 使用手动选择的CSR进行转换
            $result = $this->cerConverterService->convertCerToP12WithManualCsr($fileData, $conversionData, $user['_id'], $manualCsrId);
        } else {
            // 使用智能匹配进行转换
            $result = $this->cerConverterService->convertCerToP12($fileData, $conversionData, $user['_id']);
        }

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }



    /**
     * 下载转换后的P12文件
     */
    public function downloadP12(Request $request): Response
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $certificateId = $request->attributes->get('certificateId');

        // 获取证书信息
        $certificateModel = new \App\Models\Certificate();
        $certificate = $certificateModel->findById($certificateId);

        if (!$certificate) {
            return new JsonResponse([
                'success' => false,
                'error' => '证书不存在'
            ], 404);
        }

        // 检查权限（用户只能下载自己的证书）
        $certificateUserId = is_object($certificate['uploaded_by']) ? (string)$certificate['uploaded_by'] : $certificate['uploaded_by'];
        $currentUserId = is_object($user['_id']) ? (string)$user['_id'] : $user['_id'];

        if ($certificateUserId !== $currentUserId) {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        // 检查文件是否存在
        $filePath = $certificate['file_path'];
        if (!file_exists($filePath)) {
            return new JsonResponse([
                'success' => false,
                'error' => '文件不存在'
            ], 404);
        }

        // 返回文件下载
        $filename = $certificate['name'] . '.p12';
        $response = new Response(file_get_contents($filePath));
        $response->headers->set('Content-Type', 'application/x-pkcs12');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
        $response->headers->set('Content-Length', filesize($filePath));

        return $response;
    }

    /**
     * 获取转换指南
     */
    public function getConversionGuide(Request $request): JsonResponse
    {
        $guide = [
            'title' => 'CER转P12智能转换指南',
            'steps' => [
                [
                    'step' => 1,
                    'title' => '生成CSR',
                    'description' => '首先在本系统使用CSR生成功能创建证书签名请求',
                    'note' => '系统会自动生成CSR文件和对应的私钥，私钥会安全保存在服务器上'
                ],
                [
                    'step' => 2,
                    'title' => '申请证书',
                    'description' => '使用生成的CSR文件到Apple Developer申请证书，下载CER文件',
                    'note' => '确保使用本系统生成的CSR文件申请证书，这样系统才能找到对应的私钥'
                ],
                [
                    'step' => 3,
                    'title' => '上传转换',
                    'description' => '上传从Apple下载的CER文件，系统会自动匹配对应的私钥',
                    'note' => '系统会智能检测CER文件是否由本系统的CSR申请，无需手动提供私钥'
                ],
                [
                    'step' => 4,
                    'title' => '下载P12',
                    'description' => '设置P12密码和证书名称，系统自动转换并提供下载',
                    'note' => '转换后的P12文件可以直接用于iOS应用签名和Xcode配置'
                ]
            ],
            'requirements' => [
                'CER文件必须是使用本系统生成的CSR申请的',
                'CER文件必须是从Apple Developer下载的有效证书',
                'P12密码长度至少6位',
                '支持的证书类型：开发证书、分发证书'
            ],
            'tips' => [
                '只有使用本系统生成的CSR申请的证书才能转换',
                '如果提示找不到匹配的私钥，请先使用CSR生成功能',
                '系统会自动匹配证书的Common Name和组织信息',
                '转换后的P12文件包含完整的证书链和私钥'
            ],
            'workflow' => [
                '本系统CSR生成 → Apple Developer申请证书 → 下载CER文件 → 本系统转换P12',
                '这样的流程确保私钥安全且转换成功率最高'
            ]
        ];

        return new JsonResponse([
            'success' => true,
            'guide' => $guide
        ]);
    }

    /**
     * 获取用户转换记录
     */
    public function getConversionRecords(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $userId = $user['_id'];
        $role = $user['role'] ?? 'user';

        // 获取分页参数，与重签记录保持一致
        $page = max(1, (int) $request->query->get('page', 1));
        $pageSize = max(1, min(100, (int) $request->query->get('page_size', 10))); // 限制最大100条

        if ($role === 'admin' || $role === 'super_admin') {
            // 管理员和超级管理员查所有记录（支持分页）
            $result = $this->cerConverterService->getAllRecordsWithUserPaginated($page, $pageSize);
        } else {
            // 普通用户查自己的记录（支持分页）
            $result = $this->cerConverterService->findByUserIdPaginated($userId, $page, $pageSize);
        }

        return new JsonResponse([
            'success' => true,
            'records' => $result['records'],
            'pagination' => $result['pagination']
        ]);
    }
}
