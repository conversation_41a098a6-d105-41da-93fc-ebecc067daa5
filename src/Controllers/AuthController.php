<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Services\UploadService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class AuthController
{
    private AuthService $authService;
    private UploadService $uploadService;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->uploadService = new UploadService();
    }

    /**
     * 根据错误信息返回对应的HTTP状态码
     */
    private function getStatusCodeByError(string $error): int
    {
        if (strpos($error, '未激活') !== false) {
            return 403; // Forbidden - 账号未激活
        }

        if (strpos($error, '过期') !== false) {
            return 410; // Gone - 账号已过期
        }

        if (strpos($error, '用户不存在') !== false || strpos($error, '密码错误') !== false) {
            return 401; // Unauthorized - 认证失败
        }

        if (strpos($error, '频繁') !== false) {
            return 429; // Too Many Requests - 请求过于频繁
        }

        return 400; // Bad Request - 其他错误
    }

    /**
     * 用户登录
     */
    public function login(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $username = $data['username'] ?? $data['email'] ?? '';
        $password = $data['password'] ?? '';

        if (empty($username) || empty($password)) {
            return new JsonResponse([
                'success' => false,
                'error' => '用户名/邮箱和密码不能为空'
            ], 400);
        }

        $result = $this->authService->login($username, $password);

        if ($result['success']) {
            return new JsonResponse($result, 200);
        } else {
            // 根据错误类型返回不同的状态码
            $statusCode = $this->getStatusCodeByError($result['error']);
            return new JsonResponse($result, $statusCode);
        }
    }

    /**
     * 激活账号
     */
    public function activate(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $username = $data['username'] ?? '';
        $activationCode = $data['activation_code'] ?? '';

        if (empty($username) || empty($activationCode)) {
            return new JsonResponse([
                'success' => false,
                'error' => '用户名和激活码不能为空'
            ], 400);
        }

        $result = $this->authService->activate($username, $activationCode);

        if ($result['success']) {
            return new JsonResponse($result, 200);
        } else {
            return new JsonResponse($result, 400);
        }
    }

    /**
     * 验证token
     */
    public function verify(Request $request): JsonResponse
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少认证token'
            ], 401);
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        $user = $this->authService->verifyToken($token);

        if ($user) {
            return new JsonResponse([
                'success' => true,
                'user' => $user
            ], 200);
        } else {
            return new JsonResponse([
                'success' => false,
                'error' => 'token无效或已过期'
            ], 401);
        }
    }

    /**
     * 刷新token
     */
    public function refresh(Request $request): JsonResponse
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少认证token'
            ], 401);
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        $newToken = $this->authService->refreshToken($token);

        if ($newToken) {
            return new JsonResponse([
                'success' => true,
                'token' => $newToken
            ], 200);
        } else {
            return new JsonResponse([
                'success' => false,
                'error' => 'token刷新失败'
            ], 401);
        }
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $currentPassword = $data['current_password'] ?? '';
        $newPassword = $data['new_password'] ?? '';

        if (empty($currentPassword) || empty($newPassword)) {
            return new JsonResponse([
                'success' => false,
                'error' => '当前密码和新密码不能为空'
            ], 400);
        }

        if (strlen($newPassword) < 6) {
            return new JsonResponse([
                'success' => false,
                'error' => '新密码长度至少6位'
            ], 400);
        }

        // 获取当前用户
        $token = $request->headers->get('Authorization');
        if (!$token) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少认证token'
            ], 401);
        }

        $token = str_replace('Bearer ', '', $token);
        $user = $this->authService->verifyToken($token);

        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => 'token无效或已过期'
            ], 401);
        }

        $result = $this->authService->changePassword($user['_id'], $currentPassword, $newPassword);

        if ($result['success']) {
            return new JsonResponse($result, 200);
        } else {
            $statusCode = $this->getStatusCodeByError($result['error']);
            return new JsonResponse($result, $statusCode);
        }
    }

    // validateAPIKey 和 validateAppleID 方法已删除（前端未使用）
}
