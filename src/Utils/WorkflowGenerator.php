<?php

namespace App\Utils;

class WorkflowGenerator
{
  public function __construct()
  {
    // 简化构造函数，专注于工作流生成
  }

  /**
   * 生成API Key工作流
   */
  public function generateAPIKeyWorkflow(array $data): string
  {
    $recordId = $data['record_id'];
    $callbackUrl = $data['callback_url'];
    $downloadUrl = $data['download_url'] ?? "https://api.ios.xxyx.cn/api/upload/download/{$recordId}";

    return "name: Upload to TestFlight (API Key)

on:
  workflow_dispatch:
    inputs:
      record_id:
        description: 'Upload Record ID'
        required: true
        type: string

jobs:
  upload:
    runs-on: macos-latest
    
    steps:
    - name: Initialize Workflow
      run: |
        # 记录工作流开始时间
        echo \"WORKFLOW_START_TIME=\$(date +%s)\" >> \$GITHUB_ENV
        echo \"🚀 工作流开始时间: \$(date)\"

        # 获取执行IP地址
        WORKFLOW_IP=\$(curl -s https://api.ipify.org || curl -s https://ipinfo.io/ip || echo \"unknown\")
        echo \"WORKFLOW_IP=\$WORKFLOW_IP\" >> \$GITHUB_ENV
        echo \"🌐 工作流执行IP: \$WORKFLOW_IP\"

    - name: Checkout
      uses: actions/checkout@v4

    - name: Report Start
      run: |
        curl -X POST \"{$callbackUrl}\" \\
          -H \"Content-Type: application/json\" \\
          -d '{\"record_id\":\"\${{ github.event.inputs.record_id }}\",\"status\":\"started\"}'

    - name: Download IPA file
      run: |
        echo \"下载IPA文件...\"
        DOWNLOAD_URL=\"{$downloadUrl}\"
        
        # 下载文件，添加重试机制
        DOWNLOAD_SUCCESS=false
        for i in {1..3}; do
          echo \"尝试下载 (第\$i次)...\"
          if curl -L -f --connect-timeout 30 --max-time 300 \"\$DOWNLOAD_URL\" -o app.ipa; then
            echo \"✅ 下载成功\"
            DOWNLOAD_SUCCESS=true
            break
          else
            echo \"❌ 下载失败，等待5秒后重试...\"
            if [ \$i -lt 3 ]; then
              sleep 5
            fi
          fi
        done
        
        # 检查下载是否成功
        if [ \"\$DOWNLOAD_SUCCESS\" = false ]; then
          echo \"❌ 下载失败，已重试3次\"
          
          # 报告下载失败
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"failed\\\",\\
              \\\"error\\\":{\\
                \\\"type\\\":\\\"download_failed\\\",\\
                \\\"category\\\":\\\"download_error\\\",\\
                \\\"title\\\":\\\"IPA文件下载失败\\\",\\
                \\\"message\\\":\\\"无法从服务器下载IPA文件\\\",\\
                \\\"solution\\\":\\\"请检查文件是否存在，或稍后重试\\\",\\
                \\\"details\\\":\\\"下载URL: \$DOWNLOAD_URL\\\",\\
                \\\"step\\\":\\\"download\\\"\\
              }\\
            }\"
          
          exit 1
        fi

        # 验证文件
        if [ ! -f \"app.ipa\" ] || [ ! -s \"app.ipa\" ]; then
          echo \"❌ IPA文件不存在或为空\"
          
          # 报告文件验证失败
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"failed\\\",\\
              \\\"error\\\":{\\
                \\\"type\\\":\\\"download_failed\\\",\\
                \\\"category\\\":\\\"download_error\\\",\\
                \\\"title\\\":\\\"IPA文件下载失败\\\",\\
                \\\"message\\\":\\\"下载的IPA文件不存在或为空\\\",\\
                \\\"solution\\\":\\\"请检查文件是否正确上传到服务器\\\",\\
                \\\"details\\\":\\\"文件验证失败：文件不存在或大小为0\\\",\\
                \\\"step\\\":\\\"download\\\"\\
              }\\
            }\"
          
          exit 1
        fi

        echo \"✅ IPA文件下载成功\"
        ls -la app.ipa
        file app.ipa

    - name: Setup App Store Connect API Key
      run: |
        mkdir -p ~/private_keys
        echo \"\${{ secrets.API_KEY_CONTENT }}\" > ~/private_keys/AuthKey_\${{ secrets.API_KEY_ID }}.p8
        chmod 600 ~/private_keys/AuthKey_\${{ secrets.API_KEY_ID }}.p8

    - name: Validate IPA
      run: |
        echo \"🔍 验证IPA文件...\"
        
        if xcrun altool --validate-app \\
          -f \"app.ipa\" \\
          -t ios \\
          --apiKey \"\${{ secrets.API_KEY_ID }}\" \\
          --apiIssuer \"\${{ secrets.ISSUER_ID }}\" \\
          --verbose 2>&1 | tee validation_output.log; then
          echo \"✅ IPA验证成功\"
        else
          echo \"❌ IPA验证失败\"
          
          # 分析验证错误
          if grep -q \"bundle version must be higher\" validation_output.log; then
            ERROR_TYPE=\"version_exists\"
            ERROR_TITLE=\"版本号重复\"
            ERROR_MSG=\"当前版本号已存在于App Store Connect\"
            ERROR_SOLUTION=\"请增加应用的Bundle Version (Build号)后重新上传\"
          else
            ERROR_TYPE=\"validation_failed\"
            ERROR_TITLE=\"IPA验证失败\"
            ERROR_MSG=\"IPA文件验证未通过\"
            ERROR_SOLUTION=\"请检查应用签名和配置\"
          fi
          
          # 报告验证失败
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"failed\\\",\\
              \\\"error\\\":{\\
                \\\"type\\\":\\\"\$ERROR_TYPE\\\",\\
                \\\"category\\\":\\\"validation_error\\\",\\
                \\\"title\\\":\\\"\$ERROR_TITLE\\\",\\
                \\\"message\\\":\\\"\$ERROR_MSG\\\",\\
                \\\"solution\\\":\\\"\$ERROR_SOLUTION\\\",\\
                \\\"step\\\":\\\"validation\\\"\\
              }\\
            }\"
          
          exit 1
        fi

    - name: Upload to TestFlight
      run: |
        echo \"🚀 开始上传到TestFlight...\"

        # 首先提取应用信息
        echo \"📱 提取应用信息...\"
        unzip -q app.ipa -d temp_ipa
        INFO_PLIST=\"temp_ipa/Payload/*.app/Info.plist\"
        
        BUNDLE_ID=\$(plutil -extract CFBundleIdentifier raw \$INFO_PLIST)
        VERSION=\$(plutil -extract CFBundleShortVersionString raw \$INFO_PLIST)
        BUILD=\$(plutil -extract CFBundleVersion raw \$INFO_PLIST)
        
        echo \"Bundle ID: \$BUNDLE_ID\"
        echo \"Version: \$VERSION\"
        echo \"Build: \$BUILD\"
        
        # 清理临时文件
        rm -rf temp_ipa

        # 报告开始上传状态
        curl -X POST \"{$callbackUrl}\" \\
          -H \"Content-Type: application/json\" \\
          -d '{\"record_id\":\"\${{ github.event.inputs.record_id }}\",\"status\":\"uploading\"}'

        # 使用完整的altool命令上传，包含所有必要参数
        echo \"🚀 开始上传到App Store Connect...\"
        xcrun altool --upload-app \\
          -f \"app.ipa\" \\
          -t ios \\
          --apiKey \"\${{ secrets.API_KEY_ID }}\" \\
          --apiIssuer \"\${{ secrets.ISSUER_ID }}\" \\
          --bundle-id \"\$BUNDLE_ID\" \\
          --bundle-short-version-string \"\$VERSION\" \\
          --bundle-version \"\$BUILD\" \\
          --verbose 2>&1 | tee upload_output.log
        
        ALTOOL_EXIT_CODE=\$?
        
        # 检查是否有错误，即使退出码为0
        # 先检查是否有明显的错误关键词
        if grep -q \"Error uploading\\|Failed to request\\|ContentDelivery Code=\\|IrisAPI Code=\\|ENTITY_ERROR\" upload_output.log; then
          echo \"❌ 检测到错误关键词，判定为失败\"
          UPLOAD_FAILED=true
        elif [ \$ALTOOL_EXIT_CODE -ne 0 ]; then
          echo \"❌ altool 退出码非零: \$ALTOOL_EXIT_CODE\"
          UPLOAD_FAILED=true
        else
          echo \"✅ 未检测到错误，判定为成功\"
          UPLOAD_FAILED=false
        fi

        if [ \"\$UPLOAD_FAILED\" = \"false\" ]; then
          echo \"✅ Upload succeeded!\"
          echo \"🎉 Your app is now processing in App Store Connect\"

          # 计算执行时长
          END_TIME=\$(date +%s)
          DURATION=\$((END_TIME - WORKFLOW_START_TIME))
          echo \"工作流执行时长: \${DURATION}秒\"

          # 报告成功（包含日志内容用于隐藏错误检测）
          UPLOAD_LOGS=\$(cat upload_output.log | base64 | tr -d '\\n')
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"success\\\",\\
              \\\"result\\\":{\\\"message\\\":\\\"Upload completed successfully\\\"},\\
              \\\"logs\\\":\\\"\$UPLOAD_LOGS\\\",\\
              \\\"workflow_ip\\\":\\\"\$WORKFLOW_IP\\\",\\
              \\\"workflow_duration\\\":\$DURATION\\
            }\"
        else
          echo \"❌ Upload failed, analyzing error...\"

          echo \"📋 分析错误信息...\"

          # 分析错误信息
          if grep -q \"bundle version must be higher\" upload_output.log || \\
             grep -q \"ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE\" upload_output.log || \\
             grep -q \"ContentDelivery Code=-19232\" upload_output.log || \\
             grep -q \"IrisAPI Code=-19241\" upload_output.log || \\
             grep -q \"previousBundleVersion\" upload_output.log; then
            ERROR_TYPE=\"version_exists\"
            ERROR_CATEGORY=\"version_error\"
            ERROR_TITLE=\"版本号重复\"
            ERROR_MSG=\"当前版本号已存在于App Store Connect\"
            ERROR_SOLUTION=\"请增加应用的Bundle Version (Build号)后重新上传\"

            # 尝试提取之前的版本号
            PREV_VERSION=\$(grep -o \"previousBundleVersion = [0-9]*\" upload_output.log | head -1 | cut -d' ' -f3)
            if [ ! -z \"\$PREV_VERSION\" ]; then
              ERROR_MSG=\"当前版本号已存在，之前上传的版本号是: \$PREV_VERSION\"
              ERROR_SOLUTION=\"请将Bundle Version (Build号)设置为大于 \$PREV_VERSION 的数字后重新上传\"
            fi

          elif grep -q \"authentication\" upload_output.log || grep -q \"Invalid API Key\" upload_output.log; then
            ERROR_TYPE=\"auth_failed\"
            ERROR_CATEGORY=\"auth_error\"
            ERROR_TITLE=\"认证失败\"
            ERROR_MSG=\"API Key认证失败\"
            ERROR_SOLUTION=\"请检查API Key ID、Issuer ID和私钥内容是否正确\"

          else
            ERROR_TYPE=\"unknown\"
            ERROR_CATEGORY=\"upload_error\"
            ERROR_TITLE=\"上传失败\"
            ERROR_MSG=\"未知错误\"
            ERROR_SOLUTION=\"请检查详细日志信息\"
          fi

          # 提取关键错误信息
          ERROR_DETAILS=\$(grep -i \"error\\|failed\\|invalid\\|ContentDelivery\\|IrisAPI\" upload_output.log | head -5 | tr '\\n' '; ')

          # 提取最后一段完整的错误信息（从最后一个分隔符开始）
          FINAL_ERROR_SECTION=\$(awk '/^={30,}/ {section=\"\"} {section=section\"\\n\"\$0} END {print section}' upload_output.log | tail -c +2)

          # 如果没有找到分隔符，尝试提取最后的错误块
          if [ -z \"\$FINAL_ERROR_SECTION\" ] || [ \"\$FINAL_ERROR_SECTION\" = \"\\n\" ]; then
            FINAL_ERROR_SECTION=\$(tail -20 upload_output.log | grep -A 20 -B 5 \"Error\\|Failed\\|ContentDelivery\\|IrisAPI\" | head -20)
          fi

          echo \"🔍 错误类型: \$ERROR_TITLE\"
          echo \"💡 解决方案: \$ERROR_SOLUTION\"
          echo \"📋 详细错误: \$ERROR_DETAILS\"

          # 报告失败（使用Base64编码避免JSON转义问题）
          # 将详细错误信息进行Base64编码（兼容macOS和Linux）
          ERROR_DETAILS_B64=\$(echo \"\$ERROR_DETAILS\" | base64 | tr -d '\\n')
          RAW_ERROR_B64=\$(echo \"\$FINAL_ERROR_SECTION\" | base64 | tr -d '\\n')

          # 先发送错误详情（状态仍为processing，避免前端过早检测到失败）
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"processing\\\",\\
              \\\"error\\\":{\\
                \\\"type\\\":\\\"\$ERROR_TYPE\\\",\\
                \\\"category\\\":\\\"\$ERROR_CATEGORY\\\",\\
                \\\"title\\\":\\\"\$ERROR_TITLE\\\",\\
                \\\"message\\\":\\\"\$ERROR_MSG\\\",\\
                \\\"solution\\\":\\\"\$ERROR_SOLUTION\\\",\\
                \\\"details_base64\\\":\\\"\$ERROR_DETAILS_B64\\\",\\
                \\\"raw_error_base64\\\":\\\"\$RAW_ERROR_B64\\\",\\
                \\\"step\\\":\\\"upload\\\"\\
              }\\
            }\"

          # 等待1秒确保回调处理完成
          sleep 1

          # 计算执行时长
          END_TIME=\$(date +%s)
          DURATION=\$((END_TIME - WORKFLOW_START_TIME))
          echo \"工作流执行时长: \${DURATION}秒\"

          # 再发送最终失败状态
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"failed\\\",\\
              \\\"workflow_ip\\\":\\\"\$WORKFLOW_IP\\\",\\
              \\\"workflow_duration\\\":\$DURATION\\
            }\"

          exit 1
        fi

    - name: Cleanup
      if: always()
      run: |
        rm -f app.ipa
        rm -rf temp_ipa
        echo \"清理完成\"
";
  }

  /**
   * 生成Apple ID工作流
   */
  public function generateAppleIDWorkflow(array $data): string
  {
    $recordId = $data['record_id'];
    $callbackUrl = $data['callback_url'];
    $downloadUrl = $data['download_url'] ?? "https://api.ios.xxyx.cn/api/upload/download/{$recordId}";

    return "name: Upload to TestFlight (Apple ID)

on:
  workflow_dispatch:
    inputs:
      record_id:
        description: 'Upload Record ID'
        required: true
        type: string

jobs:
  upload:
    runs-on: macos-latest

    steps:
    - name: Initialize Workflow
      run: |
        # 记录工作流开始时间
        echo \"WORKFLOW_START_TIME=\$(date +%s)\" >> \$GITHUB_ENV
        echo \"🚀 工作流开始时间: \$(date)\"

        # 获取执行IP地址
        WORKFLOW_IP=\$(curl -s https://api.ipify.org || curl -s https://ipinfo.io/ip || echo \"unknown\")
        echo \"WORKFLOW_IP=\$WORKFLOW_IP\" >> \$GITHUB_ENV
        echo \"🌐 工作流执行IP: \$WORKFLOW_IP\"

    - name: Checkout
      uses: actions/checkout@v4

    - name: Report Start
      run: |
        curl -X POST \"{$callbackUrl}\" \\
          -H \"Content-Type: application/json\" \\
          -d '{\"record_id\":\"\${{ github.event.inputs.record_id }}\",\"status\":\"started\"}'

    - name: Download IPA file
      run: |
        echo \"下载IPA文件...\"
        DOWNLOAD_URL=\"{$downloadUrl}\"

        # 下载文件，添加重试机制
        DOWNLOAD_SUCCESS=false
        for i in {1..3}; do
          echo \"尝试下载 (第\$i次)...\"
          if curl -L -f --connect-timeout 30 --max-time 300 \"\$DOWNLOAD_URL\" -o app.ipa; then
            echo \"✅ 下载成功\"
            DOWNLOAD_SUCCESS=true
            break
          else
            echo \"❌ 下载失败，等待5秒后重试...\"
            if [ \$i -lt 3 ]; then
              sleep 5
            fi
          fi
        done

        # 检查下载是否成功
        if [ \"\$DOWNLOAD_SUCCESS\" = false ]; then
          echo \"❌ 下载失败，已重试3次\"

          # 报告下载失败
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"failed\\\",\\
              \\\"error\\\":{\\
                \\\"type\\\":\\\"download_failed\\\",\\
                \\\"category\\\":\\\"download_error\\\",\\
                \\\"title\\\":\\\"IPA文件下载失败\\\",\\
                \\\"message\\\":\\\"无法从服务器下载IPA文件\\\",\\
                \\\"solution\\\":\\\"请检查文件是否存在，或稍后重试\\\",\\
                \\\"details\\\":\\\"下载URL: \$DOWNLOAD_URL\\\",\\
                \\\"step\\\":\\\"download\\\"\\
              }\\
            }\"

          exit 1
        fi

        # 验证文件
        if [ ! -f \"app.ipa\" ] || [ ! -s \"app.ipa\" ]; then
          echo \"❌ IPA文件不存在或为空\"

          # 报告文件验证失败
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"failed\\\",\\
              \\\"error\\\":{\\
                \\\"type\\\":\\\"download_failed\\\",\\
                \\\"category\\\":\\\"download_error\\\",\\
                \\\"title\\\":\\\"IPA文件下载失败\\\",\\
                \\\"message\\\":\\\"下载的IPA文件不存在或为空\\\",\\
                \\\"solution\\\":\\\"请检查文件是否正确上传到服务器\\\",\\
                \\\"details\\\":\\\"文件验证失败：文件不存在或大小为0\\\",\\
                \\\"step\\\":\\\"download\\\"\\
              }\\
            }\"

          exit 1
        fi

        echo \"✅ IPA文件下载成功\"
        ls -la app.ipa
        file app.ipa

    - name: Upload to TestFlight
      run: |
        echo \"🚀 开始上传到TestFlight (使用Apple ID)...\"

        # 首先提取应用信息
        echo \"📱 提取应用信息...\"
        unzip -q app.ipa -d temp_ipa
        INFO_PLIST=\"temp_ipa/Payload/*.app/Info.plist\"

        BUNDLE_ID=\$(plutil -extract CFBundleIdentifier raw \$INFO_PLIST)
        VERSION=\$(plutil -extract CFBundleShortVersionString raw \$INFO_PLIST)
        BUILD=\$(plutil -extract CFBundleVersion raw \$INFO_PLIST)

        echo \"Bundle ID: \$BUNDLE_ID\"
        echo \"Version: \$VERSION\"
        echo \"Build: \$BUILD\"

        # 清理临时文件
        rm -rf temp_ipa

        # 报告开始上传状态
        curl -X POST \"{$callbackUrl}\" \\
          -H \"Content-Type: application/json\" \\
          -d '{\"record_id\":\"\${{ github.event.inputs.record_id }}\",\"status\":\"uploading\"}'

        # 使用完整的altool命令上传，包含所有必要参数
        echo \"🚀 开始上传到App Store Connect...\"
        xcrun altool --upload-app \\
          -f \"app.ipa\" \\
          -t ios \\
          -u \"\${{ secrets.APPLE_ID }}\" \\
          -p \"\${{ secrets.APP_PASSWORD }}\" \\
          --bundle-id \"\$BUNDLE_ID\" \\
          --bundle-short-version-string \"\$VERSION\" \\
          --bundle-version \"\$BUILD\" \\
          --verbose 2>&1 | tee upload_output.log

        ALTOOL_EXIT_CODE=\$?

        # 检查是否有错误，即使退出码为0
        # 先检查是否有明显的错误关键词
        if grep -q \"Error uploading\\|Failed to request\\|ContentDelivery Code=\\|IrisAPI Code=\\|ENTITY_ERROR\" upload_output.log; then
          echo \"❌ 检测到错误关键词，判定为失败\"
          UPLOAD_FAILED=true
        elif [ \$ALTOOL_EXIT_CODE -ne 0 ]; then
          echo \"❌ altool 退出码非零: \$ALTOOL_EXIT_CODE\"
          UPLOAD_FAILED=true
        else
          echo \"✅ 未检测到错误，判定为成功\"
          UPLOAD_FAILED=false
        fi

        if [ \"\$UPLOAD_FAILED\" = \"false\" ]; then
          echo \"✅ Upload succeeded!\"

          # 计算执行时长
          END_TIME=\$(date +%s)
          DURATION=\$((END_TIME - WORKFLOW_START_TIME))
          echo \"工作流执行时长: \${DURATION}秒\"

          # 报告成功（包含日志内容用于隐藏错误检测）
          UPLOAD_LOGS=\$(cat upload_output.log | base64 | tr -d '\\n')
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"success\\\",\\
              \\\"result\\\":{\\\"message\\\":\\\"Upload completed successfully\\\"},\\
              \\\"logs\\\":\\\"\$UPLOAD_LOGS\\\",\\
              \\\"workflow_ip\\\":\\\"\$WORKFLOW_IP\\\",\\
              \\\"workflow_duration\\\":\$DURATION\\
            }\"
        else
          echo \"❌ Upload failed, analyzing error...\"

          # 分析错误信息
          if grep -q \"bundle version must be higher\" upload_output.log || \\
             grep -q \"ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE\" upload_output.log || \\
             grep -q \"ContentDelivery Code=-19232\" upload_output.log || \\
             grep -q \"IrisAPI Code=-19241\" upload_output.log || \\
             grep -q \"previousBundleVersion\" upload_output.log; then
            ERROR_TYPE=\"version_exists\"
            ERROR_CATEGORY=\"version_error\"
            ERROR_TITLE=\"版本号重复\"
            ERROR_MSG=\"当前版本号已存在于App Store Connect\"
            ERROR_SOLUTION=\"请增加应用的Bundle Version (Build号)后重新上传\"

            # 尝试提取之前的版本号
            PREV_VERSION=\$(grep -o \"previousBundleVersion = [0-9]*\" upload_output.log | head -1 | cut -d' ' -f3)
            if [ ! -z \"\$PREV_VERSION\" ]; then
              ERROR_MSG=\"当前版本号已存在，之前上传的版本号是: \$PREV_VERSION\"
              ERROR_SOLUTION=\"请将Bundle Version (Build号)设置为大于 \$PREV_VERSION 的数字后重新上传\"
            fi

          elif grep -q \"authentication\" upload_output.log || grep -q \"password\" upload_output.log; then
            ERROR_TYPE=\"auth_failed\"
            ERROR_CATEGORY=\"auth_error\"
            ERROR_TITLE=\"认证失败\"
            ERROR_MSG=\"Apple ID或专属密码认证失败\"
            ERROR_SOLUTION=\"请检查Apple ID和专属密码是否正确\"

          else
            ERROR_TYPE=\"unknown\"
            ERROR_CATEGORY=\"upload_error\"
            ERROR_TITLE=\"上传失败\"
            ERROR_MSG=\"未知错误\"
            ERROR_SOLUTION=\"请检查详细日志信息\"
          fi

          # 提取关键错误信息
          ERROR_DETAILS=\$(grep -i \"error\\|failed\\|invalid\\|ContentDelivery\\|IrisAPI\" upload_output.log | head -5 | tr '\\n' '; ')

          # 提取最后一段完整的错误信息（从最后一个分隔符开始）
          FINAL_ERROR_SECTION=\$(awk '/^={30,}/ {section=\"\"} {section=section\"\\n\"\$0} END {print section}' upload_output.log | tail -c +2)

          # 如果没有找到分隔符，尝试提取最后的错误块
          if [ -z \"\$FINAL_ERROR_SECTION\" ] || [ \"\$FINAL_ERROR_SECTION\" = \"\\n\" ]; then
            FINAL_ERROR_SECTION=\$(tail -20 upload_output.log | grep -A 20 -B 5 \"Error\\|Failed\\|ContentDelivery\\|IrisAPI\" | head -20)
          fi

          echo \"🔍 错误类型: \$ERROR_TITLE\"
          echo \"💡 解决方案: \$ERROR_SOLUTION\"
          echo \"📋 详细错误: \$ERROR_DETAILS\"

          # 报告失败（使用Base64编码避免JSON转义问题）
          # 将详细错误信息进行Base64编码（兼容macOS和Linux）
          ERROR_DETAILS_B64=\$(echo \"\$ERROR_DETAILS\" | base64 | tr -d '\\n')
          RAW_ERROR_B64=\$(echo \"\$FINAL_ERROR_SECTION\" | base64 | tr -d '\\n')

          # 先发送错误详情（状态仍为processing，避免前端过早检测到失败）
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"processing\\\",\\
              \\\"error\\\":{\\
                \\\"type\\\":\\\"\$ERROR_TYPE\\\",\\
                \\\"category\\\":\\\"\$ERROR_CATEGORY\\\",\\
                \\\"title\\\":\\\"\$ERROR_TITLE\\\",\\
                \\\"message\\\":\\\"\$ERROR_MSG\\\",\\
                \\\"solution\\\":\\\"\$ERROR_SOLUTION\\\",\\
                \\\"details_base64\\\":\\\"\$ERROR_DETAILS_B64\\\",\\
                \\\"raw_error_base64\\\":\\\"\$RAW_ERROR_B64\\\",\\
                \\\"step\\\":\\\"upload\\\"\\
              }\\
            }\"

          # 等待1秒确保回调处理完成
          sleep 1

          # 计算执行时长
          END_TIME=\$(date +%s)
          DURATION=\$((END_TIME - WORKFLOW_START_TIME))
          echo \"工作流执行时长: \${DURATION}秒\"

          # 再发送最终失败状态
          curl -X POST \"{$callbackUrl}\" \\
            -H \"Content-Type: application/json\" \\
            -d \"{\\
              \\\"record_id\\\":\\\"\${{ github.event.inputs.record_id }}\\\",\\
              \\\"status\\\":\\\"failed\\\",\\
              \\\"workflow_ip\\\":\\\"\$WORKFLOW_IP\\\",\\
              \\\"workflow_duration\\\":\$DURATION\\
            }\"

          exit 1
        fi

    - name: Cleanup
      if: always()
      run: |
        rm -f app.ipa
        rm -rf temp_ipa
        echo \"清理完成\"
";
  }
}
