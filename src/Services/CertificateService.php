<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Models\Certificate;
use App\Services\ZsignService;

class CertificateService
{
    private AppConfig $config;
    private Certificate $certificateModel;
    private ZsignService $zsignService;
    private string $certificateStoragePath;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->certificateModel = new Certificate();
        $this->zsignService = new ZsignService();

        // 设置存储路径
        $basePath = $this->config->get('app_storage_path') ?? '/data/storage/';
        $this->certificateStoragePath = $basePath . '/certificates';

        // 确保目录存在
        $this->ensureDirectoriesExist();
    }

    /**
     * 上传P12证书
     */
    public function uploadP12Certificate(array $fileData, array $certificateInfo, string $userId): array
    {
        try {
            // 验证文件
            $validation = $this->validateP12File($fileData);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }

            // 保存文件
            $filename = $this->generateFilename($fileData['name'], 'p12');
            $filePath = $this->certificateStoragePath . '/' . $filename;

            // 复制文件到目标位置
            if (!copy($fileData['tmp_name'], $filePath)) {
                return [
                    'success' => false,
                    'error' => 'P12证书文件保存失败'
                ];
            }

            // 验证P12证书并提取信息
            $p12Info = $this->extractP12Info($filePath, $certificateInfo['password']);
            if (!$p12Info['success']) {
                @unlink($filePath); // 清理文件
                return $p12Info;
            }

            // 保存到数据库
            $certificateData = [
                'name' => $certificateInfo['name'],
                'type' => 'p12',
                'file_path' => $filePath,
                'file_size' => filesize($filePath),
                'uploaded_by' => $userId,
                'password' => $certificateInfo['password'], // 存储明文密码用于重签
                'password_hash' => password_hash($certificateInfo['password'], PASSWORD_DEFAULT), // 存储哈希用于验证
                'certificate_name' => $p12Info['certificate_name'] ?? ($p12Info['subject']['CN'] ?? null),
                'certificate_type' => $certificateInfo['certificate_type'] ?? 'distribution',
                'team_id' => $p12Info['team_id'] ?? null,
                'team_name' => $p12Info['team_name'] ?? null,
                'expires_at' => $p12Info['expires_at'] ?? ($p12Info['valid_to'] ?? null)
            ];

            $certificateId = $this->certificateModel->create($certificateData);

            return [
                'success' => true,
                'certificate_id' => (string)$certificateId,
                'certificate_info' => $p12Info
            ];
        } catch (\Exception $e) {
            error_log("P12证书上传失败: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'P12证书上传失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 上传mobileprovision文件
     */
    public function uploadMobileProvision(array $fileData, string $userId): array
    {
        try {
            // 验证文件
            $validation = $this->validateMobileProvisionFile($fileData);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }

            // 保存文件
            $filename = $this->generateFilename($fileData['name'], 'mobileprovision');
            $filePath = $this->certificateStoragePath . '/' . $filename;

            // 复制文件到目标位置
            if (!copy($fileData['tmp_name'], $filePath)) {
                return [
                    'success' => false,
                    'error' => 'mobileprovision文件保存失败'
                ];
            }

            // 解析mobileprovision文件
            $provisionInfo = $this->zsignService->parseMobileProvision($filePath);
            if (!$provisionInfo) {
                @unlink($filePath); // 清理文件
                return [
                    'success' => false,
                    'error' => 'mobileprovision文件解析失败'
                ];
            }

            // 保存到数据库
            $certificateData = [
                'name' => pathinfo($fileData['name'], PATHINFO_FILENAME),
                'type' => 'mobileprovision',
                'file_path' => $filePath,
                'file_size' => filesize($filePath),
                'uploaded_by' => $userId,
                'bundle_id' => $provisionInfo['entitlements']['application-identifier'] ?? null,
                'team_id' => $provisionInfo['team_identifier'][0] ?? null,
                'team_name' => $provisionInfo['team_name'],
                'provisioning_profile_name' => $provisionInfo['name'],
                'provisioning_profile_uuid' => $provisionInfo['uuid'],
                'provisioned_devices' => $provisionInfo['provisioned_devices'],
                'entitlements' => $provisionInfo['entitlements'],
                'expires_at' => isset($provisionInfo['expiration_date']) ? strtotime($provisionInfo['expiration_date']) : null
            ];

            $certificateId = $this->certificateModel->create($certificateData);

            return [
                'success' => true,
                'certificate_id' => (string)$certificateId,
                'provision_info' => $provisionInfo
            ];
        } catch (\Exception $e) {
            error_log("mobileprovision文件上传失败: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'mobileprovision文件上传失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取最佳证书对（用于自动重签）
     */
    public function getBestCertificatePair(?string $bundleId = null): ?array
    {
        $pairs = $this->certificateModel->getAvailableCertificatePairs();

        if (empty($pairs)) {
            return null;
        }

        // 如果指定了bundle ID，优先匹配
        if ($bundleId) {
            foreach ($pairs as $pair) {
                if ($pair['bundle_id'] === $bundleId) {
                    return $pair;
                }
            }
        }

        // 返回最晚过期的证书对
        return $pairs[0];
    }

    /**
     * 验证P12文件
     */
    private function validateP12File(array $fileData): array
    {
        if ($fileData['error'] !== UPLOAD_ERR_OK) {
            $errorMessages = [
                UPLOAD_ERR_INI_SIZE => '文件大小超过PHP配置限制',
                UPLOAD_ERR_FORM_SIZE => '文件大小超过表单限制',
                UPLOAD_ERR_PARTIAL => '文件只上传了一部分',
                UPLOAD_ERR_NO_FILE => '没有文件被上传',
                UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
                UPLOAD_ERR_CANT_WRITE => '文件写入失败',
                UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
            ];

            return [
                'valid' => false,
                'error' => $errorMessages[$fileData['error']] ?? '文件上传失败'
            ];
        }

        if (!file_exists($fileData['tmp_name'])) {
            return [
                'valid' => false,
                'error' => '上传的临时文件不存在'
            ];
        }

        $allowedExtensions = ['p12', 'pfx'];
        $extension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));

        if (!in_array($extension, $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => '只支持.p12或.pfx格式的证书文件'
            ];
        }

        if ($fileData['size'] > 10 * 1024 * 1024) { // 10MB限制
            return [
                'valid' => false,
                'error' => '证书文件大小不能超过10MB'
            ];
        }

        if ($fileData['size'] < 100) { // 最小大小检查
            return [
                'valid' => false,
                'error' => 'P12证书文件过小，可能文件损坏'
            ];
        }

        return ['valid' => true];
    }

    /**
     * 验证mobileprovision文件
     */
    private function validateMobileProvisionFile(array $fileData): array
    {
        if ($fileData['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'error' => '文件上传失败'
            ];
        }

        $extension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));

        if ($extension !== 'mobileprovision') {
            return [
                'valid' => false,
                'error' => '只支持.mobileprovision格式的文件'
            ];
        }

        if ($fileData['size'] > 1024 * 1024) { // 1MB限制
            return [
                'valid' => false,
                'error' => 'mobileprovision文件大小不能超过1MB'
            ];
        }

        return ['valid' => true];
    }

    /**
     * 提取P12证书信息
     */
    private function extractP12Info(string $filePath, string $password): array
    {
        try {
            $p12Content = file_get_contents($filePath);
            $certs = [];

            if (!openssl_pkcs12_read($p12Content, $certs, $password)) {
                $opensslError = openssl_error_string();

                // 如果是OpenSSL 3.x的兼容性问题，尝试使用外部工具
                if (
                    strpos($opensslError, 'digital envelope routines') !== false ||
                    strpos($opensslError, 'unsupported') !== false
                ) {
                    return $this->extractP12InfoWithExternalTool($filePath, $password);
                }

                // 提供更详细的错误信息
                $errorMsg = 'P12证书处理失败';
                if (strlen($p12Content) < 100) {
                    $errorMsg .= '：文件内容过短，可能文件损坏';
                } elseif (strpos($opensslError, 'mac verify failure') !== false) {
                    $errorMsg .= '：密码错误';
                } elseif (strpos($opensslError, 'invalid') !== false) {
                    $errorMsg .= '：文件格式无效';
                } else {
                    $errorMsg .= '：' . ($opensslError ?: '密码错误或文件损坏');
                }

                return [
                    'success' => false,
                    'error' => $errorMsg
                ];
            }



            // 解析证书信息
            $certInfo = openssl_x509_parse($certs['cert']);

            return [
                'success' => true,
                'certificate_name' => $certInfo['subject']['CN'] ?? 'Unknown',
                'team_id' => $this->extractTeamIdFromCert($certInfo),
                'team_name' => $certInfo['subject']['O'] ?? 'Unknown',
                'expires_at' => $certInfo['validTo_time_t'] ?? null
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'P12证书解析失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 从证书中提取Team ID
     */
    private function extractTeamIdFromCert(array $certInfo): ?string
    {
        // Team ID通常在证书的OU字段中
        if (isset($certInfo['subject']['OU'])) {
            $ou = $certInfo['subject']['OU'];
            if (is_array($ou)) {
                // 查找10位字符的Team ID
                foreach ($ou as $unit) {
                    if (preg_match('/^[A-Z0-9]{10}$/', $unit)) {
                        return $unit;
                    }
                }
            } elseif (preg_match('/^[A-Z0-9]{10}$/', $ou)) {
                return $ou;
            }
        }

        return null;
    }

    /**
     * 生成文件名
     */
    private function generateFilename(string $originalName, string $type): string
    {
        $timestamp = date('Y-m-d_H-i-s');
        $basename = pathinfo($originalName, PATHINFO_FILENAME);
        $extension = $type === 'p12' ? 'p12' : 'mobileprovision';

        return $basename . '_' . $timestamp . '.' . $extension;
    }

    /**
     * 确保存储目录存在
     */
    private function ensureDirectoriesExist(): void
    {
        if (!is_dir($this->certificateStoragePath)) {
            mkdir($this->certificateStoragePath, 0755, true);
        }
    }

    /**
     * 获取所有证书列表（管理员用）- 排除CSR记录
     */
    public function getAllCertificates(): array
    {
        // 获取P12和mobileprovision证书，排除CSR
        $p12Certificates = $this->certificateModel->findByType('p12');
        $mobileProvisionCertificates = $this->certificateModel->findByType('mobileprovision');

        // 合并并按创建时间排序
        $allCertificates = array_merge($p12Certificates, $mobileProvisionCertificates);

        // 按创建时间倒序排序
        usort($allCertificates, function ($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return $allCertificates;
    }

    /**
     * 删除证书
     */
    public function deleteCertificate(string $certificateId): array
    {
        try {
            $certificate = $this->certificateModel->findById($certificateId);

            if (!$certificate) {
                return [
                    'success' => false,
                    'error' => '证书不存在'
                ];
            }

            // 删除文件
            if (file_exists($certificate['file_path'])) {
                @unlink($certificate['file_path']);
            }

            // 如果是CSR，还要删除私钥文件
            if ($certificate['type'] === 'csr' && !empty($certificate['private_key_path'])) {
                if (file_exists($certificate['private_key_path'])) {
                    @unlink($certificate['private_key_path']);
                }
            }

            // 从数据库删除
            $this->certificateModel->delete($certificateId);

            return [
                'success' => true,
                'message' => '证书删除成功'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '证书删除失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 使用外部工具提取P12证书信息（解决OpenSSL 3.x兼容性问题）
     */
    private function extractP12InfoWithExternalTool(string $filePath, string $password): array
    {
        try {

            // 创建临时目录
            $tempDir = sys_get_temp_dir() . '/p12_extract_' . uniqid();
            mkdir($tempDir, 0700, true);

            $certFile = $tempDir . '/cert.pem';

            // 使用openssl命令行工具提取证书
            // 先尝试使用legacy provider
            $extractCertCmd = sprintf(
                'openssl pkcs12 -in %s -clcerts -nokeys -out %s -passin pass:%s -legacy 2>/dev/null',
                escapeshellarg($filePath),
                escapeshellarg($certFile),
                escapeshellarg($password)
            );

            exec($extractCertCmd, $output, $returnCode);

            if ($returnCode !== 0) {
                // 如果legacy选项不支持，尝试不使用legacy
                $extractCertCmd = sprintf(
                    'openssl pkcs12 -in %s -clcerts -nokeys -out %s -passin pass:%s 2>/dev/null',
                    escapeshellarg($filePath),
                    escapeshellarg($certFile),
                    escapeshellarg($password)
                );

                exec($extractCertCmd, $output, $returnCode);
            }

            if ($returnCode !== 0 || !file_exists($certFile)) {
                $this->cleanupTempDir($tempDir);
                return [
                    'success' => false,
                    'error' => 'P12证书密码错误或文件损坏'
                ];
            }

            // 读取证书内容并解析
            $certContent = file_get_contents($certFile);
            $certData = openssl_x509_parse($certContent);

            if (!$certData) {
                $this->cleanupTempDir($tempDir);
                return [
                    'success' => false,
                    'error' => '证书解析失败'
                ];
            }

            // 清理临时文件
            $this->cleanupTempDir($tempDir);



            return [
                'success' => true,
                'certificate_name' => $certData['subject']['CN'] ?? 'Unknown',
                'team_id' => $this->extractTeamIdFromCert($certData),
                'team_name' => $certData['subject']['O'] ?? 'Unknown',
                'expires_at' => $certData['validTo_time_t'] ?? null
            ];
        } catch (\Exception $e) {
            error_log("外部工具提取P12证书信息异常: " . $e->getMessage());
            if (isset($tempDir) && is_dir($tempDir)) {
                $this->cleanupTempDir($tempDir);
            }
            return [
                'success' => false,
                'error' => '证书信息提取失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 清理临时目录
     */
    private function cleanupTempDir(string $tempDir): void
    {
        if (is_dir($tempDir)) {
            $files = array_diff(scandir($tempDir), ['.', '..']);
            foreach ($files as $file) {
                $filePath = $tempDir . '/' . $file;
                if (is_file($filePath)) {
                    unlink($filePath);
                }
            }
            rmdir($tempDir);
        }
    }
}
