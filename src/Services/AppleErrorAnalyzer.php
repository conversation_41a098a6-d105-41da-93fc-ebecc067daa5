<?php

namespace App\Services;

class AppleErrorAnalyzer
{
    /**
     * 分析Apple上传日志中的错误信息
     */
    public static function analyzeError(string $logContent): array
    {
        $errorInfo = [
            'type' => 'unknown',
            'category' => 'upload_error',
            'title' => '上传失败',
            'message' => '工作流执行失败',
            'solution' => '请检查日志信息',
            'details' => '',
            'raw_error' => '',
            'is_retryable' => false,
            'step' => 'upload'
        ];

        // 首先尝试提取最后一段完整的错误信息
        $finalErrorSection = self::extractFinalErrorSection($logContent);
        if (!empty($finalErrorSection)) {
            $errorInfo['raw_error'] = $finalErrorSection;
            $errorInfo['details'] = $finalErrorSection;
        }

        // 然后按优先级检查各种错误类型进行分类
        $patterns = self::getErrorPatterns();

        foreach ($patterns as $pattern) {
            if (self::matchesPattern($logContent, $pattern['patterns'])) {
                $errorInfo = array_merge($errorInfo, $pattern['info']);
                // 如果没有提取到完整错误段，使用传统方式提取详情
                if (empty($errorInfo['raw_error'])) {
                    $errorInfo['details'] = self::extractErrorDetails($logContent, $pattern['detail_patterns'] ?? []);
                }
                break;
            }
        }

        return $errorInfo;
    }

    /**
     * 获取错误模式定义
     */
    private static function getErrorPatterns(): array
    {
        return [
            // Apple错误代码相关 - 版本重复错误
            [
                'patterns' => [
                    '"code".*90062',
                    '"code".*90063',
                    'bundle version must be higher',
                    'ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE',
                    'version already exists',
                    'duplicate version',
                    'ContentDelivery Code=-19232',
                    'IrisAPI Code=-19241',
                    'iris-code.*ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE',
                    'The provided entity includes an attribute with a value that has already been used',
                    'previousBundleVersion'
                ],
                'info' => [
                    'type' => 'version_exists',
                    'category' => 'version_error',
                    'title' => '版本号重复',
                    'message' => '当前版本号已存在于App Store Connect',
                    'solution' => '请增加应用的Bundle Version (Build号)后重新上传',
                    'is_retryable' => false,
                    'step' => 'upload'
                ],
                'detail_patterns' => ['Version Number:', 'Bundle Version:', 'build', 'code', 'previousBundleVersion', 'cfBundleVersion']
            ],

            // 认证相关错误
            [
                'patterns' => [
                    '"code".*90014',
                    '"code".*90015',
                    '"code".*90016',
                    '"code".*90017',
                    '"code".*90018',
                    '"code".*90019',
                    'authentication failed',
                    'Invalid API Key',
                    'API key.*invalid',
                    'Unauthorized',
                    'Invalid credentials'
                ],
                'info' => [
                    'type' => 'auth_failed',
                    'category' => 'auth_error',
                    'title' => '认证失败',
                    'message' => 'API Key或Apple ID认证失败 (错误代码: 90014-90019)',
                    'solution' => '请检查API Key ID、Issuer ID和私钥内容，或Apple ID和专属密码是否正确',
                    'is_retryable' => false,
                    'step' => 'authentication'
                ],
                'detail_patterns' => ['API Key', 'Issuer', 'authentication', 'code']
            ],

            // 签名相关错误
            [
                'patterns' => [
                    '"code".*90125',
                    '"code".*90126',
                    '"code".*90127',
                    '"code".*90161',
                    '"code".*90162',
                    'does not contain a valid signature',
                    'Invalid signature',
                    'code signing',
                    'provisioning profile',
                    'certificate'
                ],
                'info' => [
                    'type' => 'signature_error',
                    'category' => 'signing_error',
                    'title' => '签名问题',
                    'message' => 'IPA文件签名无效或证书问题 (错误代码: 90125-90127, 90161-90162)',
                    'solution' => '请检查应用签名和证书配置，确保使用正确的Provisioning Profile和有效的开发者证书',
                    'is_retryable' => false,
                    'step' => 'validation'
                ],
                'detail_patterns' => ['signature', 'certificate', 'provisioning', 'code']
            ],

            // IPA文件相关错误
            [
                'patterns' => [
                    '"code".*90087',
                    '"code".*90088',
                    '"code".*90089',
                    '"code".*90090',
                    'Invalid bundle',
                    'corrupted',
                    'not a valid IPA',
                    'archive is invalid',
                    'malformed'
                ],
                'info' => [
                    'type' => 'invalid_ipa',
                    'category' => 'file_error',
                    'title' => '无效的IPA文件',
                    'message' => 'IPA文件损坏或格式不正确 (错误代码: 90087-90090)',
                    'solution' => '请重新生成IPA文件，确保文件完整性和正确的应用结构',
                    'is_retryable' => false,
                    'step' => 'validation'
                ],
                'detail_patterns' => ['bundle', 'archive', 'IPA', 'code']
            ],

            // 网络相关错误
            [
                'patterns' => [
                    'network error',
                    'connection timeout',
                    'failed to connect',
                    'network is unreachable',
                    'timeout'
                ],
                'info' => [
                    'type' => 'network_error',
                    'category' => 'network_error',
                    'title' => '网络连接问题',
                    'message' => '网络连接超时或不稳定',
                    'solution' => '请检查网络连接，稍后重试',
                    'is_retryable' => true,
                    'step' => 'upload'
                ],
                'detail_patterns' => ['network', 'connection', 'timeout']
            ],

            // 下载相关错误
            [
                'patterns' => [
                    'Failed to download',
                    'download.*failed',
                    'curl.*failed',
                    '404.*Not Found',
                    'File not found'
                ],
                'info' => [
                    'type' => 'download_failed',
                    'category' => 'download_error',
                    'title' => 'IPA文件下载失败',
                    'message' => '无法从服务器下载IPA文件',
                    'solution' => '请检查文件是否存在，或稍后重试',
                    'is_retryable' => true,
                    'step' => 'download'
                ],
                'detail_patterns' => ['download', 'curl', 'URL']
            ],

            // 服务器相关错误
            [
                'patterns' => [
                    'Internal Server Error',
                    '500',
                    'Service Unavailable',
                    '503',
                    'server error'
                ],
                'info' => [
                    'type' => 'server_error',
                    'category' => 'server_error',
                    'title' => '服务器错误',
                    'message' => 'Apple服务器暂时不可用',
                    'solution' => '这是Apple服务器的问题，请稍后重试',
                    'is_retryable' => true,
                    'step' => 'upload'
                ],
                'detail_patterns' => ['server', 'error', 'status']
            ]
        ];
    }

    /**
     * 检查日志内容是否匹配错误模式
     */
    private static function matchesPattern(string $logContent, array $patterns): bool
    {
        foreach ($patterns as $pattern) {
            if (preg_match('/' . preg_quote($pattern, '/') . '/i', $logContent)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 提取错误详细信息
     */
    private static function extractErrorDetails(string $logContent, array $detailPatterns): string
    {
        $details = [];
        $lines = explode("\n", $logContent);

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // 提取包含关键词的行
            foreach ($detailPatterns as $pattern) {
                if (stripos($line, $pattern) !== false) {
                    $details[] = $line;
                    break;
                }
            }

            // 提取错误消息行
            if (preg_match('/error|failed|invalid|duplicate/i', $line) && !in_array($line, $details)) {
                $details[] = $line;
            }
        }

        // 限制详细信息长度
        $details = array_slice($details, 0, 5);
        return implode("\n", $details);
    }

    /**
     * 分析工作流步骤错误
     */
    public static function analyzeWorkflowStepError(string $step, string $logContent): array
    {
        $baseError = self::analyzeError($logContent);
        $baseError['step'] = $step;

        // 根据步骤调整错误信息
        switch ($step) {
            case 'download':
                if ($baseError['type'] === 'unknown') {
                    $baseError['type'] = 'download_failed';
                    $baseError['category'] = 'download_error';
                    $baseError['title'] = 'IPA下载失败';
                    $baseError['message'] = '无法下载IPA文件';
                    $baseError['solution'] = '请检查文件路径和网络连接';
                }
                break;

            case 'validation':
                if ($baseError['type'] === 'unknown') {
                    $baseError['title'] = '应用验证失败';
                    $baseError['message'] = 'IPA文件验证未通过';
                    $baseError['solution'] = '请检查应用签名和配置';
                }
                break;

            case 'upload':
                if ($baseError['type'] === 'unknown') {
                    $baseError['title'] = '上传到TestFlight失败';
                    $baseError['message'] = '无法上传到App Store Connect';
                    $baseError['solution'] = '请检查网络连接和认证信息';
                }
                break;
        }

        return $baseError;
    }

    /**
     * 检测是否存在隐藏的错误（即使有成功消息）
     * 用于处理altool有时返回0退出码但实际有错误的情况
     */
    public static function hasHiddenErrors(string $logContent): bool
    {
        $errorIndicators = [
            'Error uploading',
            'Failed to request',
            'ContentDelivery Code=-19232',
            'IrisAPI Code=-19241',
            'ENTITY_ERROR.ATTRIBUTE.INVALID.DUPLICATE',
            '*** Error:',
            'NSLocalizedFailureReason',
            'bundle version must be higher',
            'previousBundleVersion'
        ];

        foreach ($errorIndicators as $indicator) {
            if (stripos($logContent, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 提取版本号信息
     */
    public static function extractVersionInfo(string $logContent): array
    {
        $versionInfo = [
            'previous_version' => null,
            'current_version' => null,
            'error_id' => null
        ];

        // 提取之前的版本号
        if (preg_match('/previousBundleVersion = (\d+)/', $logContent, $matches)) {
            $versionInfo['previous_version'] = $matches[1];
        }

        // 提取当前版本号
        if (preg_match('/bundle version.*?(\d+)/', $logContent, $matches)) {
            $versionInfo['current_version'] = $matches[1];
        }

        // 提取错误ID
        if (preg_match('/ID: ([a-f0-9-]+)/', $logContent, $matches)) {
            $versionInfo['error_id'] = $matches[1];
        }

        return $versionInfo;
    }

    /**
     * 提取最后一段完整的错误信息
     * 按照Apple日志的规律：前面是验证过程，最后一段是最终结果
     */
    private static function extractFinalErrorSection(string $logContent): string
    {
        // 按照分隔符分割日志
        $sections = preg_split('/={30,}/', $logContent);

        if (count($sections) < 2) {
            // 如果没有分隔符，尝试查找最后的错误段
            return self::extractLastErrorBlock($logContent);
        }

        // 获取最后一段
        $lastSection = trim(end($sections));

        // 检查最后一段是否包含错误信息
        if (self::containsErrorInfo($lastSection)) {
            return $lastSection;
        }

        // 如果最后一段不是错误信息，查找倒数第二段
        if (count($sections) >= 3) {
            $secondLastSection = trim($sections[count($sections) - 2]);
            if (self::containsErrorInfo($secondLastSection)) {
                return $secondLastSection;
            }
        }

        // 备用方案：查找最后的错误块
        return self::extractLastErrorBlock($logContent);
    }

    /**
     * 检查文本段是否包含错误信息
     */
    private static function containsErrorInfo(string $text): bool
    {
        $errorIndicators = [
            'Failed to request',
            '*** Error:',
            'Error uploading',
            'ContentDelivery Code=',
            'IrisAPI Code=',
            'NSLocalizedFailureReason',
            'ENTITY_ERROR',
            'bundle version must be higher',
            'authentication failed',
            'Invalid signature',
            'does not contain a valid signature'
        ];

        foreach ($errorIndicators as $indicator) {
            if (stripos($text, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 提取最后的错误块（备用方案）
     */
    private static function extractLastErrorBlock(string $logContent): string
    {
        $lines = explode("\n", $logContent);
        $errorLines = [];
        $inErrorBlock = false;

        // 从后往前查找错误信息
        for ($i = count($lines) - 1; $i >= 0; $i--) {
            $line = trim($lines[$i]);

            if (empty($line)) {
                if ($inErrorBlock && !empty($errorLines)) {
                    // 遇到空行且已经收集了错误信息，停止
                    break;
                }
                continue;
            }

            // 检查是否是错误相关的行
            if (
                self::containsErrorInfo($line) ||
                preg_match('/^\s*["{]/', $line) || // JSON 或字典格式
                preg_match('/^\s*\w+\s*=/', $line) || // 键值对格式
                preg_match('/^\s*}/', $line) || // 结束括号
                preg_match('/^\d{4}-\d{2}-\d{2}.*Error/', $line)
            ) { // 时间戳+错误

                array_unshift($errorLines, $line);
                $inErrorBlock = true;
            } else if ($inErrorBlock) {
                // 如果已经在错误块中，遇到非错误行就停止
                break;
            }
        }

        return implode("\n", $errorLines);
    }
}
