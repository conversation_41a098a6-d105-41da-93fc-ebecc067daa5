<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Models\DistributionRecord;
use App\Models\ResignRecord;
// IpaParser removed - parsing now handled client-side

class DistributionService
{
    private AppConfig $config;
    private DistributionRecord $distributionModel;
    private ResignRecord $resignRecordModel;
    private string $plistStoragePath;
    private string $qrCodeStoragePath;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->distributionModel = new DistributionRecord();
        $this->resignRecordModel = new ResignRecord();

        // 设置存储路径
        $basePath = $this->config->get('app_storage_path') ?? '/data/storage/';
        $this->plistStoragePath = $basePath . '/plists';
        $this->qrCodeStoragePath = $basePath . '/qrcodes';

        // 确保目录存在
        $this->ensureDirectoriesExist();
    }

    /**
     * 为重签记录创建分发链接
     */
    public function createDistribution(string $resignRecordId, array $distributionConfig = []): array
    {
        try {
            $resignRecord = $this->resignRecordModel->findById($resignRecordId);
            if (!$resignRecord) {
                return [
                    'success' => false,
                    'error' => '重签记录不存在'
                ];
            }

            if ($resignRecord['status'] !== 'success') {
                return [
                    'success' => false,
                    'error' => '重签未完成，无法创建分发链接'
                ];
            }

            // 检查是否已存在分发记录
            $existingDistribution = $this->distributionModel->findByResignRecordId($resignRecordId);
            if ($existingDistribution) {
                return [
                    'success' => true,
                    'distribution_id' => $existingDistribution['_id'],
                    'install_url' => $existingDistribution['install_url'],
                    'plist_url' => $existingDistribution['plist_url'],
                    'qr_code_url' => $existingDistribution['qr_code_url']
                ];
            }

            // 处理应用名称和Bundle ID，确保不为空
            $appName = !empty($resignRecord['new_app_name']) ? $resignRecord['new_app_name'] : $resignRecord['app_name'];
            $bundleId = !empty($resignRecord['new_bundle_id']) ? $resignRecord['new_bundle_id'] : $resignRecord['bundle_id'];

            // 确保关键字段不为空
            if (empty($appName)) {
                $appName = $resignRecord['original_filename'] ?? 'Unknown App';
            }
            if (empty($bundleId)) {
                $bundleId = 'com.unknown.app';
            }

            // 先创建分发记录以获取ObjectId
            $distributionData = [
                'resign_record_id' => $resignRecordId,
                'user_id' => $resignRecord['user_id'],
                'app_name' => $appName,
                'bundle_id' => $bundleId,
                'version' => $resignRecord['version'],
                'build' => $resignRecord['build'],
                'icon_base64' => $resignRecord['icon_base64'] ?? null,
                'title' => $distributionConfig['title'] ?? $appName,
                'subtitle' => $distributionConfig['subtitle'] ?? null,
                'description' => $distributionConfig['description'] ?? null,
                'minimum_os_version' => $distributionConfig['minimum_os_version'] ?? '10.0',
                'expires_at' => isset($distributionConfig['expires_in_days']) ?
                    time() + ($distributionConfig['expires_in_days'] * 24 * 60 * 60) : null,
                // 临时占位符，稍后更新
                'install_url' => '',
                'plist_url' => '',
                'download_url' => '',
                'qr_code_url' => null
            ];

            $recordId = $this->distributionModel->create($distributionData);
            $distributionId = (string)$recordId;

            // 使用ObjectId生成分发链接
            $apiUrl = $this->config->get('api_url');
            $downloadBaseUrl = $this->config->get('download_url');

            $installUrl = "{$apiUrl}/api/distribution/install/{$distributionId}";
            $plistUrl = "{$apiUrl}/api/distribution/plist/{$distributionId}";

            // IPA文件的直接下载URL（用于plist文件中）
            $ipaFileName = basename($resignRecord['resigned_ipa_path']);
            $directDownloadUrl = "{$downloadBaseUrl}/resigned_ipas/{$ipaFileName}";

            // API下载URL（用于统计和权限控制）
            $apiDownloadUrl = "{$apiUrl}/api/distribution/download/{$distributionId}";

            // 生成plist文件（使用直接下载URL）
            $plistResult = $this->generatePlistFile($resignRecord, $distributionId, $directDownloadUrl, $distributionConfig);
            if (!$plistResult['success']) {
                return $plistResult;
            }

            // 二维码现在由前端生成，不需要后端处理
            $qrCodeUrl = null;

            // 更新分发记录的URL信息
            $this->distributionModel->updateUrls($distributionId, [
                'install_url' => $installUrl,
                'plist_url' => $plistUrl,
                'download_url' => $apiDownloadUrl, // API下载URL用于统计
                'direct_download_url' => $directDownloadUrl, // 直接下载URL用于plist
                'qr_code_url' => $qrCodeUrl
            ]);

            return [
                'success' => true,
                'distribution_id' => (string)$recordId,
                'install_url' => $installUrl,
                'plist_url' => $plistUrl,
                'download_url' => $apiDownloadUrl,
                'direct_download_url' => $directDownloadUrl,
                'qr_code_url' => $qrCodeUrl
            ];
        } catch (\Exception $e) {
            error_log("创建分发链接失败: " . $e->getMessage());
            return [
                'success' => false,
                'error' => '创建分发链接失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成plist文件
     */
    private function generatePlistFile(array $resignRecord, string $distributionId, string $downloadUrl, array $config): array
    {
        try {
            // 优先使用配置中的title，然后是new_app_name（如果不为空），最后是原始app_name
            $appName = $config['title'] ??
                (!empty($resignRecord['new_app_name']) ? $resignRecord['new_app_name'] : $resignRecord['app_name']);

            // 优先使用new_bundle_id（如果不为空），否则使用原始bundle_id
            $bundleId = !empty($resignRecord['new_bundle_id']) ? $resignRecord['new_bundle_id'] : $resignRecord['bundle_id'];

            $version = $resignRecord['version'] ?? '1.0';
            $minimumOsVersion = $config['minimum_os_version'] ?? '10.0';

            // 确保关键字段不为空
            if (empty($appName)) {
                $appName = $resignRecord['original_filename'] ?? 'Unknown App';
            }
            if (empty($bundleId)) {
                $bundleId = 'com.unknown.app';
            }

            $plistContent = $this->generatePlistContent([
                'app_name' => $appName,
                'bundle_id' => $bundleId,
                'version' => $version,
                'download_url' => $downloadUrl,
                'minimum_os_version' => $minimumOsVersion,
                'subtitle' => $config['subtitle'] ?? null
            ]);

            $plistPath = $this->plistStoragePath . '/' . $distributionId . '.plist';

            // 确保plist目录存在且可写
            if (!is_dir($this->plistStoragePath)) {
                if (!mkdir($this->plistStoragePath, 0775, true)) {
                    return [
                        'success' => false,
                        'error' => 'plist存储目录创建失败'
                    ];
                }
            }

            if (!is_writable($this->plistStoragePath)) {
                chmod($this->plistStoragePath, 0775);
            }

            if (!file_put_contents($plistPath, $plistContent)) {
                $error = error_get_last();
                return [
                    'success' => false,
                    'error' => 'plist文件生成失败: ' . ($error['message'] ?? '未知错误')
                ];
            }

            // 设置文件权限
            chmod($plistPath, 0644);

            return [
                'success' => true,
                'plist_path' => $plistPath
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'plist文件生成异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成plist内容
     */
    private function generatePlistContent(array $appInfo): string
    {
        $subtitle = $appInfo['subtitle'] ? "<string>{$appInfo['subtitle']}</string>" : "<string></string>";

        return <<<PLIST
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>items</key>
    <array>
        <dict>
            <key>assets</key>
            <array>
                <dict>
                    <key>kind</key>
                    <string>software-package</string>
                    <key>url</key>
                    <string>{$appInfo['download_url']}</string>
                </dict>
            </array>
            <key>metadata</key>
            <dict>
                <key>bundle-identifier</key>
                <string>{$appInfo['bundle_id']}</string>
                <key>bundle-version</key>
                <string>{$appInfo['version']}</string>
                <key>kind</key>
                <string>software</string>
                <key>platform-identifier</key>
                <string>com.apple.platform.iphoneos</string>
                <key>title</key>
                <string>{$appInfo['app_name']}</string>
                <key>subtitle</key>
                {$subtitle}
                <key>minimum-os-version</key>
                <string>{$appInfo['minimum_os_version']}</string>
            </dict>
        </dict>
    </array>
</dict>
</plist>
PLIST;
    }



    /**
     * 获取分发记录（包含重签记录数据）
     */
    public function getDistribution(string $distributionId): ?array
    {
        $distribution = $this->distributionModel->findById($distributionId);
        if (!$distribution) {
            return null;
        }

        // 获取关联的重签记录以获取完整信息
        $resignRecord = $this->resignRecordModel->findById($distribution['resign_record_id']);
        if ($resignRecord) {
            // 将重签记录中的重要信息合并到分发记录中
            $distribution['resign_record'] = $resignRecord;
            // 确保图标数据可用
            if (!empty($resignRecord['icon_base64'])) {
                $distribution['icon_base64'] = $resignRecord['icon_base64'];
            }
        }

        return $distribution;
    }

    /**
     * 获取plist文件内容
     */
    public function getPlistContent(string $distributionId): array
    {
        try {
            $plistPath = $this->plistStoragePath . '/' . $distributionId . '.plist';

            if (!file_exists($plistPath)) {
                return [
                    'success' => false,
                    'error' => 'plist文件不存在'
                ];
            }

            return [
                'success' => true,
                'content' => file_get_contents($plistPath),
                'content_type' => 'application/x-plist'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'plist文件读取失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 记录安装统计
     */
    public function recordInstall(string $distributionId, array $metadata = []): bool
    {
        return $this->distributionModel->updateInstallStats($distributionId, $metadata);
    }

    /**
     * 记录查看统计
     */
    public function recordView(string $distributionId): bool
    {
        return $this->distributionModel->updateViewStats($distributionId);
    }



    /**
     * 确保存储目录存在
     */
    private function ensureDirectoriesExist(): void
    {
        $directories = [$this->plistStoragePath, $this->qrCodeStoragePath];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0775, true)) {
                    throw new \Exception("无法创建目录: {$dir}");
                }
            }

            // 确保目录可写
            if (!is_writable($dir)) {
                chmod($dir, 0775);
            }
        }
    }

    /**
     * 获取用户的分发记录
     */
    public function getUserDistributions(string $userId, int $limit = 20, int $skip = 0): array
    {
        return $this->distributionModel->getUserRecords($userId, $limit, $skip);
    }

    /**
     * 获取分发统计
     */
    public function getDistributionStats(): array
    {
        return $this->distributionModel->getDistributionStats();
    }

    /**
     * 获取热门应用
     */
    public function getPopularApps(int $limit = 10): array
    {
        return $this->distributionModel->getPopularApps($limit);
    }
}
