<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Models\UploadRecord;
use App\Models\GitHubAccount;
use App\Utils\WorkflowGenerator;

class UploadService
{
    private AppConfig $config;
    private UploadRecord $uploadRecordModel;
    private GitHubAccount $githubAccountModel;

    private GitHubService $githubService;
    private WorkflowGenerator $workflowGenerator;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->uploadRecordModel = new UploadRecord();
        $this->githubAccountModel = new GitHubAccount();

        $this->githubService = new GitHubService();
        $this->workflowGenerator = new WorkflowGenerator();
    }

    /**
     * 处理IPA文件上传
     */
    public function uploadIPA(array $fileData, array $authData, string $userId): array
    {
        try {
            // 验证文件
            $validation = $this->validateUploadFile($fileData);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }

            // 验证认证信息
            $authValidation = $this->validateAuthData($authData);
            if (!$authValidation['valid']) {
                return [
                    'success' => false,
                    'error' => $authValidation['error']
                ];
            }

            // 保存文件到临时目录
            $tempPath = $this->saveTempFile($fileData);
            if (!$tempPath) {
                return [
                    'success' => false,
                    'error' => '文件保存失败'
                ];
            }

            // 注意：IPA解析已移至客户端，这里不再解析
            // 应用信息应该通过authData传入

            // 获取最优GitHub账号
            $githubAccount = $this->githubService->getBestAccount();
            if (!$githubAccount) {
                return [
                    'success' => false,
                    'error' => '没有可用的GitHub账号'
                ];
            }

            // 从认证数据中获取应用信息（客户端解析的）
            $bundleId = $authData['bundle_id'] ?? 'unknown.bundle.id';
            $appName = $authData['app_name'] ?? $fileData['name'];

            // 生成存储文件名
            $storedFilename = $this->generateStoredFilename($bundleId);

            // 移动文件到最终位置
            $finalPath = $this->config->get('ipa_storage_path') . '/' . $storedFilename;
            if (!rename($tempPath, $finalPath)) {
                return [
                    'success' => false,
                    'error' => '文件移动失败'
                ];
            }

            // 创建上传记录 - 适配新的create方法格式
            $recordData = [
                'user_id' => $userId,
                'filename' => $fileData['name'],
                'file_size' => $fileData['size'],
                'auth_method' => $authData['method'],
                'auth_data' => $authData,
                'release_notes' => '',
                'status' => 'queued',

                // 临时文件路径
                'temp_file_path' => $finalPath,

                // 应用信息
                'bundle_id' => $bundleId,
                'app_name' => $appName,
                'version' => $authData['version'] ?? '1.0.0',
                'build' => $authData['build'] ?? '1',
                'minimum_os_version' => $authData['minimum_os_version'] ?? 'Unknown',
                'supported_devices' => $authData['supported_devices'] ?? [],
                'required_capabilities' => $authData['required_capabilities'] ?? [],
                'icon_base64' => $authData['icon_base64'] ?? null,
                'parsed_info' => $authData['app_info'] ?? null,
                'parsing_error' => $authData['parsing_error'] ?? null,

                // 旧格式的额外字段（用于兼容性）
                'stored_filename' => $storedFilename,
                'github_account' => $githubAccount['_id'],
                'queue_position' => $this->getNextQueuePosition()
            ];

            $recordId = $this->uploadRecordModel->create($recordData);

            // 检查是否可以立即开始上传
            if ($this->canStartUpload()) {
                $this->processUploadQueue();
            }

            return [
                'success' => true,
                'record_id' => (string) $recordId,
                'queue_position' => $recordData['queue_position'],
                'message' => '文件上传成功，已加入队列'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '上传失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证上传文件
     */
    private function validateUploadFile(array $fileData): array
    {
        // 检查文件是否存在
        if (!isset($fileData['tmp_name']) || !file_exists($fileData['tmp_name'])) {
            return [
                'valid' => false,
                'error' => '文件不存在'
            ];
        }

        // 检查文件大小
        $maxSize = $this->config->get('max_file_size');
        if ($fileData['size'] > $maxSize) {
            return [
                'valid' => false,
                'error' => '文件大小超过限制（最大' . $this->formatFileSize($maxSize) . '）'
            ];
        }

        // 检查文件类型
        $extension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));
        $allowedExtensions = $this->config->get('allowed_extensions');
        if (!in_array($extension, $allowedExtensions)) {
            return [
                'valid' => false,
                'error' => '不支持的文件类型，仅支持: ' . implode(', ', $allowedExtensions)
            ];
        }

        // 基本的IPA文件格式验证（简化版）
        if (!str_ends_with(strtolower($fileData['name']), '.ipa')) {
            return [
                'valid' => false,
                'error' => 'IPA文件格式错误'
            ];
        }

        return ['valid' => true];
    }

    /**
     * 验证认证数据
     */
    private function validateAuthData(array $authData): array
    {
        if (!isset($authData['method'])) {
            return [
                'valid' => false,
                'error' => '认证方式未指定'
            ];
        }

        switch ($authData['method']) {
            case 'api_key':
                if (!isset($authData['api_key_id']) || !isset($authData['issuer_id']) || !isset($authData['api_key_content'])) {
                    return [
                        'valid' => false,
                        'error' => 'API Key认证信息不完整'
                    ];
                }
                break;

            case 'apple_id':
                if (!isset($authData['apple_id']) || !isset($authData['app_password'])) {
                    return [
                        'valid' => false,
                        'error' => 'Apple ID认证信息不完整'
                    ];
                }
                break;

            default:
                return [
                    'valid' => false,
                    'error' => '不支持的认证方式'
                ];
        }

        return ['valid' => true];
    }

    /**
     * 保存临时文件
     */
    private function saveTempFile(array $fileData): ?string
    {
        $tempDir = $this->config->get('upload_tmp_path');
        $tempFilename = uniqid('ipa_') . '.ipa';
        $tempPath = $tempDir . '/' . $tempFilename;

        if (!move_uploaded_file($fileData['tmp_name'], $tempPath)) {
            return null;
        }

        return $tempPath;
    }

    /**
     * 生成存储文件名
     */
    private function generateStoredFilename(string $bundleId): string
    {
        return $bundleId . '_' . time() . '.ipa';
    }

    /**
     * 获取下一个队列位置
     */
    private function getNextQueuePosition(): int
    {
        return $this->uploadRecordModel->countQueuedRecords() + 1;
    }

    /**
     * 检查是否可以开始上传
     */
    private function canStartUpload(): bool
    {
        $uploadingCount = $this->uploadRecordModel->countUploadingRecords();
        $maxConcurrent = $this->config->get('max_concurrent_uploads');

        return $uploadingCount < $maxConcurrent;
    }

    /**
     * 处理上传队列
     */
    public function processUploadQueue(): void
    {
        while ($this->canStartUpload()) {
            $nextRecord = $this->uploadRecordModel->getNextQueuedRecord();
            if (!$nextRecord) {
                break; // 队列为空
            }

            $this->startUpload($nextRecord);
        }
    }

    /**
     * 开始上传
     */
    private function startUpload(array $record): void
    {
        // 更新状态为上传中
        $this->uploadRecordModel->updateStatus($record['_id'], 'uploading');

        // 获取GitHub账号信息
        $githubAccount = $this->githubAccountModel->findById($record['github_account']);
        if (!$githubAccount) {
            $this->uploadRecordModel->updateStatus($record['_id'], 'failed', [
                'upload_result' => ['error' => 'GitHub账号不存在']
            ]);
            return;
        }

        $token = $this->githubAccountModel->getAccountToken($record['github_account']);
        if (!$token) {
            $this->uploadRecordModel->updateStatus($record['_id'], 'failed', [
                'upload_result' => ['error' => '无法获取GitHub Token']
            ]);
            return;
        }

        try {
            // 生成Repository名称
            $repoName = $this->githubService->generateRepoName();

            // 创建Repository
            $repoResult = $this->githubService->createRepository($token, $repoName, true);
            if (!$repoResult['success']) {
                $this->uploadRecordModel->updateStatus($record['_id'], 'failed', [
                    'upload_result' => ['error' => '创建Repository失败: ' . $repoResult['error']]
                ]);
                return;
            }

            // 生成工作流
            $workflowData = [
                'record_id' => $record['_id'],
                'callback_url' => $this->config->get('api_url') . '/api/workflow/callback'
            ];

            // 转换 MongoDB BSONDocument 为数组
            $authData = is_array($record['auth_data']) ? $record['auth_data'] : (array)$record['auth_data'];

            if ($record['auth_method'] === 'api_key') {
                $workflowContent = $this->workflowGenerator->generateAPIKeyWorkflow(array_merge($workflowData, $authData));
            } else {
                $workflowContent = $this->workflowGenerator->generateAppleIDWorkflow(array_merge($workflowData, $authData));
            }

            // 上传工作流文件
            $workflowFile = $this->githubService->generateWorkflowFileName();
            $workflowResult = $this->githubService->uploadFile(
                $token,
                $githubAccount['username'],
                $repoName,
                '.github/workflows/' . $workflowFile,
                $workflowContent,
                'Add upload workflow'
            );

            if (!$workflowResult['success']) {
                $this->uploadRecordModel->updateStatus($record['_id'], 'failed', [
                    'upload_result' => ['error' => '上传工作流失败: ' . $workflowResult['error']]
                ]);
                return;
            }

            // 触发工作流
            $triggerResult = $this->githubService->triggerWorkflow(
                $token,
                $githubAccount['username'],
                $repoName,
                $workflowFile,
                [
                    'record_id' => $record['_id'],
                    'callback_url' => $this->config->get('api_url') . '/api/workflow/callback'
                ]
            );

            if (!$triggerResult['success']) {
                $this->uploadRecordModel->updateStatus($record['_id'], 'failed', [
                    'upload_result' => ['error' => '触发工作流失败: ' . $triggerResult['error']]
                ]);
                return;
            }

            // 更新工作流ID
            $this->uploadRecordModel->updateWorkflowId($record['_id'], $triggerResult['workflow_id'] ?? '');
        } catch (\Exception $e) {
            $this->uploadRecordModel->updateStatus($record['_id'], 'failed', [
                'upload_result' => ['error' => '上传过程异常: ' . $e->getMessage()]
            ]);
        }
    }

    /**
     * 获取用户上传记录
     */
    public function getUserRecords(string $userId, int $limit = 50, int $skip = 0): array
    {
        return $this->uploadRecordModel->findByUserId($userId, $limit, $skip);
    }

    /**
     * 获取上传记录详情
     */
    public function getRecordDetail(string $recordId): ?array
    {
        return $this->uploadRecordModel->findById($recordId);
    }

    /**
     * 获取队列状态
     */
    public function getQueueStatus(): array
    {
        return [
            'queued_count' => $this->uploadRecordModel->countQueuedRecords(),
            'uploading_count' => $this->uploadRecordModel->countUploadingRecords(),
            'max_concurrent' => $this->config->get('max_concurrent_uploads')
        ];
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
