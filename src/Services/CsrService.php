<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Models\Certificate;

class CsrService
{
    private AppConfig $config;
    private Certificate $certificateModel;
    private string $csrStoragePath;
    private string $privateKeyStoragePath;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->certificateModel = new Certificate();

        // 设置存储路径
        $basePath = $this->config->get('app_storage_path') ?? '/data/storage/';
        $this->csrStoragePath = $basePath . '/csr';
        $this->privateKeyStoragePath = $basePath . '/private_keys';

        // 确保目录存在
        $this->ensureDirectoriesExist();
    }

    /**
     * 生成CSR文件和私钥
     */
    public function generateCsr(array $csrData, string $userId): array
    {
        try {
            // 验证输入数据
            $validation = $this->validateCsrData($csrData);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }

            // 生成私钥
            $privateKeyResult = $this->generatePrivateKey();
            if (!$privateKeyResult['success']) {
                return $privateKeyResult;
            }

            // 生成CSR
            $csrResult = $this->createCsr($csrData, $privateKeyResult['private_key']);
            if (!$csrResult['success']) {
                // 清理私钥文件
                if (isset($privateKeyResult['private_key_path'])) {
                    @unlink($privateKeyResult['private_key_path']);
                }
                return $csrResult;
            }

            // 保存文件到存储目录
            $timestamp = date('Y-m-d_H-i-s');
            $filename = $csrData['common_name'] . '_' . $timestamp;

            $privateKeyPath = $this->privateKeyStoragePath . '/' . $filename . '.key';
            $csrPath = $this->csrStoragePath . '/' . $filename . '.csr';

            // 保存私钥文件
            if (!file_put_contents($privateKeyPath, $privateKeyResult['private_key'])) {
                return [
                    'success' => false,
                    'error' => '私钥文件保存失败'
                ];
            }

            // 保存CSR文件
            if (!file_put_contents($csrPath, $csrResult['csr'])) {
                @unlink($privateKeyPath); // 清理私钥文件
                return [
                    'success' => false,
                    'error' => 'CSR文件保存失败'
                ];
            }

            // 设置文件权限
            chmod($privateKeyPath, 0600); // 私钥只有所有者可读写
            chmod($csrPath, 0644);        // CSR文件可读

            // 保存到数据库
            $certificateData = [
                'name' => $filename,
                'type' => 'csr',
                'file_path' => $csrPath,
                'file_size' => filesize($csrPath),
                'uploaded_by' => $userId,
                'private_key_path' => $privateKeyPath,
                'common_name' => $csrData['common_name'],
                'country' => $csrData['country'],
                'organization' => $csrData['organization'],
                'organizational_unit' => $csrData['organizational_unit'] ?? null
            ];

            $certificateId = $this->certificateModel->create($certificateData);

            return [
                'success' => true,
                'certificate_id' => (string)$certificateId,
                'csr_path' => $csrPath,
                'private_key_path' => $privateKeyPath,
                'filename' => $filename,
                'csr_content' => $csrResult['csr']
            ];
        } catch (\Exception $e) {
            error_log("CSR生成失败: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'CSR生成失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 生成私钥
     */
    private function generatePrivateKey(): array
    {
        try {
            // 生成2048位RSA私钥
            $config = [
                "digest_alg" => "sha256",
                "private_key_bits" => 2048,
                "private_key_type" => OPENSSL_KEYTYPE_RSA,
            ];

            $privateKey = openssl_pkey_new($config);
            if (!$privateKey) {
                return [
                    'success' => false,
                    'error' => 'OpenSSL私钥生成失败: ' . openssl_error_string()
                ];
            }

            // 导出私钥
            if (!openssl_pkey_export($privateKey, $privateKeyOut)) {
                return [
                    'success' => false,
                    'error' => 'OpenSSL私钥导出失败: ' . openssl_error_string()
                ];
            }

            return [
                'success' => true,
                'private_key' => $privateKeyOut,
                'private_key_resource' => $privateKey
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '私钥生成异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 创建CSR
     */
    private function createCsr(array $csrData, $privateKey): array
    {
        try {
            // 构建Distinguished Name
            $dn = [
                "countryName" => $csrData['country'],
                "organizationName" => $csrData['organization'],
                "commonName" => $csrData['common_name']
            ];

            if (!empty($csrData['organizational_unit'])) {
                $dn["organizationalUnitName"] = $csrData['organizational_unit'];
            }

            // 生成CSR
            $csr = openssl_csr_new($dn, $privateKey, [
                "digest_alg" => "sha256",
                "req_extensions" => "v3_req",
                "config" => $this->getOpenSSLConfig()
            ]);

            if (!$csr) {
                return [
                    'success' => false,
                    'error' => 'OpenSSL CSR生成失败: ' . openssl_error_string()
                ];
            }

            // 导出CSR
            if (!openssl_csr_export($csr, $csrOut)) {
                return [
                    'success' => false,
                    'error' => 'OpenSSL CSR导出失败: ' . openssl_error_string()
                ];
            }

            return [
                'success' => true,
                'csr' => $csrOut
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'CSR创建异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证CSR数据
     */
    private function validateCsrData(array $data): array
    {
        $required = ['common_name', 'country', 'organization'];

        foreach ($required as $field) {
            if (empty($data[$field])) {
                return [
                    'valid' => false,
                    'error' => "缺少必需字段: {$field}"
                ];
            }
        }

        // 验证国家代码
        if (strlen($data['country']) !== 2) {
            return [
                'valid' => false,
                'error' => '国家代码必须是2位字符'
            ];
        }

        // 验证Common Name格式（通常是邮箱或域名）
        if (
            !filter_var($data['common_name'], FILTER_VALIDATE_EMAIL) &&
            !preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/', $data['common_name'])
        ) {
            return [
                'valid' => false,
                'error' => 'Common Name必须是有效的邮箱地址或域名'
            ];
        }

        return ['valid' => true];
    }

    /**
     * 获取OpenSSL配置
     */
    private function getOpenSSLConfig(): string
    {
        // 创建临时配置文件
        $configContent = "
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
";

        $configPath = sys_get_temp_dir() . '/openssl_' . uniqid() . '.conf';
        file_put_contents($configPath, $configContent);

        return $configPath;
    }

    /**
     * 确保存储目录存在
     */
    private function ensureDirectoriesExist(): void
    {
        $directories = [$this->csrStoragePath, $this->privateKeyStoragePath];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * 下载CSR文件
     */
    public function downloadCsr(string $certificateId): array
    {
        try {
            $certificate = $this->certificateModel->findById($certificateId);

            if (!$certificate || $certificate['type'] !== 'csr') {
                return [
                    'success' => false,
                    'error' => 'CSR记录不存在'
                ];
            }

            if (!file_exists($certificate['file_path'])) {
                return [
                    'success' => false,
                    'error' => 'CSR文件不存在'
                ];
            }

            return [
                'success' => true,
                'file_path' => $certificate['file_path'],
                'filename' => $certificate['name'] . '.csr',
                'content' => file_get_contents($certificate['file_path'])
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'CSR下载失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取用户的CSR列表
     */
    public function getUserCsrList(string $userId): array
    {
        return $this->certificateModel->findByType('csr', ['uploaded_by' => $userId]);
    }
}
