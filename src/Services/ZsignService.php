<?php

namespace App\Services;

/**
 * Zsign服务类
 * 用于iOS应用重签名
 */
class ZsignService
{
    private string $zsignPath;
    private string $tempDir;

    public function __construct()
    {
        $this->zsignPath = '/usr/local/bin/zsign';
        $this->tempDir = sys_get_temp_dir();
    }

    /**
     * 检查zsign是否可用
     */
    public function isAvailable(): bool
    {
        return file_exists($this->zsignPath) && is_executable($this->zsignPath);
    }

    /**
     * 重签名IPA文件
     *
     * @param string $ipaPath IPA文件路径
     * @param string $certPath P12证书文件路径或证书名称
     * @param string $provisionPath mobileprovision文件路径
     * @param string $outputPath 输出文件路径
     * @param array $options 额外选项
     * @return array 结果信息
     */
    public function resignIpa(
        string $ipaPath,
        string $certPath,
        string $provisionPath,
        string $outputPath,
        array $options = []
    ): array {
        if (!$this->isAvailable()) {
            return [
                'success' => false,
                'error' => 'zsign工具不可用'
            ];
        }

        if (!file_exists($ipaPath)) {
            return [
                'success' => false,
                'error' => 'IPA文件不存在'
            ];
        }

        if (!file_exists($provisionPath)) {
            return [
                'success' => false,
                'error' => 'mobileprovision文件不存在'
            ];
        }

        // 构建命令
        $command = $this->buildResignCommand($ipaPath, $certPath, $provisionPath, $outputPath, $options);

        // 执行命令
        $startTime = microtime(true);
        exec($command, $output, $returnCode);
        $duration = microtime(true) - $startTime;

        if ($returnCode === 0) {
            return [
                'success' => true,
                'output_path' => $outputPath,
                'duration' => round($duration, 2),
                'output' => implode("\n", $output)
            ];
        } else {
            return [
                'success' => false,
                'error' => '重签名失败',
                'output' => implode("\n", $output),
                'return_code' => $returnCode
            ];
        }
    }

    /**
     * 构建重签名命令
     */
    private function buildResignCommand(
        string $ipaPath,
        string $certPath,
        string $provisionPath,
        string $outputPath,
        array $options = []
    ): string {
        $parts = [
            escapeshellcmd($this->zsignPath),
            '-k',
            escapeshellarg($certPath),
            '-m',
            escapeshellarg($provisionPath),
            '-o',
            escapeshellarg($outputPath)
        ];

        // 添加P12证书密码
        if (isset($options['password'])) {
            $parts[] = '-p';
            $parts[] = escapeshellarg($options['password']);
        }

        // 添加额外选项
        if (isset($options['bundle_id'])) {
            $parts[] = '-b';
            $parts[] = escapeshellarg($options['bundle_id']);
        }

        if (isset($options['bundle_name'])) {
            $parts[] = '-n';
            $parts[] = escapeshellarg($options['bundle_name']);
        }

        if (isset($options['bundle_version'])) {
            $parts[] = '-v';
            $parts[] = escapeshellarg($options['bundle_version']);
        }

        // 强制重签名
        if ($options['force'] ?? false) {
            $parts[] = '-f';
        }

        // 输入文件
        $parts[] = escapeshellarg($ipaPath);

        // 重定向错误输出
        $parts[] = '2>&1';

        return implode(' ', $parts);
    }

    /**
     * 解析mobileprovision文件信息（别名方法）
     */
    public function parseMobileProvision(string $provisionPath): ?array
    {
        return $this->parseProvisionProfile($provisionPath);
    }

    /**
     * 解析mobileprovision文件信息
     */
    public function parseProvisionProfile(string $provisionPath): ?array
    {
        if (!file_exists($provisionPath)) {
            return null;
        }

        // 尝试使用security命令（macOS）
        $command = sprintf(
            'security cms -D -i %s 2>/dev/null',
            escapeshellarg($provisionPath)
        );

        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            // 如果security命令不可用（Linux），尝试使用openssl
            return $this->parseProvisionProfileWithOpenSSL($provisionPath);
        }

        $plistContent = implode("\n", $output);

        try {
            // 解析plist内容
            $data = $this->parsePlist($plistContent);

            return [
                'name' => $data['Name'] ?? 'Unknown',
                'app_id_name' => $data['AppIDName'] ?? 'Unknown',
                'application_identifier_prefix' => $data['ApplicationIdentifierPrefix'] ?? [],
                'creation_date' => $data['CreationDate'] ?? null,
                'expiration_date' => $data['ExpirationDate'] ?? null,
                'team_identifier' => $data['TeamIdentifier'] ?? [],
                'team_name' => $data['TeamName'] ?? 'Unknown',
                'uuid' => $data['UUID'] ?? 'Unknown',
                'version' => $data['Version'] ?? 1,
                'entitlements' => $data['Entitlements'] ?? [],
                'provisioned_devices' => $data['ProvisionedDevices'] ?? []
            ];
        } catch (\Exception $e) {
            error_log("解析mobileprovision文件失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 简单的plist解析器
     */
    private function parsePlist(string $content): array
    {
        // 这里可以使用更复杂的plist解析库
        // 为了简单起见，使用基本的XML解析

        // 移除plist头部
        $content = preg_replace('/^.*?<plist[^>]*>/s', '<plist>', $content);
        $content = preg_replace('/<\/plist>.*$/s', '</plist>', $content);

        // 尝试解析为XML
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($content);

        if ($xml === false) {
            throw new \Exception('无法解析plist文件');
        }

        return $this->xmlToArray($xml->dict);
    }

    /**
     * 将XML转换为数组
     */
    private function xmlToArray($xml): array
    {
        $result = [];
        $children = $xml->children();

        for ($i = 0; $i < count($children); $i += 2) {
            if (isset($children[$i]) && isset($children[$i + 1])) {
                $key = (string)$children[$i];
                $value = $children[$i + 1];

                switch ($value->getName()) {
                    case 'string':
                        $result[$key] = (string)$value;
                        break;
                    case 'integer':
                        $result[$key] = (int)$value;
                        break;
                    case 'true':
                        $result[$key] = true;
                        break;
                    case 'false':
                        $result[$key] = false;
                        break;
                    case 'array':
                        $result[$key] = $this->xmlArrayToArray($value);
                        break;
                    case 'dict':
                        $result[$key] = $this->xmlToArray($value);
                        break;
                    case 'date':
                        $result[$key] = (string)$value;
                        break;
                    default:
                        $result[$key] = (string)$value;
                }
            }
        }

        return $result;
    }

    /**
     * 将XML数组转换为PHP数组
     */
    private function xmlArrayToArray($xmlArray): array
    {
        $result = [];
        foreach ($xmlArray->children() as $child) {
            switch ($child->getName()) {
                case 'string':
                    $result[] = (string)$child;
                    break;
                case 'integer':
                    $result[] = (int)$child;
                    break;
                case 'true':
                    $result[] = true;
                    break;
                case 'false':
                    $result[] = false;
                    break;
                case 'dict':
                    $result[] = $this->xmlToArray($child);
                    break;
                default:
                    $result[] = (string)$child;
            }
        }
        return $result;
    }

    /**
     * 获取zsign版本信息
     */
    public function getVersion(): ?string
    {
        if (!$this->isAvailable()) {
            return null;
        }

        exec($this->zsignPath . ' --version 2>&1', $output, $returnCode);

        if ($returnCode === 0 && !empty($output)) {
            return trim($output[0]);
        }

        return null;
    }

    /**
     * 使用OpenSSL解析mobileprovision文件（Linux兼容）
     */
    private function parseProvisionProfileWithOpenSSL(string $provisionPath): ?array
    {
        try {
            // mobileprovision文件是一个PKCS#7签名的plist文件
            // 使用openssl提取plist内容
            $command = sprintf(
                'openssl smime -inform DER -verify -noverify -in %s 2>/dev/null',
                escapeshellarg($provisionPath)
            );

            exec($command, $output, $returnCode);

            if ($returnCode !== 0 || empty($output)) {
                // 如果openssl smime失败，尝试直接提取plist内容
                return $this->extractPlistFromProvision($provisionPath);
            }

            $plistContent = implode("\n", $output);

            // 解析plist内容
            $data = $this->parsePlist($plistContent);

            return [
                'name' => $data['Name'] ?? 'Unknown',
                'app_id_name' => $data['AppIDName'] ?? 'Unknown',
                'application_identifier_prefix' => $data['ApplicationIdentifierPrefix'] ?? [],
                'creation_date' => $data['CreationDate'] ?? null,
                'expiration_date' => $data['ExpirationDate'] ?? null,
                'team_identifier' => $data['TeamIdentifier'] ?? [],
                'team_name' => $data['TeamName'] ?? 'Unknown',
                'uuid' => $data['UUID'] ?? 'Unknown',
                'version' => $data['Version'] ?? 1,
                'entitlements' => $data['Entitlements'] ?? [],
                'provisioned_devices' => $data['ProvisionedDevices'] ?? []
            ];
        } catch (\Exception $e) {
            error_log("使用OpenSSL解析mobileprovision文件失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 从mobileprovision文件中直接提取plist内容
     */
    private function extractPlistFromProvision(string $provisionPath): ?array
    {
        try {
            $content = file_get_contents($provisionPath);

            // 查找plist的开始和结束标记
            $startPos = strpos($content, '<?xml');
            $endPos = strrpos($content, '</plist>');

            if ($startPos === false || $endPos === false) {
                return null;
            }

            $plistContent = substr($content, $startPos, $endPos - $startPos + 8);

            // 解析plist内容
            $data = $this->parsePlist($plistContent);

            return [
                'name' => $data['Name'] ?? 'Unknown',
                'app_id_name' => $data['AppIDName'] ?? 'Unknown',
                'application_identifier_prefix' => $data['ApplicationIdentifierPrefix'] ?? [],
                'creation_date' => $data['CreationDate'] ?? null,
                'expiration_date' => $data['ExpirationDate'] ?? null,
                'team_identifier' => $data['TeamIdentifier'] ?? [],
                'team_name' => $data['TeamName'] ?? 'Unknown',
                'uuid' => $data['UUID'] ?? 'Unknown',
                'version' => $data['Version'] ?? 1,
                'entitlements' => $data['Entitlements'] ?? [],
                'provisioned_devices' => $data['ProvisionedDevices'] ?? []
            ];
        } catch (\Exception $e) {
            error_log("直接提取plist内容失败: " . $e->getMessage());
            return null;
        }
    }
}
