<?php

namespace App\Services;

use App\Models\Certificate;
use App\Models\ConversionRecord;
use App\Config\AppConfig;

class CerConverterService
{
    private Certificate $certificateModel;
    private ConversionRecord $conversionRecord;
    private AppConfig $config;
    private string $tempStoragePath;
    private string $certificateStoragePath;

    public function __construct()
    {
        $this->certificateModel = new Certificate();
        $this->conversionRecord = new ConversionRecord();
        $this->config = AppConfig::getInstance();

        // 设置存储路径
        $basePath = $this->config->get('app_storage_path') ?? '/data/storage';
        $this->tempStoragePath = $basePath . '/temp';
        $this->certificateStoragePath = $basePath . '/certificates';

        $this->ensureDirectoriesExist();
    }

    /**
     * 智能转换CER文件为P12格式
     */
    public function convertCerToP12(array $fileData, array $conversionData, string $userId): array
    {
        try {
            // 验证输入数据
            $validationResult = $this->validateConversionData($fileData, $conversionData);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'error' => $validationResult['error']
                ];
            }

            // 保存上传的CER文件
            $cerPath = $this->saveCerFile($fileData);
            if (!$cerPath) {
                return [
                    'success' => false,
                    'error' => 'CER文件保存失败'
                ];
            }

            // 直接尝试用所有私钥转换CER文件
            $conversionResult = $this->tryConvertWithAllKeys($cerPath, $userId, $conversionData);

            // 清理临时CER文件
            @unlink($cerPath);

            if (!$conversionResult['success']) {
                return $conversionResult;
            }

            if (!$conversionResult['success']) {
                return $conversionResult;
            }

            // 保存转换后的P12证书到数据库
            $certificateId = $this->saveCertificateRecord(
                $conversionResult,
                $conversionData,
                $userId,
                $conversionResult['matched_csr_info']
            );

            // 记录转换历史
            $this->recordConversion($fileData, $conversionData, $conversionResult, $userId, $certificateId, 'auto');

            return [
                'success' => true,
                'certificate_id' => $certificateId,
                'message' => 'CER文件已成功转换为P12格式',
                'p12_path' => $conversionResult['p12_path'],
                'filename' => basename($conversionResult['p12_path']),
                'matched_csr' => $conversionResult['matched_csr_info']['name'],
                'certificate_info' => $conversionResult['certificate_info'] ?? []
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'CER转换失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 尝试用所有私钥转换CER文件
     */
    private function tryConvertWithAllKeys(string $cerPath, string $userId, array $conversionData): array
    {
        try {
            // 获取用户的所有CSR记录
            $userCsrRecords = $this->certificateModel->findByType('csr', ['uploaded_by' => $userId]);

            if (empty($userCsrRecords)) {
                return [
                    'success' => false,
                    'error' => '您还没有在本系统生成过CSR文件',
                    'suggestion' => '请先使用本系统的CSR生成功能创建证书签名请求，然后用生成的CSR去Apple Developer申请证书',
                    'error_type' => 'no_csr_records'
                ];
            }

            $conversionErrors = [];

            // 逐个尝试每个CSR的私钥
            foreach ($userCsrRecords as $csr) {
                // 检查私钥文件是否存在
                if (!isset($csr['private_key_path']) || !file_exists($csr['private_key_path'])) {
                    $conversionErrors[] = "CSR '{$csr['name']}': 私钥文件不存在";
                    continue;
                }

                // 尝试转换
                $result = $this->attemptConversion($cerPath, $csr['private_key_path'], $csr, $conversionData);

                if ($result['success']) {
                    // 转换成功，返回结果
                    return [
                        'success' => true,
                        'p12_path' => $result['p12_path'],
                        'certificate_info' => $result['certificate_info'],
                        'matched_csr_info' => $csr
                    ];
                } else {
                    $conversionErrors[] = "CSR '{$csr['name']}': " . $result['error'];
                }
            }

            // 所有私钥都尝试失败
            return [
                'success' => false,
                'error' => '无法找到与此CER文件匹配的私钥',
                'suggestion' => '此CER文件可能不是使用本系统生成的CSR申请的，或者私钥文件已损坏。请检查CER文件是否正确，或重新生成CSR并申请新的证书。',
                'error_type' => 'no_matching_key',
                'conversion_errors' => $conversionErrors,
                'available_csrs' => array_map(function ($csr) {
                    return [
                        'id' => $csr['_id'],
                        'name' => $csr['name'],
                        'common_name' => $csr['common_name'] ?? '',
                        'organization' => $csr['organization'] ?? '',
                        'created_at' => $csr['created_at'] ?? ''
                    ];
                }, $userCsrRecords),
                'allow_manual_selection' => true
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '转换过程中发生错误: ' . $e->getMessage(),
                'error_type' => 'conversion_error'
            ];
        }
    }

    /**
     * 尝试使用指定私钥转换CER文件
     */
    private function attemptConversion(string $cerPath, string $privateKeyPath, array $csrInfo, array $conversionData): array
    {
        try {
            // 生成临时输出文件路径
            $tempP12Path = $this->tempStoragePath . '/temp_' . uniqid() . '.p12';

            // 构建OpenSSL命令
            $password = escapeshellarg($conversionData['password']);
            $cerPathEscaped = escapeshellarg($cerPath);
            $privateKeyPathEscaped = escapeshellarg($privateKeyPath);
            $tempP12PathEscaped = escapeshellarg($tempP12Path);
            $name = escapeshellarg($conversionData['certificate_name']);

            // OpenSSL命令：将CER和私钥合并为P12
            $command = "openssl pkcs12 -export -out $tempP12PathEscaped -inkey $privateKeyPathEscaped -in $cerPathEscaped -name $name -passout pass:$password 2>&1";

            // 执行命令
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                // 清理临时文件
                @unlink($tempP12Path);
                return [
                    'success' => false,
                    'error' => 'OpenSSL转换失败: ' . implode("\n", $output)
                ];
            }

            // 验证P12文件是否生成成功
            if (!file_exists($tempP12Path) || filesize($tempP12Path) === 0) {
                @unlink($tempP12Path);
                return [
                    'success' => false,
                    'error' => 'P12文件生成失败'
                ];
            }

            // 验证P12文件是否有效
            $validationResult = $this->validateP12File($tempP12Path, $conversionData['password']);
            if (!$validationResult['valid']) {
                @unlink($tempP12Path);
                return [
                    'success' => false,
                    'error' => 'P12文件验证失败: ' . $validationResult['error']
                ];
            }

            // 移动到正式存储位置
            $finalP12Filename = 'converted_' . $csrInfo['name'] . '_' . uniqid() . '.p12';
            $finalP12Path = $this->certificateStoragePath . '/' . $finalP12Filename;

            if (!rename($tempP12Path, $finalP12Path)) {
                @unlink($tempP12Path);
                return [
                    'success' => false,
                    'error' => '文件移动失败'
                ];
            }

            return [
                'success' => true,
                'p12_path' => $finalP12Path,
                'certificate_info' => $validationResult['info']
            ];
        } catch (\Exception $e) {
            // 清理临时文件
            if (isset($tempP12Path)) {
                @unlink($tempP12Path);
            }

            return [
                'success' => false,
                'error' => '转换过程异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 检测CER文件对应的私钥（已废弃，保留用于兼容性）
     */
    private function detectPrivateKeyForCer(string $cerPath, string $userId): array
    {
        try {
            // 提取CER文件的公钥信息
            $cerInfo = $this->extractCertificateInfo($cerPath);
            if (!$cerInfo['success']) {
                return [
                    'found' => false,
                    'error' => '无法解析CER文件: ' . $cerInfo['error'],
                    'suggestion' => '请确保上传的是有效的CER证书文件'
                ];
            }

            // 获取用户的所有CSR记录
            $userCsrRecords = $this->certificateModel->findByType('csr', ['uploaded_by' => $userId]);

            if (empty($userCsrRecords)) {
                return [
                    'found' => false,
                    'error' => '您还没有在本系统生成过CSR文件',
                    'suggestion' => '请先使用本系统的CSR生成功能创建证书签名请求，然后用生成的CSR去Apple Developer申请证书'
                ];
            }

            // 尝试匹配CSR和CER
            $matchResult = $this->matchCsrWithCer($cerInfo, $userCsrRecords);

            if (!$matchResult['found']) {
                // 提供详细的匹配信息和手动选择选项
                $availableCsrs = [];
                foreach ($userCsrRecords as $csr) {
                    $availableCsrs[] = [
                        'id' => $csr['_id'],
                        'name' => $csr['name'],
                        'common_name' => $csr['common_name'] ?? '',
                        'organization' => $csr['organization'] ?? '',
                        'created_at' => $csr['created_at'] ?? ''
                    ];
                }

                return [
                    'found' => false,
                    'error' => '智能匹配未找到对应的CSR记录',
                    'suggestion' => '系统无法自动匹配您的CER文件与CSR记录。您可以尝试手动选择对应的CSR记录进行转换。',
                    'match_details' => $matchResult['match_details'] ?? [],
                    'available_csrs' => $availableCsrs,
                    'allow_manual_selection' => true
                ];
            }

            // 检查私钥文件是否存在
            $privateKeyPath = $matchResult['csr_info']['private_key_path'];
            if (!file_exists($privateKeyPath)) {
                return [
                    'found' => false,
                    'error' => '找到匹配的CSR记录，但对应的私钥文件已丢失',
                    'suggestion' => '私钥文件可能已被删除或移动，请重新生成CSR并申请新的证书'
                ];
            }

            return [
                'found' => true,
                'private_key_path' => $privateKeyPath,
                'csr_info' => $matchResult['csr_info'],
                'match_confidence' => $matchResult['confidence']
            ];
        } catch (\Exception $e) {
            return [
                'found' => false,
                'error' => '检测私钥时发生错误: ' . $e->getMessage(),
                'suggestion' => '请联系管理员或重新生成CSR'
            ];
        }
    }

    /**
     * 提取证书信息
     */
    private function extractCertificateInfo(string $cerPath): array
    {
        try {
            // 使用OpenSSL提取证书信息
            $command = "openssl x509 -in " . escapeshellarg($cerPath) . " -noout -subject -dates -pubkey 2>&1";
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                return [
                    'success' => false,
                    'error' => 'OpenSSL解析失败: ' . implode("\n", $output)
                ];
            }

            $info = [
                'subject' => '',
                'valid_from' => '',
                'valid_to' => '',
                'public_key' => ''
            ];

            $collectingPubKey = false;
            $pubKeyLines = [];

            foreach ($output as $line) {
                if (strpos($line, 'subject=') === 0) {
                    $info['subject'] = substr($line, 8);
                } elseif (strpos($line, 'notBefore=') === 0) {
                    $info['valid_from'] = substr($line, 10);
                } elseif (strpos($line, 'notAfter=') === 0) {
                    $info['valid_to'] = substr($line, 9);
                } elseif (strpos($line, '-----BEGIN PUBLIC KEY-----') === 0) {
                    $collectingPubKey = true;
                    $pubKeyLines[] = $line;
                } elseif (strpos($line, '-----END PUBLIC KEY-----') === 0) {
                    $pubKeyLines[] = $line;
                    $collectingPubKey = false;
                } elseif ($collectingPubKey) {
                    $pubKeyLines[] = $line;
                }
            }

            $info['public_key'] = implode("\n", $pubKeyLines);

            // 提取Common Name
            if (preg_match('/CN\s*=\s*([^,]+)/', $info['subject'], $matches)) {
                $info['common_name'] = trim($matches[1]);
            }

            return [
                'success' => true,
                'info' => $info
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '提取证书信息异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 匹配CSR和CER - 优化版本
     */
    private function matchCsrWithCer(array $cerInfo, array $csrRecords): array
    {
        $bestMatch = null;
        $highestConfidence = 0;
        $matchDetails = [];

        foreach ($csrRecords as $csr) {
            $confidence = 0;
            $details = [];

            // 1. 检查Common Name精确匹配 (最高权重)
            if (
                isset($cerInfo['info']['common_name']) &&
                isset($csr['common_name']) &&
                $cerInfo['info']['common_name'] === $csr['common_name']
            ) {
                $confidence += 60;
                $details[] = 'Common Name精确匹配 (+60)';
            }

            // 2. 检查Common Name模糊匹配
            if (isset($cerInfo['info']['common_name']) && isset($csr['common_name'])) {
                $cerCN = strtolower($cerInfo['info']['common_name']);
                $csrCN = strtolower($csr['common_name']);

                if (strpos($cerCN, $csrCN) !== false || strpos($csrCN, $cerCN) !== false) {
                    $confidence += 40;
                    $details[] = 'Common Name模糊匹配 (+40)';
                }
            }

            // 3. 检查组织信息匹配
            if (isset($csr['organization']) && !empty($csr['organization'])) {
                if (strpos($cerInfo['info']['subject'], $csr['organization']) !== false) {
                    $confidence += 30;
                    $details[] = '组织信息匹配 (+30)';
                }
            }

            // 4. 检查国家代码匹配
            if (isset($csr['country']) && !empty($csr['country'])) {
                if (strpos($cerInfo['info']['subject'], 'C=' . $csr['country']) !== false) {
                    $confidence += 20;
                    $details[] = '国家代码匹配 (+20)';
                }
            }

            // 5. 检查邮箱匹配 (如果Common Name是邮箱格式)
            if (isset($cerInfo['info']['common_name']) && isset($csr['common_name'])) {
                if (
                    filter_var($cerInfo['info']['common_name'], FILTER_VALIDATE_EMAIL) &&
                    filter_var($csr['common_name'], FILTER_VALIDATE_EMAIL)
                ) {
                    if ($cerInfo['info']['common_name'] === $csr['common_name']) {
                        $confidence += 50;
                        $details[] = '邮箱地址匹配 (+50)';
                    }
                }
            }

            // 6. 检查部门信息匹配
            if (isset($csr['organizational_unit']) && !empty($csr['organizational_unit'])) {
                if (strpos($cerInfo['info']['subject'], $csr['organizational_unit']) !== false) {
                    $confidence += 15;
                    $details[] = '部门信息匹配 (+15)';
                }
            }

            // 7. 时间窗口匹配 (CSR创建时间和证书有效期的合理性)
            if (isset($csr['created_at']) && isset($cerInfo['info']['valid_from'])) {
                $csrTime = strtotime($csr['created_at']);
                $certTime = strtotime($cerInfo['info']['valid_from']);

                // 如果证书签发时间在CSR创建后的合理时间内（30天内）
                if ($certTime >= $csrTime && ($certTime - $csrTime) <= (30 * 24 * 3600)) {
                    $confidence += 10;
                    $details[] = '时间窗口合理 (+10)';
                }
            }

            $matchDetails[$csr['_id']] = [
                'csr_name' => $csr['name'],
                'confidence' => $confidence,
                'details' => $details
            ];

            // 降低匹配阈值到30分，增加匹配成功率
            if ($confidence >= 30 && $confidence > $highestConfidence) {
                $highestConfidence = $confidence;
                $bestMatch = $csr;
            }
        }

        if ($bestMatch) {
            return [
                'found' => true,
                'csr_info' => $bestMatch,
                'confidence' => $highestConfidence,
                'match_details' => $matchDetails
            ];
        }

        return [
            'found' => false,
            'confidence' => $highestConfidence,
            'match_details' => $matchDetails
        ];
    }

    /**
     * 使用检测到的私钥执行转换
     */
    private function performConversionWithDetectedKey(string $cerPath, string $privateKeyPath, array $csrInfo, array $conversionData): array
    {
        try {
            // 生成输出文件路径
            $p12Filename = 'converted_' . $csrInfo['name'] . '_' . uniqid() . '.p12';
            $p12Path = $this->certificateStoragePath . '/' . $p12Filename;

            // 构建OpenSSL命令
            $password = escapeshellarg($conversionData['password']);
            $cerPath = escapeshellarg($cerPath);
            $privateKeyPath = escapeshellarg($privateKeyPath);
            $p12Path = escapeshellarg($p12Path);
            $name = escapeshellarg($conversionData['certificate_name']);

            // OpenSSL命令：将CER和私钥合并为P12
            $command = "openssl pkcs12 -export -out $p12Path -inkey $privateKeyPath -in $cerPath -name $name -passout pass:$password 2>&1";

            // 执行命令
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                return [
                    'success' => false,
                    'error' => 'OpenSSL转换失败: ' . implode("\n", $output)
                ];
            }

            // 验证P12文件是否生成成功
            if (!file_exists($p12Path) || filesize($p12Path) === 0) {
                return [
                    'success' => false,
                    'error' => 'P12文件生成失败'
                ];
            }

            // 验证P12文件是否有效
            $validationResult = $this->validateP12File($p12Path, $conversionData['password']);
            if (!$validationResult['valid']) {
                @unlink($p12Path);
                return [
                    'success' => false,
                    'error' => 'P12文件验证失败: ' . $validationResult['error']
                ];
            }

            return [
                'success' => true,
                'p12_path' => $p12Path,
                'certificate_info' => $validationResult['info']
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '转换过程异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证转换数据（简化版）
     */
    private function validateConversionData(array $fileData, array $conversionData): array
    {
        // 验证CER文件
        if (!isset($fileData['tmp_name']) || !file_exists($fileData['tmp_name'])) {
            return ['valid' => false, 'error' => 'CER文件无效'];
        }

        if (!isset($fileData['name']) || !preg_match('/\.cer$/i', $fileData['name'])) {
            return ['valid' => false, 'error' => '文件必须是.cer格式'];
        }

        // 验证必需字段
        $requiredFields = ['certificate_name', 'password'];
        foreach ($requiredFields as $field) {
            if (!isset($conversionData[$field]) || empty($conversionData[$field])) {
                return ['valid' => false, 'error' => "缺少必需字段: $field"];
            }
        }

        // 验证密码强度
        if (strlen($conversionData['password']) < 6) {
            return ['valid' => false, 'error' => 'P12密码长度至少6位'];
        }

        return ['valid' => true];
    }

    /**
     * 保存CER文件
     */
    private function saveCerFile(array $fileData): ?string
    {
        $filename = 'cer_' . uniqid() . '.cer';
        $cerPath = $this->tempStoragePath . '/' . $filename;

        if (move_uploaded_file($fileData['tmp_name'], $cerPath)) {
            return $cerPath;
        }

        return null;
    }

    /**
     * 验证P12文件
     */
    private function validateP12File(string $p12Path, string $password): array
    {
        try {
            // 使用OpenSSL验证P12文件
            $command = "openssl pkcs12 -in " . escapeshellarg($p12Path) . " -noout -passin pass:" . escapeshellarg($password) . " 2>&1";
            $output = [];
            $returnCode = 0;
            exec($command, $output, $returnCode);

            if ($returnCode !== 0) {
                return [
                    'valid' => false,
                    'error' => 'P12文件格式无效'
                ];
            }

            return [
                'valid' => true,
                'info' => ['status' => 'valid']
            ];
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'error' => '验证过程异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 保存证书记录到数据库
     */
    private function saveCertificateRecord(array $conversionResult, array $conversionData, string $userId, array $csrInfo): string
    {
        $certificateData = [
            'name' => $conversionData['certificate_name'],
            'type' => 'p12',
            'file_path' => $conversionResult['p12_path'],
            'file_size' => filesize($conversionResult['p12_path']),
            'password' => $conversionData['password'],
            'certificate_type' => $conversionData['certificate_type'] ?? 'distribution',
            'uploaded_by' => $userId,
            'source' => 'cer_conversion',
            'source_csr_id' => $csrInfo['_id'] ?? null,
            'source_csr_name' => $csrInfo['name'] ?? null,
            'certificate_info' => $conversionResult['certificate_info'] ?? [],
            'status' => 'active'
        ];

        $objectId = $this->certificateModel->create($certificateData);
        return (string)$objectId;
    }

    /**
     * 手动选择CSR进行转换
     */
    public function convertCerToP12WithManualCsr(array $fileData, array $conversionData, string $userId, string $csrId): array
    {
        try {
            // 验证输入数据
            $validationResult = $this->validateConversionData($fileData, $conversionData);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'error' => $validationResult['error']
                ];
            }

            // 保存上传的CER文件
            $cerPath = $this->saveCerFile($fileData);
            if (!$cerPath) {
                return [
                    'success' => false,
                    'error' => 'CER文件保存失败'
                ];
            }

            // 获取指定的CSR记录
            $csrRecord = $this->certificateModel->findById($csrId);
            if (!$csrRecord || $csrRecord['type'] !== 'csr' || $csrRecord['uploaded_by'] !== $userId) {
                @unlink($cerPath);
                return [
                    'success' => false,
                    'error' => '指定的CSR记录不存在或无权访问'
                ];
            }

            // 检查私钥文件是否存在
            $privateKeyPath = $csrRecord['private_key_path'];
            if (!file_exists($privateKeyPath)) {
                @unlink($cerPath);
                return [
                    'success' => false,
                    'error' => '对应的私钥文件已丢失'
                ];
            }

            // 使用指定的CSR进行转换
            $conversionResult = $this->performConversionWithDetectedKey(
                $cerPath,
                $privateKeyPath,
                $csrRecord,
                $conversionData
            );

            // 清理临时CER文件
            @unlink($cerPath);

            if (!$conversionResult['success']) {
                return $conversionResult;
            }

            // 保存转换后的P12证书到数据库
            $certificateId = $this->saveCertificateRecord(
                $conversionResult,
                $conversionData,
                $userId,
                $csrRecord
            );

            // 记录转换历史（手动选择）
            $this->recordConversion($fileData, $conversionData, array_merge($conversionResult, ['matched_csr_info' => $csrRecord]), $userId, $certificateId, 'manual');

            return [
                'success' => true,
                'certificate_id' => $certificateId,
                'message' => 'CER文件已成功转换为P12格式（手动选择CSR）',
                'p12_path' => $conversionResult['p12_path'],
                'filename' => basename($conversionResult['p12_path']),
                'matched_csr' => $csrRecord['name'],
                'certificate_info' => $conversionResult['certificate_info'] ?? []
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'CER转换失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 记录转换历史
     */
    private function recordConversion(array $fileData, array $conversionData, array $conversionResult, string $userId, string $certificateId, string $method): void
    {
        try {
            $recordData = [
                'user_id' => $userId,
                'conversion_type' => 'cer_to_p12',
                'source_file_name' => $fileData['name'],
                'source_file_type' => 'cer',
                'target_file_name' => basename($conversionResult['p12_path']),
                'target_file_type' => 'p12',
                'target_file_path' => $conversionResult['p12_path'],
                'certificate_id' => $certificateId,
                'source_csr_id' => $conversionResult['matched_csr_info']['_id'] ?? null,
                'source_csr_name' => $conversionResult['matched_csr_info']['name'] ?? null,
                'conversion_method' => $method, // 'auto' or 'manual'
                'certificate_name' => $conversionData['certificate_name'],
                'certificate_type' => $conversionData['certificate_type'],
                'file_size' => file_exists($conversionResult['p12_path']) ? filesize($conversionResult['p12_path']) : 0,
                'status' => 'completed',
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ];

            $this->conversionRecord->createRecord($recordData);
        } catch (\Exception $e) {
            // 记录转换历史失败不应该影响主要功能
            error_log('Failed to record conversion: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户转换记录
     */
    public function getUserConversionRecords(string $userId, int $limit = 20): array
    {
        return $this->conversionRecord->getUserRecords($userId, $limit);
    }

    /**
     * 获取用户转换统计
     */
    public function getUserConversionStats(string $userId): array
    {
        return $this->conversionRecord->getConversionStats($userId);
    }

    /**
     * 获取所有记录（支持分页）
     */
    public function getAllRecordsWithUserPaginated(int $page = 1, int $pageSize = 10): array
    {
        return $this->conversionRecord->getAllRecordsWithUserPaginated($page, $pageSize);
    }

    /**
     * 根据用户ID获取记录（支持分页）
     */
    public function findByUserIdPaginated(string $userId, int $page = 1, int $pageSize = 10): array
    {
        return $this->conversionRecord->findByUserIdPaginated($userId, $page, $pageSize);
    }

    /**
     * 确保存储目录存在
     */
    private function ensureDirectoriesExist(): void
    {
        if (!is_dir($this->tempStoragePath)) {
            mkdir($this->tempStoragePath, 0755, true);
        }
        if (!is_dir($this->certificateStoragePath)) {
            mkdir($this->certificateStoragePath, 0755, true);
        }
    }
}
