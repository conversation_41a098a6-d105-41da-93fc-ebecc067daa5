<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Utils\WorkflowGenerator;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class GitHubService
{
    private AppConfig $config;
    private Client $httpClient;
    private WorkflowGenerator $workflowGenerator;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->httpClient = new Client();
        $this->workflowGenerator = new WorkflowGenerator();
    }

    /**
     * 生成工作流文件名
     */
    public function generateWorkflowFileName(): string
    {
        return 'upload-testflight-' . date('YmdHis') . '.yml';
    }

    /**
     * 上传文件到GitHub仓库
     */
    public function uploadFile(array $githubAccount, string $repoName, string $path, string $content, string $message = 'Upload file')
    {
        try {
            // 检查文件是否已存在
            $existingFile = null;
            try {
                $response = $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/contents/{$path}", [
                    'headers' => [
                        'Authorization' => 'token ' . $githubAccount['token']
                    ]
                ]);
                $existingFile = json_decode($response->getBody()->getContents(), true);
            } catch (RequestException $e) {
                // 文件不存在，继续创建
            }

            $data = [
                'message' => $message,
                'content' => base64_encode($content)
            ];

            if ($existingFile) {
                $data['sha'] = $existingFile['sha'];
            }

            $response = $this->httpClient->put("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/contents/{$path}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            return [
                'success' => true,
                'message' => '文件上传成功'
            ];
        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => '文件上传失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取或创建上传仓库
     */
    public function getOrCreateUploadRepo(array $githubAccount): string
    {
        $repoName = 'ios-upload-' . date('Y-m');

        // 检查仓库是否存在
        try {
            $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ]
            ]);
            return $repoName;
        } catch (RequestException $e) {
            if ($e->getResponse() && $e->getResponse()->getStatusCode() === 404) {
                // 仓库不存在，创建新仓库
                $createResult = $this->createRepository($githubAccount['token'], $repoName);
                if ($createResult['success']) {
                    return $repoName;
                } else {
                    throw new \Exception('创建仓库失败: ' . $createResult['error']);
                }
            } else {
                throw new \Exception('检查仓库失败: ' . $e->getMessage());
            }
        }
    }

    /**
     * 创建GitHub仓库
     */
    public function createRepository(string $token, string $repoName, string $description = '')
    {
        try {
            $data = [
                'name' => $repoName,
                'description' => $description ?: 'iOS App Upload Repository - ' . date('Y-m'),
                'private' => true,
                'auto_init' => true
            ];

            $response = $this->httpClient->post('https://api.github.com/user/repos', [
                'headers' => [
                    'Authorization' => 'token ' . $token
                ],
                'json' => $data
            ]);

            return [
                'success' => true,
                'message' => '仓库创建成功'
            ];
        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => '仓库创建失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 检查仓库是否存在
     */
    public function repositoryExists(string $token, string $username, string $repoName): bool
    {
        try {
            $this->httpClient->get("https://api.github.com/repos/{$username}/{$repoName}", [
                'headers' => [
                    'Authorization' => 'token ' . $token
                ]
            ]);
            return true;
        } catch (RequestException $e) {
            return false;
        }
    }

    /**
     * 创建仓库密钥
     */
    public function createRepositorySecret(array $githubAccount, string $repoName, string $secretName, string $secretValue)
    {
        try {
            // 获取公钥
            $response = $this->httpClient->get("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/secrets/public-key", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ]
            ]);
            $publicKey = json_decode($response->getBody()->getContents(), true);

            // 加密密钥值
            $encryptedValue = $this->encryptSecret($secretValue, $publicKey['key']);

            // 创建密钥
            $this->httpClient->put("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/secrets/{$secretName}", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => [
                    'encrypted_value' => $encryptedValue,
                    'key_id' => $publicKey['key_id']
                ]
            ]);

            return [
                'success' => true,
                'message' => "密钥 {$secretName} 创建成功"
            ];
        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => "创建密钥 {$secretName} 失败: " . $e->getMessage()
            ];
        }
    }

    /**
     * 加密密钥值
     */
    private function encryptSecret(string $value, string $publicKey): string
    {
        // 使用 sodium 加密
        $publicKeyBinary = base64_decode($publicKey);
        $encrypted = sodium_crypto_box_seal($value, $publicKeyBinary);
        return base64_encode($encrypted);
    }

    /**
     * 触发GitHub Actions工作流
     */
    public function triggerWorkflow(array $githubAccount, string $repoName, string $workflowFile, array $inputs = [])
    {
        try {
            $data = [
                'ref' => 'main',
                'inputs' => $inputs
            ];

            $this->httpClient->post("https://api.github.com/repos/{$githubAccount['username']}/{$repoName}/actions/workflows/{$workflowFile}/dispatches", [
                'headers' => [
                    'Authorization' => 'token ' . $githubAccount['token']
                ],
                'json' => $data
            ]);

            return [
                'success' => true,
                'message' => '工作流触发成功'
            ];
        } catch (RequestException $e) {
            return [
                'success' => false,
                'error' => '工作流触发失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 设置仓库密钥（批量）
     */
    public function setupRepositorySecrets(array $githubAccount, string $repoName, array $config)
    {
        $results = [];

        if ($config['auth_method'] === 'api_key') {
            $secrets = [
                'API_KEY_ID' => $config['auth_data']['api_key_id'],
                'ISSUER_ID' => $config['auth_data']['issuer_id'],
                'API_KEY_CONTENT' => $config['auth_data']['api_key_content']
            ];
        } else {
            $secrets = [
                'APPLE_ID' => $config['auth_data']['apple_id'],
                'APP_PASSWORD' => $config['auth_data']['app_password']
            ];
        }

        foreach ($secrets as $name => $value) {
            $result = $this->createRepositorySecret($githubAccount, $repoName, $name, $value);
            $results[$name] = $result;
        }

        return $results;
    }

    /**
     * 创建上传工作流（兼容原有接口）
     */
    public function createUploadWorkflow(array $githubAccount, array $config)
    {
        try {
            // 获取或创建仓库
            $repoName = $this->getOrCreateUploadRepo($githubAccount);

            // 生成工作流内容
            $workflowContent = $this->generateWorkflowContent($config);

            // 生成工作流文件名
            $workflowFileName = $this->generateWorkflowFileName();
            $workflowPath = ".github/workflows/{$workflowFileName}";

            // 上传工作流文件
            $uploadResult = $this->uploadFile(
                $githubAccount,
                $repoName,
                $workflowPath,
                $workflowContent,
                'Add upload workflow'
            );

            if (!$uploadResult['success']) {
                throw new \Exception('上传工作流文件失败: ' . $uploadResult['error']);
            }

            // 设置仓库密钥
            $this->setupRepositorySecrets($githubAccount, $repoName, $config);

            // 触发工作流
            $triggerResult = $this->triggerWorkflow(
                $githubAccount,
                $repoName,
                $workflowFileName,
                ['record_id' => $config['upload_id']]
            );

            if (!$triggerResult['success']) {
                throw new \Exception('触发工作流失败: ' . $triggerResult['error']);
            }

            return [
                'success' => true,
                'repo_name' => $repoName,
                'workflow_id' => uniqid('workflow_'),
                'github_url' => "https://github.com/{$githubAccount['username']}/{$repoName}/actions"
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 生成工作流内容（私有方法）
     */
    private function generateWorkflowContent(array $config): string
    {
        // 准备工作流数据
        $workflowData = [
            'record_id' => $config['upload_id'],
            'callback_url' => 'https://api.ios.xxyx.cn/api/workflow/callback',
            'download_url' => $config['download_url']
        ];

        // 转换 auth_data（处理 MongoDB BSONDocument）
        $authData = is_array($config['auth_data']) ? $config['auth_data'] : (array)$config['auth_data'];

        // 根据认证方式生成工作流内容
        if ($config['auth_method'] === 'api_key') {
            return $this->workflowGenerator->generateAPIKeyWorkflow(array_merge($workflowData, $authData));
        } else {
            return $this->workflowGenerator->generateAppleIDWorkflow(array_merge($workflowData, $authData));
        }
    }
}
