<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class UploadRecord
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('upload_records');
    }



    /**
     * 创建上传记录
     */
    public function create(array $data): ObjectId
    {
        $recordData = [
            'user_id' => new ObjectId($data['user_id']),
            'filename' => $data['filename'],
            'file_size' => $data['file_size'],
            'auth_method' => $data['auth_method'],
            'auth_data' => $data['auth_data'],
            'release_notes' => $data['release_notes'] ?? '',
            'status' => $data['status'] ?? 'pending',

            // 临时文件路径（如果提供）
            'temp_file_path' => $data['temp_file_path'] ?? null,

            // 客户端解析的应用信息
            'bundle_id' => $data['bundle_id'] ?? 'unknown.bundle.id',
            'app_name' => $data['app_name'] ?? $data['filename'],
            'version' => $data['version'] ?? '1.0.0',
            'build' => $data['build'] ?? '1',
            'minimum_os_version' => $data['minimum_os_version'] ?? 'Unknown',
            'supported_devices' => $data['supported_devices'] ?? [],
            'required_capabilities' => $data['required_capabilities'] ?? [],
            'icon_base64' => $data['icon_base64'] ?? null,
            'parsed_info' => $data['parsed_info'] ?? null,
            'parsing_error' => $data['parsing_error'] ?? null,

            // 以下字段将在工作流中填充，但如果提供了就使用提供的值
            'stored_filename' => $data['stored_filename'] ?? null,
            'github_account' => isset($data['github_account']) ? new ObjectId($data['github_account']) : null,
            'queue_position' => $data['queue_position'] ?? null,
            'workflow_id' => $data['workflow_id'] ?? null,
            'upload_result' => $data['upload_result'] ?? null,

            // 时间戳
            'created_at' => new \MongoDB\BSON\UTCDateTime(),
            'started_at' => null,
            'completed_at' => null,

            // 工作流执行信息
            'workflow_ip' => null,        // 工作流执行的IP地址
            'workflow_duration' => null   // 工作流执行时长（秒）
        ];

        $result = $this->collection->insertOne($recordData);
        return $result->getInsertedId();
    }

    public function findById(string $id): ?array
    {
        $record = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $record ? $this->documentToArray($record) : null;
    }

    public function findByUserId(string $userId, int $limit = 50, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            ['user_id' => new ObjectId($userId)],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    public function getQueuedRecords(): array
    {
        $cursor = $this->collection->find(
            ['status' => 'queued'],
            ['sort' => ['created_at' => 1]]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    public function getUploadingRecords(): array
    {
        $cursor = $this->collection->find(['status' => 'uploading']);

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 更新进度值
     */
    public function updateProgress(string $recordId, int $progress): bool
    {
        try {
            $result = $this->collection->updateOne(
                ['_id' => new ObjectId($recordId)],
                [
                    '$set' => [
                        'max_progress' => $progress,
                        'updated_at' => new \MongoDB\BSON\UTCDateTime()
                    ]
                ]
            );

            return $result->getModifiedCount() > 0;
        } catch (\Exception $e) {
            error_log("更新进度失败: " . $e->getMessage());
            return false;
        }
    }

    public function updateStatus(string $recordId, string $status, array $additionalData = []): bool
    {
        $updateData = ['status' => $status];

        if ($status === 'uploading') {
            $updateData['started_at'] = new \MongoDB\BSON\UTCDateTime();
        } elseif (in_array($status, ['success', 'failed'])) {
            $updateData['completed_at'] = new \MongoDB\BSON\UTCDateTime();
        }

        if (!empty($additionalData)) {
            $updateData = array_merge($updateData, $additionalData);
        }

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => $updateData]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateQueuePosition(string $recordId, int $position): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => ['queue_position' => $position]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateWorkflowId(string $recordId, string $workflowId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => ['workflow_id' => $workflowId]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateUploadResult(string $recordId, array $result): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => ['upload_result' => $result]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function countRecords(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    public function countQueuedRecords(): int
    {
        return $this->collection->countDocuments(['status' => 'queued']);
    }

    public function countUploadingRecords(): int
    {
        return $this->collection->countDocuments(['status' => 'uploading']);
    }

    /**
     * 按日期统计记录数
     */
    public function countRecordsByDate(\DateTime $date): int
    {
        $startOfDay = new \MongoDB\BSON\UTCDateTime($date->getTimestamp() * 1000);
        $endOfDay = new \MongoDB\BSON\UTCDateTime(($date->getTimestamp() + 86400) * 1000);

        return $this->collection->countDocuments([
            'created_at' => [
                '$gte' => $startOfDay,
                '$lt' => $endOfDay
            ]
        ]);
    }

    /**
     * 获取最近的记录
     */
    public function getRecentRecords(int $limit = 20): array
    {
        $cursor = $this->collection->find([], [
            'sort' => ['created_at' => -1],
            'limit' => $limit
        ]);

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 查找符合批量删除条件的记录
     */
    public function findRecordsForBatchDelete(array $conditions, array $sortOptions, int $limit): array
    {
        $cursor = $this->collection->find($conditions, [
            'sort' => $sortOptions,
            'limit' => $limit
        ]);

        return $cursor->toArray();
    }

    public function getNextQueuedRecord(): ?array
    {
        $record = $this->collection->findOne(
            ['status' => 'queued'],
            ['sort' => ['created_at' => 1]]
        );

        return $record ? $this->documentToArray($record) : null;
    }

    public function deleteRecord(string $recordId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($recordId)]);
        return $result->getDeletedCount() > 0;
    }

    public function getAllRecordsWithUser()
    {
        $collection = $this->db->getCollection('upload_records');
        $userCollection = $this->db->getCollection('users');
        $records = iterator_to_array($collection->find([], ['sort' => ['created_at' => -1]]));
        // 关联用户名
        foreach ($records as &$rec) {
            if (isset($rec['user_id'])) {
                $user = $userCollection->findOne(['_id' => $rec['user_id']]);
                $rec['username'] = $user['username'] ?? '';
            }
        }
        return array_map([$this, 'documentToArray'], $records);
    }

    /**
     * 获取所有记录（支持分页）
     */
    public function getAllRecordsWithUserPaginated(int $page = 1, int $pageSize = 10): array
    {
        $collection = $this->db->getCollection('upload_records');
        $userCollection = $this->db->getCollection('users');

        // 计算偏移量
        $skip = ($page - 1) * $pageSize;

        // 获取总记录数
        $totalCount = $collection->countDocuments([]);

        // 获取当前页记录
        $records = iterator_to_array($collection->find(
            [],
            [
                'sort' => ['created_at' => -1],
                'skip' => $skip,
                'limit' => $pageSize
            ]
        ));

        // 关联用户名
        foreach ($records as &$rec) {
            if (isset($rec['user_id'])) {
                $user = $userCollection->findOne(['_id' => $rec['user_id']]);
                $rec['username'] = $user['username'] ?? '';
            }
        }

        // 计算分页信息
        $totalPages = ceil($totalCount / $pageSize);

        return [
            'records' => array_map([$this, 'documentToArray'], $records),
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null,
            ]
        ];
    }

    /**
     * 根据用户ID获取记录（支持分页）
     */
    public function findByUserIdPaginated(string $userId, int $page = 1, int $pageSize = 10): array
    {
        // 计算偏移量
        $skip = ($page - 1) * $pageSize;

        // 获取总记录数
        $totalCount = $this->collection->countDocuments(['user_id' => new ObjectId($userId)]);

        // 获取当前页记录
        $cursor = $this->collection->find(
            ['user_id' => new ObjectId($userId)],
            [
                'limit' => $pageSize,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        // 计算分页信息
        $totalPages = ceil($totalCount / $pageSize);

        return [
            'records' => $records,
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null,
            ]
        ];
    }

    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        if (isset($data['user_id']) && $data['user_id'] instanceof ObjectId) {
            $data['user_id'] = (string) $data['user_id'];
        }

        if (isset($data['github_account']) && $data['github_account'] instanceof ObjectId) {
            $data['github_account'] = (string) $data['github_account'];
        }

        // 转换日期为ISO字符串
        if (isset($data['created_at']) && $data['created_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['created_at'] = $data['created_at']->toDateTime()->format('c');
        }

        if (isset($data['started_at']) && $data['started_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['started_at'] = $data['started_at']->toDateTime()->format('c');
        }

        if (isset($data['completed_at']) && $data['completed_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['completed_at'] = $data['completed_at']->toDateTime()->format('c');
        }

        // 转换 upload_result 中的 BSONDocument 为数组
        if (isset($data['upload_result'])) {
            $data['upload_result'] = $this->convertBSONToArray($data['upload_result']);
        }

        // 转换 error_details 中的 BSONDocument 为数组
        if (isset($data['error_details'])) {
            $data['error_details'] = $this->convertBSONToArray($data['error_details']);
        }

        // 转换 parsed_info 中的 BSONDocument 为数组
        if (isset($data['parsed_info'])) {
            $data['parsed_info'] = $this->convertBSONToArray($data['parsed_info']);
        }

        // 转换 supported_devices 数组
        if (isset($data['supported_devices'])) {
            $data['supported_devices'] = $this->convertBSONToArray($data['supported_devices']);
        }

        // 转换 required_capabilities 数组
        if (isset($data['required_capabilities'])) {
            $data['required_capabilities'] = $this->convertBSONToArray($data['required_capabilities']);
        }

        return $data;
    }

    /**
     * 递归转换 BSON 对象为 PHP 数组
     */
    private function convertBSONToArray($value)
    {
        if ($value instanceof \MongoDB\Model\BSONDocument) {
            // 使用 getArrayCopy() 方法转换 BSONDocument
            $array = $value->getArrayCopy();
            // 递归转换嵌套的 BSON 对象
            foreach ($array as $key => $val) {
                $array[$key] = $this->convertBSONToArray($val);
            }
            return $array;
        } elseif ($value instanceof \MongoDB\Model\BSONArray) {
            // 转换 BSONArray
            $array = $value->getArrayCopy();
            foreach ($array as $key => $val) {
                $array[$key] = $this->convertBSONToArray($val);
            }
            return $array;
        } elseif (is_array($value)) {
            // 递归处理普通数组
            foreach ($value as $key => $val) {
                $value[$key] = $this->convertBSONToArray($val);
            }
            return $value;
        } else {
            // 返回原始值
            return $value;
        }
    }

    /**
     * 查找待处理的上传任务
     */
    public function findPendingUploads(int $limit = 10): array
    {
        $cursor = $this->collection->find(
            ['status' => 'pending'],
            [
                'limit' => $limit,
                'sort' => ['created_at' => 1] // 按创建时间升序
            ]
        );

        $uploads = [];
        foreach ($cursor as $document) {
            $uploads[] = $this->documentToArray($document);
        }

        return $uploads;
    }

    /**
     * 根据GitHub账号统计使用次数
     */
    public function countByGitHubAccount(string $githubAccountId): int
    {
        return $this->collection->countDocuments([
            'github_account' => new ObjectId($githubAccountId)
        ]);
    }

    /**
     * 更新上传记录
     */
    public function updateById(string $id, array $updateData): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($id)],
            ['$set' => $updateData]
        );

        return $result->getModifiedCount() > 0;
    }
}
