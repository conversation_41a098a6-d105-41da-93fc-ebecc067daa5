<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class GitHubAccount
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('github_accounts');
    }

    public function create(array $data): ObjectId
    {
        $accountData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'token' => $data['token'],
            'remaining_minutes' => $data['remaining_minutes'] ?? 0,
            'status' => $data['status'] ?? 'active',
            'last_check' => new \MongoDB\BSON\UTCDateTime(),
            'created_at' => new \MongoDB\BSON\UTCDateTime(),

            // GitHub Actions使用情况
            'total_usage_minutes' => 0,      // GitHub Actions总使用分钟数
            'is_in_use' => false,            // 是否正在使用中

            // 工作流执行统计
            'total_usage_seconds' => 0,      // 工作流总执行时长（秒）
            'usage_count' => 0,              // 工作流执行次数
            'last_used_at' => null           // 最后使用时间
        ];

        $result = $this->collection->insertOne($accountData);
        return $result->getInsertedId();
    }

    public function findById(string $id): ?array
    {
        $account = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $account ? $this->documentToArray($account) : null;
    }

    public function findByUsername(string $username): ?array
    {
        $account = $this->collection->findOne(['username' => $username]);
        return $account ? $this->documentToArray($account) : null;
    }

    public function getAllAccounts(): array
    {
        $cursor = $this->collection->find(
            [], // 获取所有账号，不限制状态
            ['sort' => ['created_at' => -1]] // 按创建时间倒序
        );

        $accounts = [];
        foreach ($cursor as $account) {
            $accounts[] = $this->documentToArray($account);
        }

        return $accounts;
    }

    public function getBestAccount(): ?array
    {
        // 优先选择状态为active且未被使用的账号
        // 如果没有未使用的，则选择使用时间最少的

        // 首先尝试找到未使用的账号
        $account = $this->collection->findOne(
            [
                'status' => 'active',
                'is_in_use' => ['$ne' => true]
            ],
            [
                'sort' => ['last_used_at' => 1], // 按最后使用时间升序
                'limit' => 1
            ]
        );

        // 如果没有未使用的账号，选择使用时间最少的
        if (!$account) {
            $account = $this->collection->findOne(
                ['status' => 'active'],
                [
                    'sort' => ['total_usage_minutes' => 1], // 按总使用时间升序
                    'limit' => 1
                ]
            );
        }

        return $account ? $this->documentToArray($account) : null;
    }

    public function updateRemainingMinutes(string $accountId, int $minutes): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            [
                '$set' => [
                    'remaining_minutes' => $minutes,
                    'last_check' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateStatus(string $accountId, string $status): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            ['$set' => ['status' => $status]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function deleteAccount(string $accountId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($accountId)]);
        return $result->getDeletedCount() > 0;
    }

    public function countActiveAccounts(): int
    {
        return $this->collection->countDocuments(['status' => 'active']);
    }

    public function countAccounts(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    public function getAccountToken(string $accountId): ?string
    {
        $account = $this->collection->findOne(
            ['_id' => new ObjectId($accountId)],
            ['projection' => ['token' => 1]]
        );

        return $account ? $account->token : null;
    }

    /**
     * 标记账号为使用中
     */
    public function markAsInUse(string $accountId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            [
                '$set' => [
                    'is_in_use' => true,
                    'last_used_at' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 标记账号为空闲
     */
    public function markAsIdle(string $accountId, int $usageMinutes = 0): bool
    {
        $updateData = [
            'is_in_use' => false,
            'last_used_at' => new \MongoDB\BSON\UTCDateTime()
        ];

        // 如果提供了使用分钟数，累加到总使用时间
        if ($usageMinutes > 0) {
            $updateData['$inc'] = ['total_usage_minutes' => $usageMinutes];
        }

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            ['$set' => $updateData]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新账号的GitHub API剩余分钟数
     */
    public function updateApiRemainingMinutes(string $accountId, int $remainingMinutes): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            [
                '$set' => [
                    'api_remaining_minutes' => $remainingMinutes,
                    'last_api_check' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 重置账号的使用分钟数
     */
    public function resetUsageMinutes(string $accountId, int $minutes = 0): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($accountId)],
            [
                '$set' => [
                    'total_usage_minutes' => $minutes,
                    'is_in_use' => false,
                    'last_used_at' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        // 转换日期为ISO字符串
        if (isset($data['created_at']) && $data['created_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['created_at'] = $data['created_at']->toDateTime()->format('c');
        }

        if (isset($data['last_check']) && $data['last_check'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['last_check'] = $data['last_check']->toDateTime()->format('c');
        }

        if (isset($data['last_used_at']) && $data['last_used_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['last_used_at'] = $data['last_used_at']->toDateTime()->format('c');
        }

        // 为旧数据添加默认值
        $data['total_usage_minutes'] = $data['total_usage_minutes'] ?? 0;
        $data['is_in_use'] = $data['is_in_use'] ?? false;
        $data['total_usage_seconds'] = $data['total_usage_seconds'] ?? 0;
        $data['usage_count'] = $data['usage_count'] ?? 0;

        // 不返回token字段，保护敏感信息
        unset($data['token']);

        return $data;
    }

    /**
     * 更新账号使用时长统计
     */
    public function updateUsageStats(string $accountId, int $durationSeconds): bool
    {
        try {
            // 尝试不同的查询方式
            $filter = null;

            // 首先尝试作为ObjectId
            try {
                $filter = ['_id' => new ObjectId($accountId)];
            } catch (\Exception $e) {
                // 如果不是有效的ObjectId，尝试作为username查询
                $filter = ['username' => $accountId];
            }

            $result = $this->collection->updateOne(
                $filter,
                [
                    '$inc' => [
                        'total_usage_seconds' => $durationSeconds,
                        'usage_count' => 1
                    ],
                    '$set' => [
                        'last_used_at' => new \MongoDB\BSON\UTCDateTime()
                    ]
                ]
            );

            return $result->getModifiedCount() > 0;
        } catch (\Exception $e) {
            error_log("更新GitHub账号使用统计失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取账号使用统计
     */
    public function getUsageStats(string $accountId): ?array
    {
        try {
            // 尝试不同的查询方式
            $filter = null;

            // 首先尝试作为ObjectId
            try {
                $filter = ['_id' => new ObjectId($accountId)];
            } catch (\Exception $e) {
                // 如果不是有效的ObjectId，尝试作为username查询
                $filter = ['username' => $accountId];
            }

            $account = $this->collection->findOne(
                $filter,
                [
                    'projection' => [
                        'username' => 1,
                        'total_usage_seconds' => 1,
                        'usage_count' => 1,
                        'last_used_at' => 1
                    ]
                ]
            );

            if (!$account) {
                return null;
            }

            $data = $this->documentToArray($account);

            // 计算平均使用时长
            $data['average_duration'] = $data['usage_count'] > 0
                ? round($data['total_usage_seconds'] / $data['usage_count'], 2)
                : 0;

            // 格式化总时长为可读格式
            $data['total_usage_formatted'] = $this->formatDuration($data['total_usage_seconds']);

            return $data;
        } catch (\Exception $e) {
            error_log("获取GitHub账号使用统计失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 格式化时长为可读格式
     */
    private function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . '分' . ($remainingSeconds > 0 ? $remainingSeconds . '秒' : '');
        } else {
            $hours = floor($seconds / 3600);
            $remainingMinutes = floor(($seconds % 3600) / 60);
            $remainingSeconds = $seconds % 60;
            $result = $hours . '小时';
            if ($remainingMinutes > 0) {
                $result .= $remainingMinutes . '分';
            }
            if ($remainingSeconds > 0) {
                $result .= $remainingSeconds . '秒';
            }
            return $result;
        }
    }
}
