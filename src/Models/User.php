<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class User
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('users');
    }

    public function create(array $data): ObjectId
    {
        // 验证角色有效性
        $validRoles = ['user', 'admin', 'super_admin'];
        $role = $data['role'] ?? 'user';
        if (!in_array($role, $validRoles)) {
            throw new \InvalidArgumentException("无效的用户角色: {$role}");
        }

        $userData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'password' => password_hash($data['password'], PASSWORD_DEFAULT),
            'role' => $role,
            'status' => $data['status'] ?? 'inactive',
            'created_at' => new \MongoDB\BSON\UTCDateTime(),
            'activated_at' => null,
            'last_login' => null
        ];

        $result = $this->collection->insertOne($userData);
        return $result->getInsertedId();
    }

    public function findByEmail(string $email): ?array
    {
        $user = $this->collection->findOne(['email' => $email]);
        return $user ? $this->documentToArray($user) : null;
    }

    public function findById(string $id): ?array
    {
        $user = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $user ? $this->documentToArray($user) : null;
    }

    public function findByUsername(string $username): ?array
    {
        $user = $this->collection->findOne(['username' => $username]);
        return $user ? $this->documentToArray($user) : null;
    }

    public function updateLastLogin(string $userId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($userId)],
            ['$set' => ['last_login' => new \MongoDB\BSON\UTCDateTime()]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function activate(string $userId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($userId)],
            [
                '$set' => [
                    'status' => 'active',
                    'activated_at' => new \MongoDB\BSON\UTCDateTime()
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    public function activateWithExpiry(string $userId, int $expiresAt): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($userId)],
            [
                '$set' => [
                    'status' => 'active',
                    'activated_at' => new \MongoDB\BSON\UTCDateTime(),
                    'expires_at' => new \MongoDB\BSON\UTCDateTime($expiresAt * 1000) // MongoDB需要毫秒
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    public function updateStatus(string $userId, string $status): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($userId)],
            ['$set' => ['status' => $status]]
        );
        return $result->getModifiedCount() > 0;
    }

    public function getAllUsers(int $limit = 50, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            [],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $users = [];
        foreach ($cursor as $user) {
            $users[] = $this->documentToArray($user);
        }

        return $users;
    }

    /**
     * 根据当前用户权限获取可见的用户列表
     */
    public function getUsersByPermission(array $currentUser, int $limit = 50, int $skip = 0): array
    {
        $filter = [];

        // 根据当前用户角色设置过滤条件
        if ($currentUser['role'] === 'super_admin') {
            // 超级管理员可以看到所有用户，但不包括其他超级管理员
            $filter = ['role' => ['$ne' => 'super_admin']];
        } elseif ($currentUser['role'] === 'admin') {
            // 普通管理员只能看到普通用户
            $filter = ['role' => 'user'];
        } else {
            // 普通用户不应该调用这个方法，但为了安全返回空数组
            return [];
        }

        $cursor = $this->collection->find(
            $filter,
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $users = [];
        foreach ($cursor as $user) {
            $users[] = $this->documentToArray($user);
        }

        return $users;
    }

    public function countUsers(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    public function deleteUser(string $userId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($userId)]);
        return $result->getDeletedCount() > 0;
    }

    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * 更新用户密码
     */
    public function updatePassword(string $userId, string $hashedPassword): bool
    {
        try {
            $result = $this->collection->updateOne(
                ['_id' => new \MongoDB\BSON\ObjectId($userId)],
                [
                    '$set' => [
                        'password' => $hashedPassword,
                        'updated_at' => new \MongoDB\BSON\UTCDateTime()
                    ]
                ]
            );

            return $result->getModifiedCount() > 0;
        } catch (\Exception $e) {
            error_log("更新密码失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查用户是否为超级管理员
     */
    public function isSuperAdmin(array $user): bool
    {
        return isset($user['role']) && $user['role'] === 'super_admin';
    }

    /**
     * 检查用户是否为管理员（包括超级管理员）
     */
    public function isAdmin(array $user): bool
    {
        return isset($user['role']) && in_array($user['role'], ['admin', 'super_admin']);
    }

    /**
     * 获取所有管理员用户
     */
    public function getAllAdmins(): array
    {
        $cursor = $this->collection->find(
            ['role' => ['$in' => ['admin', 'super_admin']]],
            ['sort' => ['created_at' => -1]]
        );

        $admins = [];
        foreach ($cursor as $admin) {
            $admins[] = $this->documentToArray($admin);
        }

        return $admins;
    }

    /**
     * 检查是否可以删除指定用户
     */
    public function canDeleteUser(array $currentUser, array $targetUser): bool
    {
        // 不能删除自己
        if ($currentUser['_id'] === $targetUser['_id']) {
            return false;
        }

        // 超级管理员可以删除任何人
        if ($this->isSuperAdmin($currentUser)) {
            return true;
        }

        // 普通管理员不能删除管理员或超级管理员
        if ($this->isAdmin($currentUser) && !$this->isAdmin($targetUser)) {
            return true;
        }

        return false;
    }

    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        // 转换日期为ISO字符串
        if (isset($data['created_at']) && $data['created_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['created_at'] = $data['created_at']->toDateTime()->format('c');
        }

        if (isset($data['activated_at']) && $data['activated_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['activated_at'] = $data['activated_at']->toDateTime()->format('c');
        }

        if (isset($data['last_login']) && $data['last_login'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['last_login'] = $data['last_login']->toDateTime()->format('c');
        }

        if (isset($data['expires_at']) && $data['expires_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['expires_at'] = $data['expires_at']->toDateTime()->format('c');
        }

        return $data;
    }
}
