<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class DistributionRecord
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('distribution_records');
    }

    /**
     * 创建分发记录
     */
    public function create(array $data): ObjectId
    {
        $recordData = [
            'resign_record_id' => new ObjectId($data['resign_record_id']),
            'user_id' => new ObjectId($data['user_id']),

            // 应用信息
            'app_name' => $data['app_name'],
            'bundle_id' => $data['bundle_id'],
            'version' => $data['version'],
            'build' => $data['build'],
            'icon_base64' => $data['icon_base64'] ?? null,

            // 分发链接
            'install_url' => $data['install_url'],
            'plist_url' => $data['plist_url'],
            'download_url' => $data['download_url'],
            'qr_code_url' => $data['qr_code_url'] ?? null,

            // 分发配置
            'title' => $data['title'] ?? $data['app_name'],
            'subtitle' => $data['subtitle'] ?? null,
            'description' => $data['description'] ?? null,
            'minimum_os_version' => $data['minimum_os_version'] ?? '10.0',

            // 状态和时间
            'status' => $data['status'] ?? 'active', // active, inactive, expired
            'created_at' => new \MongoDB\BSON\UTCDateTime(),
            'expires_at' => isset($data['expires_at']) ? new \MongoDB\BSON\UTCDateTime($data['expires_at'] * 1000) : null,

            // 统计信息
            'install_count' => 0,
            'last_installed_at' => null,
            'view_count' => 0,
            'last_viewed_at' => null,

            // 访问控制
            'access_password' => $data['access_password'] ?? null,
            'allowed_devices' => $data['allowed_devices'] ?? [],
            'max_installs' => $data['max_installs'] ?? null,

            // 元数据
            'user_agent_stats' => [],
            'ip_stats' => []
        ];

        $result = $this->collection->insertOne($recordData);
        return $result->getInsertedId();
    }

    /**
     * 根据ID查找记录
     */
    public function findById(string $id): ?array
    {
        $record = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $record ? $this->documentToArray($record) : null;
    }

    /**
     * 根据重签记录ID查找分发记录
     */
    public function findByResignRecordId(string $resignRecordId): ?array
    {
        $record = $this->collection->findOne(['resign_record_id' => new ObjectId($resignRecordId)]);
        return $record ? $this->documentToArray($record) : null;
    }

    /**
     * 更新安装统计
     */
    public function updateInstallStats(string $recordId, array $metadata = []): bool
    {
        $updateData = [
            '$inc' => ['install_count' => 1],
            '$set' => ['last_installed_at' => new \MongoDB\BSON\UTCDateTime()]
        ];

        // 更新用户代理统计
        if (isset($metadata['user_agent'])) {
            $updateData['$inc']['user_agent_stats.' . md5($metadata['user_agent'])] = 1;
        }

        // 更新IP统计
        if (isset($metadata['ip'])) {
            $updateData['$inc']['ip_stats.' . md5($metadata['ip'])] = 1;
        }

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            $updateData
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新查看统计
     */
    public function updateViewStats(string $recordId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            [
                '$inc' => ['view_count' => 1],
                '$set' => ['last_viewed_at' => new \MongoDB\BSON\UTCDateTime()]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新分发状态
     */
    public function updateStatus(string $recordId, string $status): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => ['status' => $status]]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新分发链接
     */
    public function updateUrls(string $recordId, array $urls): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => $urls]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 获取用户的分发记录
     */
    public function getUserRecords(string $userId, int $limit = 20, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            ['user_id' => new ObjectId($userId)],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 获取所有分发记录（管理员用）
     */
    public function getAllRecords(int $limit = 50, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            [],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 统计记录数量
     */
    public function countRecords(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    /**
     * 统计用户记录数量
     */
    public function countUserRecords(string $userId): int
    {
        return $this->collection->countDocuments(['user_id' => new ObjectId($userId)]);
    }

    /**
     * 获取热门应用统计
     */
    public function getPopularApps(int $limit = 10): array
    {
        $pipeline = [
            [
                '$match' => [
                    'status' => 'active',
                    'install_count' => ['$gt' => 0]
                ]
            ],
            [
                '$sort' => ['install_count' => -1]
            ],
            [
                '$limit' => $limit
            ],
            [
                '$project' => [
                    'app_name' => 1,
                    'bundle_id' => 1,
                    'version' => 1,
                    'install_count' => 1,
                    'created_at' => 1
                ]
            ]
        ];

        $cursor = $this->collection->aggregate($pipeline);
        $results = [];

        foreach ($cursor as $doc) {
            $results[] = $this->documentToArray($doc);
        }

        return $results;
    }

    /**
     * 获取分发统计
     */
    public function getDistributionStats(): array
    {
        $pipeline = [
            [
                '$group' => [
                    '_id' => null,
                    'total_distributions' => ['$sum' => 1],
                    'total_installs' => ['$sum' => '$install_count'],
                    'total_views' => ['$sum' => '$view_count'],
                    'active_distributions' => [
                        '$sum' => [
                            '$cond' => [
                                ['$eq' => ['$status', 'active']],
                                1,
                                0
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $cursor = $this->collection->aggregate($pipeline);
        $result = $cursor->toArray();

        // 将BSONDocument转换为数组
        $stats = $result[0] ?? [
            'total_distributions' => 0,
            'total_installs' => 0,
            'total_views' => 0,
            'active_distributions' => 0
        ];

        // 确保返回的是数组而不是BSONDocument
        if ($stats instanceof \MongoDB\Model\BSONDocument) {
            $stats = $this->documentToArray($stats);
        }

        return $stats;
    }

    /**
     * 删除记录
     */
    public function delete(string $recordId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($recordId)]);
        return $result->getDeletedCount() > 0;
    }

    /**
     * 文档转数组
     */
    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        $objectIdFields = ['_id', 'resign_record_id', 'user_id'];
        foreach ($objectIdFields as $field) {
            if (isset($data[$field]) && $data[$field] instanceof ObjectId) {
                $data[$field] = (string) $data[$field];
            }
        }

        // 转换日期为ISO字符串
        $dateFields = ['created_at', 'expires_at', 'last_installed_at', 'last_viewed_at'];
        foreach ($dateFields as $field) {
            if (isset($data[$field]) && $data[$field] instanceof \MongoDB\BSON\UTCDateTime) {
                $data[$field] = $data[$field]->toDateTime()->format('c');
            }
        }

        return $data;
    }
}
