<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class ResignRecord
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('resign_records');
    }

    /**
     * 创建重签记录
     */
    public function create(array $data): ObjectId
    {
        $recordData = [
            'user_id' => new ObjectId($data['user_id']),
            'original_ipa_path' => $data['original_ipa_path'],
            'original_filename' => $data['original_filename'],
            'resigned_ipa_path' => $data['resigned_ipa_path'] ?? null,
            'resigned_filename' => $data['resigned_filename'] ?? null,

            // 应用信息
            'bundle_id' => $data['bundle_id'] ?? null,
            'app_name' => $data['app_name'] ?? null,
            'version' => $data['version'] ?? null,
            'build' => $data['build'] ?? null,
            'icon_base64' => $data['icon_base64'] ?? null,
            'original_file_size' => $data['original_file_size'] ?? 0,
            'resigned_file_size' => $data['resigned_file_size'] ?? 0,

            // 证书信息
            'p12_certificate_id' => isset($data['p12_certificate_id']) ? new ObjectId($data['p12_certificate_id']) : null,
            'mobileprovision_id' => isset($data['mobileprovision_id']) ? new ObjectId($data['mobileprovision_id']) : null,
            'certificate_pair_id' => $data['certificate_pair_id'] ?? null,

            // 重签配置
            'new_bundle_id' => $data['new_bundle_id'] ?? null,
            'new_app_name' => $data['new_app_name'] ?? null,
            'resign_options' => $data['resign_options'] ?? [],

            // 状态和时间
            'status' => $data['status'] ?? 'pending', // pending, processing, success, failed
            'created_at' => new \MongoDB\BSON\UTCDateTime(),
            'started_at' => null,
            'completed_at' => null,

            // 错误信息
            'error_message' => null,
            'error_details' => null,

            // 分发信息
            'distribution_enabled' => $data['distribution_enabled'] ?? false,
            'install_url' => null,
            'plist_url' => null,
            'qr_code_url' => null,

            // 统计信息
            'download_count' => 0,
            'last_downloaded_at' => null
        ];

        $result = $this->collection->insertOne($recordData);
        return $result->getInsertedId();
    }

    /**
     * 根据ID查找记录
     */
    public function findById(string $id): ?array
    {
        $record = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $record ? $this->documentToArray($record) : null;
    }

    /**
     * 更新记录状态
     */
    public function updateStatus(string $recordId, string $status, array $additionalData = []): bool
    {
        $updateData = ['status' => $status];

        if ($status === 'processing') {
            $updateData['started_at'] = new \MongoDB\BSON\UTCDateTime();
        } elseif (in_array($status, ['success', 'failed'])) {
            $updateData['completed_at'] = new \MongoDB\BSON\UTCDateTime();
        }

        if (!empty($additionalData)) {
            $updateData = array_merge($updateData, $additionalData);
        }

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => $updateData]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新重签结果
     */
    public function updateResignResult(string $recordId, array $resultData): bool
    {
        $updateData = [
            'resigned_ipa_path' => $resultData['resigned_ipa_path'],
            'resigned_filename' => $resultData['resigned_filename'],
            'resigned_file_size' => $resultData['resigned_file_size'],
            'status' => 'success',
            'completed_at' => new \MongoDB\BSON\UTCDateTime()
        ];

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => $updateData]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新分发信息
     */
    public function updateDistributionInfo(string $recordId, array $distributionData): bool
    {
        $updateData = [
            'install_url' => $distributionData['install_url'],
            'plist_url' => $distributionData['plist_url'],
            'qr_code_url' => $distributionData['qr_code_url'] ?? null
        ];

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => $updateData]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新下载统计
     */
    public function updateDownloadStats(string $recordId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            [
                '$inc' => ['download_count' => 1],
                '$set' => ['last_downloaded_at' => new \MongoDB\BSON\UTCDateTime()]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 获取用户的重签记录
     */
    public function getUserRecords(string $userId, int $limit = 20, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            ['user_id' => new ObjectId($userId)],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 获取所有重签记录（管理员用）
     */
    public function getAllRecords(int $limit = 50, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            [],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 统计记录数量
     */
    public function countRecords(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    /**
     * 统计用户记录数量
     */
    public function countUserRecords(string $userId): int
    {
        return $this->collection->countDocuments(['user_id' => new ObjectId($userId)]);
    }

    /**
     * 获取待处理的记录
     */
    public function getPendingRecords(int $limit = 10): array
    {
        $cursor = $this->collection->find(
            ['status' => 'pending'],
            [
                'limit' => $limit,
                'sort' => ['created_at' => 1] // 先进先出
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 删除记录
     */
    public function delete(string $recordId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($recordId)]);
        return $result->getDeletedCount() > 0;
    }

    /**
     * 获取过期的记录
     */
    public function getExpiredRecords(\MongoDB\BSON\UTCDateTime $cutoffDate): array
    {
        $cursor = $this->collection->find([
            'created_at' => ['$lt' => $cutoffDate]
        ]);

        $records = [];
        foreach ($cursor as $document) {
            $records[] = $this->documentToArray($document);
        }

        return $records;
    }

    /**
     * 获取所有记录（支持分页）
     */
    public function getAllRecordsWithUserPaginated(int $page = 1, int $pageSize = 10): array
    {
        $userCollection = $this->db->getCollection('users');

        // 计算偏移量
        $skip = ($page - 1) * $pageSize;

        // 获取总记录数
        $totalCount = $this->collection->countDocuments([]);

        // 获取当前页记录
        $records = iterator_to_array($this->collection->find(
            [],
            [
                'sort' => ['created_at' => -1],
                'skip' => $skip,
                'limit' => $pageSize
            ]
        ));

        // 关联用户名
        foreach ($records as &$rec) {
            if (isset($rec['user_id'])) {
                $user = $userCollection->findOne(['_id' => $rec['user_id']]);
                $rec['username'] = $user['username'] ?? '';
            }
        }

        // 计算分页信息
        $totalPages = ceil($totalCount / $pageSize);

        return [
            'records' => array_map([$this, 'documentToArray'], $records),
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null,
            ]
        ];
    }

    /**
     * 根据用户ID获取记录（支持分页）
     */
    public function findByUserIdPaginated(string $userId, int $page = 1, int $pageSize = 10): array
    {
        // 计算偏移量
        $skip = ($page - 1) * $pageSize;

        // 获取总记录数
        $totalCount = $this->collection->countDocuments(['user_id' => new ObjectId($userId)]);

        // 获取当前页记录
        $cursor = $this->collection->find(
            ['user_id' => new ObjectId($userId)],
            [
                'limit' => $pageSize,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        // 计算分页信息
        $totalPages = ceil($totalCount / $pageSize);

        return [
            'records' => $records,
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null,
            ]
        ];
    }

    /**
     * 文档转数组
     */
    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        $objectIdFields = ['_id', 'user_id', 'p12_certificate_id', 'mobileprovision_id'];
        foreach ($objectIdFields as $field) {
            if (isset($data[$field]) && $data[$field] instanceof ObjectId) {
                $data[$field] = (string) $data[$field];
            }
        }

        // 转换日期为ISO字符串
        $dateFields = ['created_at', 'started_at', 'completed_at', 'last_downloaded_at'];
        foreach ($dateFields as $field) {
            if (isset($data[$field]) && $data[$field] instanceof \MongoDB\BSON\UTCDateTime) {
                $data[$field] = $data[$field]->toDateTime()->format('c');
            }
        }

        return $data;
    }

    /**
     * 查找符合批量删除条件的记录
     */
    public function findRecordsForBatchDelete(array $conditions, array $sortOptions, int $limit): array
    {
        $cursor = $this->collection->find($conditions, [
            'sort' => $sortOptions,
            'limit' => $limit
        ]);

        return $cursor->toArray();
    }

    /**
     * 按日期统计记录数
     */
    public function countRecordsByDate(\DateTime $date): int
    {
        $startOfDay = new \MongoDB\BSON\UTCDateTime($date->getTimestamp() * 1000);
        $endOfDay = new \MongoDB\BSON\UTCDateTime(($date->getTimestamp() + 86400) * 1000);

        return $this->collection->countDocuments([
            'created_at' => [
                '$gte' => $startOfDay,
                '$lt' => $endOfDay
            ]
        ]);
    }

    /**
     * 获取最近的记录
     */
    public function getRecentRecords(int $limit = 20): array
    {
        $cursor = $this->collection->find([], [
            'sort' => ['created_at' => -1],
            'limit' => $limit
        ]);

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }
}
