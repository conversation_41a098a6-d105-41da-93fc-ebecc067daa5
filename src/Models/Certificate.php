<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class Certificate
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('certificates');
    }

    /**
     * 创建证书记录
     */
    public function create(array $data): ObjectId
    {
        $certificateData = [
            'name' => $data['name'],
            'type' => $data['type'], // 'p12', 'mobileprovision', 'csr'
            'file_path' => $data['file_path'],
            'file_size' => $data['file_size'],
            'uploaded_by' => new ObjectId($data['uploaded_by']),
            'status' => $data['status'] ?? 'active',
            'created_at' => new \MongoDB\BSON\UTCDateTime(),

            // 证书特定信息
            'bundle_id' => $data['bundle_id'] ?? null,
            'team_id' => $data['team_id'] ?? null,
            'team_name' => $data['team_name'] ?? null,
            'expires_at' => isset($data['expires_at']) ? new \MongoDB\BSON\UTCDateTime($data['expires_at'] * 1000) : null,

            // P12证书特定信息
            'certificate_name' => $data['certificate_name'] ?? null,
            'certificate_type' => $data['certificate_type'] ?? null, // 'development', 'distribution'
            'password' => $data['password'] ?? null, // 存储明文密码用于重签
            'password_hash' => isset($data['password']) ? password_hash($data['password'], PASSWORD_DEFAULT) : null,

            // mobileprovision特定信息
            'provisioning_profile_name' => $data['provisioning_profile_name'] ?? null,
            'provisioning_profile_uuid' => $data['provisioning_profile_uuid'] ?? null,
            'provisioned_devices' => $data['provisioned_devices'] ?? [],
            'entitlements' => $data['entitlements'] ?? [],

            // CSR特定信息
            'private_key_path' => $data['private_key_path'] ?? null,
            'common_name' => $data['common_name'] ?? null,
            'country' => $data['country'] ?? null,
            'organization' => $data['organization'] ?? null,
            'organizational_unit' => $data['organizational_unit'] ?? null,

            // 使用统计
            'usage_count' => 0,
            'last_used_at' => null
        ];

        $result = $this->collection->insertOne($certificateData);
        return $result->getInsertedId();
    }

    /**
     * 根据ID查找证书
     */
    public function findById(string $id): ?array
    {
        $certificate = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $certificate ? $this->documentToArray($certificate) : null;
    }

    /**
     * 根据类型查找证书
     */
    public function findByType(string $type, array $filters = []): array
    {
        // 处理ObjectId字段
        if (isset($filters['uploaded_by']) && is_string($filters['uploaded_by'])) {
            $filters['uploaded_by'] = new ObjectId($filters['uploaded_by']);
        }

        $query = array_merge(['type' => $type, 'status' => 'active'], $filters);
        $cursor = $this->collection->find($query, ['sort' => ['created_at' => -1]]);

        $certificates = [];
        foreach ($cursor as $certificate) {
            $certificates[] = $this->documentToArray($certificate);
        }

        return $certificates;
    }

    /**
     * 获取可用的证书对（p12 + mobileprovision）
     */
    public function getAvailableCertificatePairs(): array
    {
        // 获取所有活跃的p12证书
        $p12Certificates = $this->findByType('p12');
        $pairs = [];

        foreach ($p12Certificates as $p12) {
            // 查找匹配的mobileprovision文件
            $mobileProvisions = $this->collection->find([
                'type' => 'mobileprovision',
                'status' => 'active',
                'team_id' => $p12['team_id']
            ])->toArray();

            foreach ($mobileProvisions as $provision) {
                // 转换过期时间为时间戳
                $p12ExpiresAt = $this->convertToTimestamp($p12['expires_at'] ?? null);
                $provisionExpiresAt = $this->convertToTimestamp($provision['expires_at'] ?? null);

                $pairs[] = [
                    'id' => (string)$p12['_id'] . '_' . (string)$provision['_id'],
                    'p12' => $this->documentToArray($p12),
                    'mobileprovision' => $this->documentToArray($provision),
                    'bundle_id' => $provision['bundle_id'],
                    'team_name' => $p12['team_name'],
                    'expires_at' => min($p12ExpiresAt ?? PHP_INT_MAX, $provisionExpiresAt ?? PHP_INT_MAX)
                ];
            }
        }

        // 按过期时间排序，最晚过期的在前面
        usort($pairs, function ($a, $b) {
            return $b['expires_at'] <=> $a['expires_at'];
        });

        return $pairs;
    }

    /**
     * 更新证书使用统计
     */
    public function updateUsageStats(string $certificateId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($certificateId)],
            [
                '$inc' => ['usage_count' => 1],
                '$set' => ['last_used_at' => new \MongoDB\BSON\UTCDateTime()]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 更新证书状态
     */
    public function updateStatus(string $certificateId, string $status): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($certificateId)],
            ['$set' => ['status' => $status]]
        );
        return $result->getModifiedCount() > 0;
    }

    /**
     * 删除证书
     */
    public function delete(string $certificateId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($certificateId)]);
        return $result->getDeletedCount() > 0;
    }

    /**
     * 获取所有证书（管理员用）
     */
    public function getAllCertificates(int $limit = 50, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            [],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $certificates = [];
        foreach ($cursor as $certificate) {
            $certificates[] = $this->documentToArray($certificate);
        }

        return $certificates;
    }

    /**
     * 统计证书数量
     */
    public function countCertificates(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    /**
     * 统计记录数量（兼容测试脚本）
     */
    public function countRecords(array $filter = []): int
    {
        return $this->countCertificates($filter);
    }

    /**
     * 文档转数组
     */
    private function documentToArray($document): array
    {
        // 如果已经是数组，直接使用
        if (is_array($document)) {
            $data = $document;
        } else {
            // 如果是MongoDB文档对象，转换为数组
            $data = $document->getArrayCopy();
        }

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        if (isset($data['uploaded_by']) && $data['uploaded_by'] instanceof ObjectId) {
            $data['uploaded_by'] = (string) $data['uploaded_by'];
        }

        // 转换日期为时间戳
        foreach (['created_at', 'expires_at', 'last_used_at'] as $dateField) {
            if (isset($data[$dateField])) {
                $data[$dateField] = $this->convertToTimestamp($data[$dateField]);
            }
        }

        // 移除敏感信息（密码哈希，但保留明文密码用于重签）
        unset($data['password_hash']);

        return $data;
    }

    /**
     * 转换日期为时间戳
     */
    private function convertToTimestamp($date): ?int
    {
        if ($date === null) {
            return null;
        }

        if ($date instanceof \MongoDB\BSON\UTCDateTime) {
            return (int)($date->toDateTime()->getTimestamp());
        }

        if (is_int($date)) {
            return $date;
        }

        if (is_string($date)) {
            return strtotime($date);
        }

        return null;
    }
}
