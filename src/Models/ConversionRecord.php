<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\BSON\UTCDateTime;
use MongoDB\Collection;

class ConversionRecord
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('conversion_records');
    }

    /**
     * 创建转换记录
     */
    public function createRecord(array $data): string
    {
        $document = [
            'user_id' => new ObjectId($data['user_id']),
            'conversion_type' => $data['conversion_type'], // 'cer_to_p12'
            'source_file_name' => $data['source_file_name'],
            'source_file_type' => $data['source_file_type'], // 'cer'
            'target_file_name' => $data['target_file_name'],
            'target_file_type' => $data['target_file_type'], // 'p12'
            'target_file_path' => $data['target_file_path'],
            'certificate_id' => isset($data['certificate_id']) ? new ObjectId($data['certificate_id']) : null,
            'source_csr_id' => isset($data['source_csr_id']) ? new ObjectId($data['source_csr_id']) : null,
            'source_csr_name' => $data['source_csr_name'] ?? null,
            'conversion_method' => $data['conversion_method'], // 'auto' or 'manual'
            'certificate_name' => $data['certificate_name'],
            'certificate_type' => $data['certificate_type'], // 'development' or 'distribution'
            'file_size' => $data['file_size'] ?? 0,
            'status' => $data['status'] ?? 'completed', // 'completed', 'failed'
            'error_message' => $data['error_message'] ?? null,
            'ip_address' => $data['ip_address'] ?? null,
            'user_agent' => $data['user_agent'] ?? null,
            'created_at' => new UTCDateTime(),
            'updated_at' => new UTCDateTime()
        ];

        $result = $this->collection->insertOne($document);
        return (string)$result->getInsertedId();
    }

    /**
     * 获取用户的转换记录
     */
    public function getUserRecords(string $userId, int $limit = 50, int $offset = 0): array
    {
        $cursor = $this->collection->find(
            ['user_id' => new ObjectId($userId)],
            [
                'sort' => ['created_at' => -1],
                'limit' => $limit,
                'skip' => $offset
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 获取转换统计信息
     */
    public function getConversionStats(string $userId): array
    {
        $pipeline = [
            ['$match' => ['user_id' => new ObjectId($userId)]],
            ['$group' => [
                '_id' => '$conversion_type',
                'count' => ['$sum' => 1],
                'success_count' => [
                    '$sum' => ['$cond' => [['$eq' => ['$status', 'completed']], 1, 0]]
                ],
                'total_size' => ['$sum' => '$file_size']
            ]]
        ];

        $cursor = $this->collection->aggregate($pipeline);
        $stats = [];

        foreach ($cursor as $stat) {
            $stats[] = [
                'conversion_type' => $stat['_id'],
                'total_count' => $stat['count'],
                'success_count' => $stat['success_count'],
                'success_rate' => $stat['count'] > 0 ? round($stat['success_count'] / $stat['count'] * 100, 2) : 0,
                'total_size' => $stat['total_size']
            ];
        }

        return $stats;
    }

    /**
     * 更新转换记录状态
     */
    public function updateStatus(string $recordId, string $status, string $errorMessage = null): bool
    {
        $updateData = [
            'status' => $status,
            'updated_at' => new UTCDateTime()
        ];

        if ($errorMessage) {
            $updateData['error_message'] = $errorMessage;
        }

        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($recordId)],
            ['$set' => $updateData]
        );

        return $result->getModifiedCount() > 0;
    }

    /**
     * 删除转换记录
     */
    public function deleteRecord(string $recordId, string $userId): bool
    {
        $result = $this->collection->deleteOne([
            '_id' => new ObjectId($recordId),
            'user_id' => new ObjectId($userId)
        ]);

        return $result->getDeletedCount() > 0;
    }

    /**
     * 获取最近的转换记录
     */
    public function getRecentRecords(string $userId, int $limit = 10): array
    {
        return $this->getUserRecords($userId, $limit, 0);
    }

    /**
     * 清理过期记录（可选功能）
     */
    public function cleanupOldRecords(int $daysOld = 90): int
    {
        $cutoffDate = new UTCDateTime((time() - $daysOld * 24 * 3600) * 1000);

        $result = $this->collection->deleteMany([
            'created_at' => ['$lt' => $cutoffDate]
        ]);

        return $result->getDeletedCount();
    }

    /**
     * 搜索转换记录
     */
    public function searchRecords(string $userId, array $filters = []): array
    {
        $query = ['user_id' => new ObjectId($userId)];

        // 添加搜索条件
        if (!empty($filters['conversion_type'])) {
            $query['conversion_type'] = $filters['conversion_type'];
        }

        if (!empty($filters['status'])) {
            $query['status'] = $filters['status'];
        }

        if (!empty($filters['certificate_type'])) {
            $query['certificate_type'] = $filters['certificate_type'];
        }

        if (!empty($filters['date_from'])) {
            $query['created_at']['$gte'] = new UTCDateTime(strtotime($filters['date_from']) * 1000);
        }

        if (!empty($filters['date_to'])) {
            $query['created_at']['$lte'] = new UTCDateTime(strtotime($filters['date_to']) * 1000);
        }

        if (!empty($filters['search_term'])) {
            $searchTerm = $filters['search_term'];
            $query['$or'] = [
                ['source_file_name' => ['$regex' => $searchTerm, '$options' => 'i']],
                ['target_file_name' => ['$regex' => $searchTerm, '$options' => 'i']],
                ['certificate_name' => ['$regex' => $searchTerm, '$options' => 'i']],
                ['source_csr_name' => ['$regex' => $searchTerm, '$options' => 'i']]
            ];
        }

        $cursor = $this->collection->find(
            $query,
            [
                'sort' => ['created_at' => -1],
                'limit' => $filters['limit'] ?? 50,
                'skip' => $filters['offset'] ?? 0
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        return $records;
    }

    /**
     * 文档转数组
     */
    private function documentToArray($document): array
    {
        // 如果已经是数组，直接使用
        if (is_array($document)) {
            $data = $document;
        } else {
            // 如果是MongoDB文档对象，转换为数组
            $data = $document->getArrayCopy();
        }

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        if (isset($data['user_id']) && $data['user_id'] instanceof ObjectId) {
            $data['user_id'] = (string) $data['user_id'];
        }

        if (isset($data['certificate_id']) && $data['certificate_id'] instanceof ObjectId) {
            $data['certificate_id'] = (string) $data['certificate_id'];
        }

        if (isset($data['source_csr_id']) && $data['source_csr_id'] instanceof ObjectId) {
            $data['source_csr_id'] = (string) $data['source_csr_id'];
        }

        // 转换日期为时间戳
        foreach (['created_at', 'updated_at'] as $dateField) {
            if (isset($data[$dateField])) {
                $data[$dateField] = $this->convertToTimestamp($data[$dateField]);
            }
        }

        return $data;
    }

    /**
     * 转换日期为时间戳
     */
    private function convertToTimestamp($date): ?int
    {
        if ($date === null) {
            return null;
        }

        if ($date instanceof \MongoDB\BSON\UTCDateTime) {
            return (int)($date->toDateTime()->getTimestamp());
        }

        if (is_int($date)) {
            return $date;
        }

        if (is_string($date)) {
            return strtotime($date);
        }

        return null;
    }

    /**
     * 获取所有记录（支持分页）
     */
    public function getAllRecordsWithUserPaginated(int $page = 1, int $pageSize = 10): array
    {
        $userCollection = $this->db->getCollection('users');

        // 计算偏移量
        $skip = ($page - 1) * $pageSize;

        // 获取总记录数
        $totalCount = $this->collection->countDocuments([]);

        // 获取当前页记录
        $records = iterator_to_array($this->collection->find(
            [],
            [
                'sort' => ['created_at' => -1],
                'skip' => $skip,
                'limit' => $pageSize
            ]
        ));

        // 关联用户名
        foreach ($records as &$rec) {
            if (isset($rec['user_id'])) {
                $user = $userCollection->findOne(['_id' => $rec['user_id']]);
                $rec['username'] = $user['username'] ?? '';
            }
        }

        // 计算分页信息
        $totalPages = ceil($totalCount / $pageSize);

        return [
            'records' => array_map([$this, 'documentToArray'], $records),
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null,
            ]
        ];
    }

    /**
     * 根据用户ID获取记录（支持分页）
     */
    public function findByUserIdPaginated(string $userId, int $page = 1, int $pageSize = 10): array
    {
        // 计算偏移量
        $skip = ($page - 1) * $pageSize;

        // 获取总记录数
        $totalCount = $this->collection->countDocuments(['user_id' => new ObjectId($userId)]);

        // 获取当前页记录
        $cursor = $this->collection->find(
            ['user_id' => new ObjectId($userId)],
            [
                'limit' => $pageSize,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $records = [];
        foreach ($cursor as $record) {
            $records[] = $this->documentToArray($record);
        }

        // 计算分页信息
        $totalPages = ceil($totalCount / $pageSize);

        return [
            'records' => $records,
            'pagination' => [
                'current_page' => $page,
                'page_size' => $pageSize,
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'has_prev' => $page > 1,
                'has_next' => $page < $totalPages,
                'prev_page' => $page > 1 ? $page - 1 : null,
                'next_page' => $page < $totalPages ? $page + 1 : null,
            ]
        ];
    }
}
