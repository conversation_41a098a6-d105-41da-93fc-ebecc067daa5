<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="XIOS - 专业的iOS应用管理工具，支持IPA上传、重签、证书管理等功能">
    <meta name="keywords" content="iOS, IPA, 重签, 证书, TestFlight, 应用管理">
    <title>XIOS - iOS应用管理工具</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/static/favicon.svg">
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="assets/static/favicon-16.svg">
    <link rel="apple-touch-icon" href="assets/static/favicon.svg">
    <link rel="shortcut icon" href="assets/static/favicon.svg">
    <!-- 兜底方案：如果SVG不支持，使用PNG -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/static/favicon.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/static/favicon-16.svg">

    <!-- CSS 样式文件 -->
    <link rel="stylesheet" href="assets/css/base/variables.css">
    <link rel="stylesheet" href="assets/css/base/reset.css">
    <link rel="stylesheet" href="assets/css/base/typography.css">
    <link rel="stylesheet" href="assets/css/components/button.css">
    <link rel="stylesheet" href="assets/css/components/card.css">
    <link rel="stylesheet" href="assets/css/components/form.css">
    <link rel="stylesheet" href="assets/css/components/navbar.css">
    <link rel="stylesheet" href="assets/css/components/modal.css">
    <link rel="stylesheet" href="assets/css/components/toast.css">
    <link rel="stylesheet" href="assets/css/layout/main.css">
    <link rel="stylesheet" href="assets/css/theme.css">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="assets/js/core/api.js" as="script" crossorigin="anonymous">
    <link rel="preload" href="assets/js/core/auth.js" as="script" crossorigin="anonymous">
    <link rel="preload" href="assets/js/core/router.js" as="script" crossorigin="anonymous">
</head>

<body class="page">
    <!-- 跳过链接（无障碍） -->
    <a href="#main-content" class="skip-link">跳转到主要内容</a>

    <!-- 页面头部 -->
    <header class="page-header">
        <!-- 主导航栏 -->
        <nav class="navbar" role="navigation" aria-label="主导航">
            <div class="navbar-container">
                <!-- 品牌标识 -->
                <a href="#/" class="navbar-brand" data-nav-link="/">
                    <img src="assets/static/logo.svg" alt="XIOS" class="navbar-brand-logo">
                    <span class="navbar-brand-text">XIOS</span>
                </a>

                <!-- 桌面端导航菜单 -->
                <ul class="navbar-nav" role="menubar">
                    <li class="navbar-nav-item" role="none">
                        <a href="#/upload" class="navbar-nav-link" data-nav-link="/upload" role="menuitem">
                            <span class="navbar-nav-icon">📤</span>
                            <span>上传中心</span>
                        </a>
                    </li>
                    <li class="navbar-nav-item" role="none">
                        <a href="#/resign" class="navbar-nav-link" data-nav-link="/resign" role="menuitem">
                            <span class="navbar-nav-icon">🔐</span>
                            <span>重签工具</span>
                        </a>
                    </li>
                    <li class="navbar-nav-item" role="none">
                        <a href="#/certificate" class="navbar-nav-link" data-nav-link="/certificate" role="menuitem">
                            <span class="navbar-nav-icon">📋</span>
                            <span>证书管理</span>
                        </a>
                    </li>
                    <li class="navbar-nav-item" role="none" id="admin-nav-item" style="display: none;">
                        <a href="#/admin" class="navbar-nav-link" data-nav-link="/admin" role="menuitem">
                            <span class="navbar-nav-icon">⚙️</span>
                            <span>管理后台</span>
                        </a>
                    </li>
                </ul>

                <!-- 用户信息 -->
                <div class="navbar-user">
                    <button class="navbar-user-profile" id="user-profile-btn" title="个人信息">
                        <div class="navbar-user-info">
                            <div class="navbar-user-name" id="user-name">加载中...</div>
                            <div class="navbar-user-role" id="user-role"></div>
                        </div>
                        <div class="navbar-user-avatar" id="user-avatar">U</div>
                    </button>
                </div>

                <!-- 移动端菜单切换按钮 -->
                <button class="navbar-toggle" id="navbar-toggle" aria-label="切换导航菜单">
                    <span class="navbar-toggle-line"></span>
                    <span class="navbar-toggle-line"></span>
                    <span class="navbar-toggle-line"></span>
                </button>
            </div>

            <!-- 移动端导航菜单 -->
            <div class="navbar-mobile" id="navbar-mobile">
                <ul class="navbar-mobile-nav" role="menubar">
                    <li role="none">
                        <a href="#/upload" class="navbar-nav-link" data-nav-link="/upload" role="menuitem">
                            <span class="navbar-nav-icon">📤</span>
                            <span>上传中心</span>
                        </a>
                    </li>
                    <li role="none">
                        <a href="#/resign" class="navbar-nav-link" data-nav-link="/resign" role="menuitem">
                            <span class="navbar-nav-icon">🔐</span>
                            <span>重签工具</span>
                        </a>
                    </li>
                    <li role="none">
                        <a href="#/certificate" class="navbar-nav-link" data-nav-link="/certificate" role="menuitem">
                            <span class="navbar-nav-icon">📋</span>
                            <span>证书管理</span>
                        </a>
                    </li>
                    <li role="none" id="admin-mobile-nav-item" style="display: none;">
                        <a href="#/admin" class="navbar-nav-link" data-nav-link="/admin" role="menuitem">
                            <span class="navbar-nav-icon">⚙️</span>
                            <span>管理后台</span>
                        </a>
                    </li>
                </ul>

                <div class="navbar-mobile-user">
                    <div class="navbar-user-avatar" id="mobile-user-avatar">U</div>
                    <div class="navbar-user-info">
                        <div class="navbar-user-name" id="mobile-user-name">加载中...</div>
                        <div class="navbar-user-role" id="mobile-user-role"></div>
                    </div>
                    <button class="btn btn-logout btn-sm" id="mobile-logout-btn" title="退出登录">
                        <span class="logout-icon">🚪</span>
                        <span>退出登录</span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- 面包屑导航 -->
        <nav class="navbar-breadcrumb" aria-label="面包屑导航">
            <div class="navbar-breadcrumb-container">
                <ol class="navbar-breadcrumb-nav" role="breadcrumb">
                    <!-- 面包屑项目将由路由器动态生成 -->
                </ol>
            </div>
        </nav>
    </header>

    <!-- 主要内容区域 -->
    <main class="page-main" id="main-content" role="main">
        <!-- 动态背景 -->
        <div class="main-background"></div>
        <!-- 上传中心页面 -->
        <section id="upload-page" class="section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <h1 class="section-title">📤 上传中心</h1>
                    <p class="section-subtitle">管理您的TestFlight应用上传</p>
                </div>

                <!-- 上传记录列表 -->
                <div class="card">
                    <div class="card-header card-header-flex">
                        <div>
                            <h2 class="card-header-title">📋 上传记录</h2>
                            <p class="card-header-subtitle">查看和管理您的上传历史</p>
                        </div>
                        <div class="card-header-actions">
                            <button class="btn btn-primary" id="add-upload-task-btn">
                                <span>➕</span>
                                <span>添加上传任务</span>
                            </button>
                            <button class="btn btn-accent" id="refresh-records-btn">
                                <span>🔄</span>
                                <span>刷新</span>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 筛选工具栏 -->
                        <div class="toolbar mb-6">
                            <div class="toolbar-left">
                                <select id="status-filter" class="form-control">
                                    <option value="">全部状态</option>
                                    <option value="pending">等待中</option>
                                    <option value="processing">处理中</option>
                                    <option value="completed">已完成</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                        </div>

                        <div id="upload-records-container">
                            <!-- 上传记录将由JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 重签工具页面 -->
        <section id="resign-page" class="section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <h1 class="section-title">🔐 重签工具</h1>
                    <p class="section-subtitle">重新签名您的IPA文件，自动生成安装页面和二维码</p>
                </div>

                <div class="grid grid-cols-1 gap-8">
                    <!-- 重签记录卡片 -->
                    <div class="card">
                        <div class="card-header card-header-flex">
                            <div>
                                <h2 class="card-header-title">重签记录</h2>
                                <p class="card-header-subtitle">查看和管理您的重签历史</p>
                            </div>
                            <div class="card-header-actions">
                                <button class="btn btn-purple btn-sm" id="add-resign-task-btn">
                                    <span>➕</span>
                                    <span>添加重签任务</span>
                                </button>
                                <button class="btn btn-outline" id="refresh-resign-records-btn">
                                    <span>🔄</span>
                                    <span>刷新</span>
                                </button>
                            </div>
                        </div>
                        <div class="card-body" id="resign-records-container">
                            <!-- 重签记录将由JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 证书管理页面 -->
        <section id="certificate-page" class="section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <h1 class="section-title">📋 证书管理</h1>
                    <p class="section-subtitle">生成CSR文件和转换证书格式</p>
                </div>

                <div class="grid grid-cols-1 gap-8">
                    <!-- 证书记录卡片 -->
                    <div class="card">
                        <div class="card-header card-header-flex">
                            <div>
                                <h2 class="card-header-title">证书记录</h2>
                                <p class="card-header-subtitle">查看和管理您的证书操作历史</p>
                            </div>
                            <div class="card-header-actions">
                                <button class="btn btn-accent btn-sm" id="generate-csr-btn">
                                    <span>📋</span>
                                    <span>CSR生成</span>
                                </button>
                                <button class="btn btn-pink btn-sm" id="convert-cer-btn">
                                    <span>🔄</span>
                                    <span>CER转P12</span>
                                </button>
                                <button class="btn btn-secondary btn-sm" id="conversion-guide-btn">
                                    <span>📖</span>
                                    <span>转换指南</span>
                                </button>
                                <button class="btn btn-outline" id="refresh-cert-records-btn">
                                    <span>🔄</span>
                                    <span>刷新</span>
                                </button>
                            </div>
                        </div>
                        <div class="card-body" id="cert-records-container">
                            <!-- 证书记录将由JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 管理后台页面 -->
        <section id="admin-page" class="section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <h1 class="section-title">⚙️ 管理后台</h1>
                    <p class="section-subtitle">系统管理和用户管理功能</p>
                </div>

                <div id="admin-content-container">
                    <!-- 管理后台内容将由JavaScript动态生成 -->
                </div>
            </div>
        </section>

        <!-- 404页面 -->
        <section id="404-page" class="section" style="display: none;">
            <div class="container">
                <div class="section-header">
                    <h1 class="section-title">😵 页面未找到</h1>
                    <p class="section-subtitle">抱歉，您访问的页面不存在</p>
                    <div class="mt-8">
                        <a href="#/" class="btn btn-primary">返回首页</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 模态框容器 -->
    <div id="modal-container"></div>

    <!-- 加载指示器 -->
    <div id="loading-indicator"
        style="position: fixed; top: 1rem; right: 1rem; background: var(--gradient-primary); color: var(--text-inverse); padding: 0.75rem 1rem; border-radius: var(--radius-xl); box-shadow: var(--shadow-glow); backdrop-filter: blur(10px); display: none; z-index: 9999; align-items: center; gap: 0.5rem;">
        <span>✨</span>
        <span>加载中...</span>
    </div>

    <!-- IPA 解析器 (必须在模块之前加载) -->
    <script src="assets/js/core/app-info-parser.js"></script>

    <!-- JavaScript 模块 -->
    <script type="module" src="assets/js/app.js"></script>
</body>

</html>