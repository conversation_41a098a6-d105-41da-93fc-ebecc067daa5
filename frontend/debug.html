<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XIOS - 调试页面</title>

    <!-- CSS 样式文件 -->
    <link rel="stylesheet" href="assets/css/base/variables.css">
    <link rel="stylesheet" href="assets/css/base/reset.css">
    <link rel="stylesheet" href="assets/css/base/typography.css">
    <link rel="stylesheet" href="assets/css/components/button.css">
    <link rel="stylesheet" href="assets/css/components/card.css">
    <link rel="stylesheet" href="assets/css/components/form.css">
    <link rel="stylesheet" href="assets/css/layout/main.css">
    <link rel="stylesheet" href="assets/css/theme.css">

    <style>
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
        }

        .debug-success {
            border-color: #28a745;
            background: #d4edda;
        }

        .debug-error {
            border-color: #dc3545;
            background: #f8d7da;
        }

        .debug-warning {
            border-color: #ffc107;
            background: #fff3cd;
        }
    </style>
</head>

<body>
    <div class="container py-8">
        <h1 class="text-center mb-8">🔧 XIOS 调试页面</h1>

        <div class="grid grid-cols-1 gap-6">
            <!-- 组件测试 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-header-title">组件功能测试</h2>
                </div>
                <div class="card-body">
                    <div id="component-test-results"></div>
                    <button class="btn btn-rainbow" onclick="testComponents()">✨ 测试组件</button>
                </div>
            </div>

            <!-- 表单测试 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-header-title">表单组件测试</h2>
                </div>
                <div class="card-body">
                    <div id="form-test-container"></div>
                    <div id="form-test-results"></div>
                    <button class="btn btn-accent" onclick="testFormComponents()">🃏 测试表单组件</button>
                </div>
            </div>

            <!-- API测试 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-header-title">API功能测试</h2>
                </div>
                <div class="card-body">
                    <div id="api-test-results"></div>
                    <button class="btn btn-purple" onclick="testAPI()">🔗 测试API</button>
                </div>
            </div>

            <!-- 路由测试 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-header-title">路由功能测试</h2>
                </div>
                <div class="card-body">
                    <div id="router-test-results"></div>
                    <button class="btn btn-pink" onclick="testRouter()">🧭 测试路由</button>
                </div>
            </div>
        </div>

        <div class="text-center mt-8">
            <a href="index.html" class="btn btn-primary btn-lg">
                <span>🚀</span>
                <span>进入主应用</span>
            </a>
        </div>
    </div>

    <script type="module" src="assets/js/pages/debug.js"></script>
</body>

</html>