<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退出登录功能测试 - XIOS</title>
    
    <!-- CSS 样式文件 -->
    <link rel="stylesheet" href="assets/css/base/variables.css">
    <link rel="stylesheet" href="assets/css/base/reset.css">
    <link rel="stylesheet" href="assets/css/base/typography.css">
    <link rel="stylesheet" href="assets/css/components/button.css">
    <link rel="stylesheet" href="assets/css/components/card.css">
    <link rel="stylesheet" href="assets/css/components/modal.css">
    <link rel="stylesheet" href="assets/css/components/toast.css">
    <link rel="stylesheet" href="assets/css/layout/main.css">
    <link rel="stylesheet" href="assets/css/theme.css">
    
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-primary);
        }
        
        .button-showcase {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
            margin: 1rem 0;
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: var(--radius-lg);
            font-family: var(--font-family-mono);
            font-size: var(--text-sm);
        }
        
        .test-result.success {
            background: var(--success-50);
            color: var(--success-800);
            border: 1px solid var(--success-200);
        }
        
        .test-result.cancelled {
            background: var(--warning-50);
            color: var(--warning-800);
            border: 1px solid var(--warning-200);
        }
        
        .navbar-demo {
            background: var(--bg-secondary);
            padding: 1rem;
            border-radius: var(--radius-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 1rem 0;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary-500);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: var(--font-bold);
        }
    </style>
</head>
<body class="page">
    <div class="container">
        <header class="page-header">
            <h1>🚪 退出登录功能测试</h1>
            <p>测试退出登录按钮的样式和确认对话框功能</p>
        </header>

        <main class="main-content">
            <!-- 按钮样式展示 -->
            <section class="test-section">
                <h2>🎨 退出登录按钮样式展示</h2>
                <p>展示不同状态下的退出登录按钮样式</p>
                
                <div class="button-showcase">
                    <button class="btn btn-logout btn-sm">
                        <span class="logout-icon">🚪</span>
                        <span>退出</span>
                    </button>
                    
                    <button class="btn btn-logout btn-md">
                        <span class="logout-icon">🚪</span>
                        <span>退出登录</span>
                    </button>
                    
                    <button class="btn btn-logout btn-lg">
                        <span class="logout-icon">🚪</span>
                        <span>退出登录</span>
                    </button>
                </div>
                
                <p><small>💡 悬停查看动画效果，注意红色边框和背景变化</small></p>
            </section>

            <!-- 导航栏模拟 -->
            <section class="test-section">
                <h2>🧭 导航栏中的退出按钮</h2>
                <p>模拟实际导航栏中的退出登录按钮</p>
                
                <div class="navbar-demo">
                    <div class="user-info">
                        <div class="user-avatar">U</div>
                        <div>
                            <div style="font-weight: var(--font-semibold);">测试用户</div>
                            <div style="font-size: var(--text-xs); color: var(--text-secondary);">管理员</div>
                        </div>
                    </div>
                    
                    <button class="btn btn-logout btn-sm" onclick="testLogout()">
                        <span class="logout-icon">🚪</span>
                        <span>退出</span>
                    </button>
                </div>
                
                <div class="navbar-demo">
                    <div class="user-info">
                        <div class="user-avatar">M</div>
                        <div>
                            <div style="font-weight: var(--font-semibold);">移动端用户</div>
                            <div style="font-size: var(--text-xs); color: var(--text-secondary);">普通用户</div>
                        </div>
                    </div>
                    
                    <button class="btn btn-logout btn-sm" onclick="testLogout()">
                        <span class="logout-icon">🚪</span>
                        <span>退出登录</span>
                    </button>
                </div>
            </section>

            <!-- 功能测试 -->
            <section class="test-section">
                <h2>🧪 退出登录功能测试</h2>
                <p>测试退出登录的确认对话框和流程</p>
                
                <div class="button-showcase">
                    <button class="btn btn-logout btn-md" onclick="testLogout()">
                        <span class="logout-icon">🚪</span>
                        <span>测试退出登录</span>
                    </button>
                    
                    <button class="btn btn-secondary btn-md" onclick="testLogoutWithoutConfirm()">
                        <span>🔍</span>
                        <span>测试直接退出（错误示例）</span>
                    </button>
                </div>
                
                <div id="test-results">
                    <p>点击上方按钮开始测试...</p>
                </div>
            </section>

            <!-- 测试说明 -->
            <section class="test-section">
                <h2>✅ 测试要点</h2>
                
                <h3>🎨 视觉效果测试：</h3>
                <ul>
                    <li><strong>按钮颜色</strong>：应该使用红色边框和文字</li>
                    <li><strong>悬停效果</strong>：鼠标悬停时背景变为浅红色</li>
                    <li><strong>图标动画</strong>：悬停时门图标向右移动</li>
                    <li><strong>文字清晰</strong>：明确显示"退出"或"退出登录"</li>
                </ul>
                
                <h3>🔧 功能测试：</h3>
                <ul>
                    <li><strong>确认对话框</strong>：点击退出按钮应该弹出确认对话框</li>
                    <li><strong>等待确认</strong>：必须等待用户点击确认才能继续</li>
                    <li><strong>取消功能</strong>：点击取消应该关闭对话框，不执行退出</li>
                    <li><strong>键盘支持</strong>：ESC键应该能关闭对话框</li>
                </ul>
                
                <h3>📱 响应式测试：</h3>
                <ul>
                    <li><strong>移动端适配</strong>：在小屏幕上按钮大小合适</li>
                    <li><strong>触摸友好</strong>：按钮大小适合触摸操作</li>
                    <li><strong>文字显示</strong>：在不同屏幕尺寸下文字清晰可读</li>
                </ul>
            </section>
        </main>
    </div>

    <!-- JavaScript 测试模块 -->
    <script type="module">
        import notifications from './assets/js/core/notifications.js';
        
        // 测试结果显示
        function showTestResult(action, confirmed) {
            const resultsContainer = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${confirmed ? 'success' : 'cancelled'}`;
            resultDiv.innerHTML = `
                <strong>[${timestamp}] ${action}</strong><br>
                结果: ${confirmed ? '✅ 用户确认退出登录' : '❌ 用户取消退出'}
            `;
            
            resultsContainer.appendChild(resultDiv);
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        }
        
        // 测试正确的退出登录流程
        window.testLogout = async () => {
            console.log('开始测试退出登录流程');
            
            try {
                const confirmed = await notifications.confirm('确定要退出登录吗？', {
                    title: '退出确认',
                    confirmText: '退出登录',
                    cancelText: '取消',
                    type: 'warning'
                });
                
                showTestResult('退出登录测试', confirmed);
                
                if (confirmed) {
                    // 模拟退出登录过程
                    const loadingId = notifications.loading('正在退出...');
                    
                    setTimeout(() => {
                        notifications.hide(loadingId);
                        notifications.success('退出登录成功');
                    }, 1500);
                }
            } catch (error) {
                console.error('退出登录测试失败:', error);
                notifications.error('测试过程中发生错误：' + error.message);
            }
        };
        
        // 测试错误的退出登录流程（不等待确认）
        window.testLogoutWithoutConfirm = () => {
            console.log('开始测试错误的退出登录流程');
            
            // 错误示例：不使用 await
            notifications.confirm('确定要退出登录吗？', {
                title: '退出确认',
                confirmText: '退出登录',
                cancelText: '取消',
                type: 'warning'
            });
            
            // 这里会立即执行，不等待用户确认
            showTestResult('错误的退出登录测试（立即执行）', true);
            notifications.warning('这是错误的实现方式！没有等待用户确认就执行了退出逻辑');
        };
        
        console.log('🚪 退出登录测试页面已加载');
        
        // 显示初始提示
        setTimeout(() => {
            notifications.info('退出登录测试页面已准备就绪，请测试按钮样式和功能', {
                duration: 3000
            });
        }, 1000);
    </script>
</body>
</html>
