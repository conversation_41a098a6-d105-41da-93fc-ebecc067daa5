<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="XIOS - 账号激活页面">
    <title>账号激活 - XIOS</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/static/favicon.svg">
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="assets/static/favicon-16.svg">
    <link rel="apple-touch-icon" href="assets/static/favicon.svg">
    <link rel="shortcut icon" href="assets/static/favicon.svg">

    <!-- CSS 样式文件 -->
    <link rel="stylesheet" href="assets/css/base/variables.css">
    <link rel="stylesheet" href="assets/css/base/reset.css">
    <link rel="stylesheet" href="assets/css/base/typography.css">
    <link rel="stylesheet" href="assets/css/components/button.css">
    <link rel="stylesheet" href="assets/css/components/card.css">
    <link rel="stylesheet" href="assets/css/components/form.css">
    <link rel="stylesheet" href="assets/css/layout/main.css">
    <link rel="stylesheet" href="assets/css/theme.css">

    <style>
        body {
            background: var(--gradient-hero);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-4);
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translate(0, 0) rotate(0deg);
            }

            33% {
                transform: translate(30px, -30px) rotate(120deg);
            }

            66% {
                transform: translate(-20px, 20px) rotate(240deg);
            }
        }

        .activate-container {
            width: 100%;
            max-width: 500px;
        }

        .activate-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .activate-logo {
            margin-bottom: var(--space-4);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .activate-logo img {
            width: 64px;
            height: 64px;
            object-fit: contain;
            transition: transform var(--transition-base);
        }

        .activate-logo img:hover {
            transform: scale(1.05);
        }

        /* 响应式优化 */
        @media (max-width: 480px) {
            .activate-logo img {
                width: 48px;
                height: 48px;
            }
        }

        .activate-title {
            color: var(--text-inverse);
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-2);
        }

        .activate-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: var(--text-base);
        }

        .activate-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl), 0 0 40px rgba(99, 102, 241, 0.1);
            padding: var(--space-8);
            position: relative;
            z-index: 1;
        }

        .activate-form {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }

        .activate-footer {
            text-align: center;
            margin-top: var(--space-8);
        }

        .activate-footer-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: var(--text-sm);
        }

        .status-message {
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-4);
            text-align: center;
        }

        .status-success {
            background: var(--color-success-light);
            color: var(--color-success);
            border: 1px solid var(--color-success-200);
        }

        .status-error {
            background: var(--color-error-light);
            color: var(--color-error);
            border: 1px solid var(--color-error-200);
        }

        .status-warning {
            background: var(--color-warning-light);
            color: var(--color-warning);
            border: 1px solid var(--color-warning-200);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: var(--bg-primary);
            padding: var(--space-6);
            border-radius: var(--radius-xl);
            text-align: center;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .spinner {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid var(--border-primary);
            border-top-color: var(--primary-500);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: var(--space-2);
        }
    </style>
</head>

<body>
    <div class="activate-container">
        <!-- 激活头部 -->
        <div class="activate-header">
            <div class="activate-logo">
                <img src="assets/static/logo.svg" alt="XIOS Logo">
            </div>
            <h1 class="activate-title">账号激活</h1>
            <p class="activate-subtitle">激活您的XIOS账号以开始使用</p>
        </div>

        <!-- 激活卡片 -->
        <div class="activate-card">
            <!-- 状态消息 -->
            <div id="status-message" style="display: none;"></div>

            <!-- 激活表单 -->
            <form class="activate-form" id="activate-form">
                <div class="form-group">
                    <label class="form-label form-label-required" for="activation-code">激活码</label>
                    <input type="text" id="activation-code" name="activation_code" class="form-control"
                        placeholder="请输入激活码" required autocomplete="off">
                    <div class="form-help">请输入管理员提供的激活码</div>
                </div>

                <div class="form-group">
                    <label class="form-label form-label-required" for="username">用户名</label>
                    <input type="text" id="username" name="username" class="form-control" placeholder="请输入用户名" required
                        autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label form-label-required" for="password">密码</label>
                    <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码"
                        required autocomplete="new-password">
                </div>

                <div class="form-group">
                    <label class="form-label form-label-required" for="confirm-password">确认密码</label>
                    <input type="password" id="confirm-password" name="confirm_password" class="form-control"
                        placeholder="请再次输入密码" required autocomplete="new-password">
                </div>

                <div class="form-group">
                    <label class="form-label" for="email">邮箱（可选）</label>
                    <input type="email" id="email" name="email" class="form-control" placeholder="请输入邮箱地址"
                        autocomplete="email">
                </div>

                <button type="submit" class="btn btn-primary btn-lg btn-block" id="activate-btn">
                    <span>🚀</span>
                    <span>激活账号</span>
                </button>
            </form>
        </div>

        <!-- 激活底部 -->
        <div class="activate-footer">
            <p class="activate-footer-text">
                已有账号？ <a href="login.html" style="color: rgba(255, 255, 255, 0.9); text-decoration: underline;">立即登录</a>
            </p>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <span>激活中...</span>
        </div>
    </div>

    <script type="module" src="assets/js/pages/activate.js"></script>
</body>

</html>