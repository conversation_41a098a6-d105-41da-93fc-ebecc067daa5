# XIOS 前端项目

这是一个全新重构的 XIOS 前端项目，采用现代化的架构设计，提供更好的用户体验和开发体验。

## 🎯 项目特点

### ✨ 现代化设计
- **组件化架构**: HTML、CSS、JS 完全分离，模块化开发
- **设计系统**: 统一的 CSS 变量和组件库
- **响应式布局**: 移动优先的设计理念
- **无障碍支持**: 符合 WCAG 标准的可访问性设计

### 🛠️ 技术栈
- **纯 HTML/CSS/JS**: 无框架依赖，轻量级
- **ES6+ 模块化**: 现代 JavaScript 特性
- **CSS Grid + Flexbox**: 现代化布局方案
- **CSS 自定义属性**: 主题系统支持

### 🏗️ 架构优势
- **统一的 API 管理**: 集中处理请求、认证、错误
- **路由系统**: 单页应用的路由管理
- **组件库**: 可复用的 UI 组件
- **状态管理**: 认证状态和用户信息管理

## 📁 项目结构

```
frontend/
├── index.html                 # 主入口页面
├── login.html                 # 登录页面
├── assets/                    # 静态资源
│   ├── css/                   # 样式文件
│   │   ├── base/              # 基础样式
│   │   │   ├── reset.css      # 样式重置
│   │   │   ├── variables.css  # CSS变量
│   │   │   └── typography.css # 字体样式
│   │   ├── components/        # 组件样式
│   │   │   ├── button.css     # 按钮组件
│   │   │   ├── card.css       # 卡片组件
│   │   │   ├── form.css       # 表单组件
│   │   │   ├── modal.css      # 模态框组件
│   │   │   └── navbar.css     # 导航栏组件
│   │   └── layout/            # 布局样式
│   │       └── main.css       # 主布局
│   └── js/                    # JavaScript文件
│       ├── core/              # 核心功能
│       │   ├── api.js         # API请求管理
│       │   ├── auth.js        # 认证管理
│       │   ├── router.js      # 路由管理
│       │   └── utils.js       # 工具函数
│       ├── components/        # UI组件
│       │   ├── Button.js      # 按钮组件
│       │   ├── Card.js        # 卡片组件
│       │   └── Form.js        # 表单组件
│       ├── pages/             # 页面逻辑
│       │   ├── upload.js      # 上传页面
│       │   ├── resign.js      # 重签页面
│       │   ├── certificate.js # 证书页面
│       │   └── admin.js       # 管理页面
│       └── app.js             # 应用主入口
└── README.md                  # 项目说明
```

## 🚀 功能模块

### 📤 上传中心
- **双认证支持**: API Key 和 Apple ID 两种认证方式
- **文件上传**: 支持拖拽上传，实时进度显示
- **应用信息**: 自动解析 IPA 文件信息
- **上传记录**: 完整的上传历史和状态跟踪

### 🔐 重签工具
- **IPA 重签**: 支持修改 Bundle ID 和应用名称
- **安装页面**: 自动生成安装页面和二维码
- **重签记录**: 管理重签历史和下载链接

### 📋 证书管理
- **CSR 生成**: 在线生成证书签名请求
- **CER 转 P12**: 证书格式转换工具
- **证书记录**: 证书操作历史管理

### ⚙️ 管理后台
- **用户管理**: 用户账号管理和权限控制
- **系统监控**: 实时统计和系统状态
- **数据管理**: 上传和重签记录管理
- **系统操作**: 缓存清理、数据备份等

## 🎨 设计系统

### 颜色系统
- **主色调**: 蓝色系 (#0ea5e9)
- **语义化颜色**: 成功、警告、错误、信息
- **中性色**: 灰色系，支持暗色主题

### 组件库
- **按钮**: 多种类型和尺寸
- **卡片**: 灵活的内容容器
- **表单**: 完整的表单控件
- **导航**: 响应式导航栏
- **模态框**: 多种样式的对话框

### 响应式设计
- **移动优先**: 从小屏幕开始设计
- **断点系统**: 640px, 768px, 1024px, 1280px
- **弹性布局**: CSS Grid 和 Flexbox

## 🔧 开发指南

### 本地开发
1. 克隆项目到本地
2. 使用本地服务器运行（如 Live Server）
3. 访问 `index.html` 开始开发

### 添加新页面
1. 在 `assets/js/pages/` 创建页面模块
2. 在 `index.html` 添加页面容器
3. 在 `app.js` 注册路由
4. 导入页面模块

### 创建新组件
1. 在 `assets/js/components/` 创建组件类
2. 在 `assets/css/components/` 添加样式
3. 导出组件供其他模块使用

### API 集成
```javascript
import api from './core/api.js';

// GET 请求
const response = await api.get('/api/endpoint');

// POST 请求
const response = await api.post('/api/endpoint', data);

// 文件上传
const response = await api.upload('/api/upload', formData);
```

### 认证管理
```javascript
import auth from './core/auth.js';

// 检查登录状态
if (auth.isAuthenticated()) {
  // 用户已登录
}

// 检查管理员权限
if (auth.isAdmin()) {
  // 用户是管理员
}

// 获取用户信息
const user = auth.getUser();
```

## 📱 浏览器支持

- **现代浏览器**: Chrome 60+, Firefox 60+, Safari 12+, Edge 79+
- **移动浏览器**: iOS Safari 12+, Chrome Mobile 60+
- **特性要求**: ES6 模块、CSS Grid、Flexbox、CSS 自定义属性

## 🔄 与旧版本对比

### 改进点
1. **代码组织**: 从混合式改为模块化
2. **样式管理**: 从重复样式改为组件化CSS
3. **状态管理**: 统一的认证和路由管理
4. **用户体验**: 现代化的界面和交互
5. **可维护性**: 清晰的架构和代码结构

### 迁移建议
1. **逐步迁移**: 可以与旧版本并存
2. **API 兼容**: 保持后端 API 接口不变
3. **数据迁移**: 用户数据和设置可以复用
4. **功能对等**: 所有原有功能都已重新实现

## 🚀 部署说明

### 静态部署
1. 将整个 `frontend` 目录上传到 Web 服务器
2. 确保服务器支持 SPA 路由（可选）
3. 配置 HTTPS（推荐）

### CDN 优化
1. 将 CSS 和 JS 文件部署到 CDN
2. 更新 HTML 中的资源链接
3. 启用 Gzip 压缩

### 性能优化
1. **资源压缩**: 压缩 CSS 和 JS 文件
2. **图片优化**: 使用 WebP 格式
3. **缓存策略**: 设置合适的缓存头
4. **预加载**: 关键资源预加载

## 📞 技术支持

如有问题或建议，请联系开发团队或提交 Issue。

---

**XIOS** - 专业的 iOS 应用管理工具 © 2024
