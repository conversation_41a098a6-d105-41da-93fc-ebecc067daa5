/* CSS Reset - 现代化重置样式 */

/* 1. 使用更好的盒模型 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 2. 移除默认边距和内边距 */
* {
  margin: 0;
  padding: 0;
}

/* 3. 改善媒体元素的默认样式 */
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 4. 移除内置表单元素的样式 */
input,
button,
textarea,
select {
  font: inherit;
  color: inherit;
}

/* 5. 避免文本溢出 */
p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 6. 改善行高 */
body {
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 7. 改善根元素的默认样式 */
html {
  height: 100%;
  scroll-behavior: smooth;
}

/* 8. 改善body的默认样式 */
body {
  min-height: 100%;
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* 9. 创建根堆叠上下文 */
#root,
#__next {
  isolation: isolate;
}

/* 10. 改善列表样式 */
ul,
ol {
  list-style: none;
}

/* 11. 改善链接样式 */
a {
  color: inherit;
  text-decoration: none;
}

a:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* 12. 改善按钮样式 */
button {
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 13. 改善表单元素样式 */
input,
textarea,
select {
  background-color: transparent;
  border: none;
  outline: none;
}

input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* 14. 改善表格样式 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 15. 改善引用样式 */
blockquote,
q {
  quotes: none;
}

blockquote::before,
blockquote::after,
q::before,
q::after {
  content: '';
  content: none;
}

/* 16. 隐藏元素的辅助类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 17. 跳过链接（无障碍） */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-base);
  z-index: var(--z-tooltip);
}

.skip-link:focus {
  top: 6px;
}

/* 18. 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 19. 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-primary: var(--gray-900);
    --border-secondary: var(--gray-800);
  }
}

/* 20. 打印样式 */
@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: " (" attr(href) ")";
  }

  abbr[title]::after {
    content: " (" attr(title) ")";
  }

  pre {
    white-space: pre-wrap !important;
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}
