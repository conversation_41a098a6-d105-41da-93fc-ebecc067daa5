/* 表单组件样式 */

/* 表单容器 */
.form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-inline {
  flex-direction: row;
  align-items: end;
  gap: var(--space-4);
}

/* 表单组 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group-inline {
  flex-direction: row;
  align-items: center;
  gap: var(--space-3);
}

/* 表单标签 */
.form-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  line-height: var(--leading-normal);
}

.form-label-required::after {
  content: ' *';
  color: var(--color-error);
}

.form-label-optional::after {
  content: ' (可选)';
  color: var(--text-muted);
  font-weight: var(--font-normal);
}

/* 表单控件基础样式 */
.form-control {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:disabled {
  background-color: var(--bg-muted);
  color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-control::placeholder {
  color: var(--text-muted);
}

/* 表单控件尺寸 */
.form-control-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.form-control-lg {
  padding: var(--space-4) var(--space-5);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

/* 输入框 */
.form-input {
  /* 继承 form-control 样式 */
}

/* 文本域 */
.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-textarea-fixed {
  resize: none;
}

/* 选择框 */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--space-10);
  appearance: none;
}

/* 复选框和单选框 */
.form-checkbox,
.form-radio {
  width: 16px;
  height: 16px;
  margin: 0;
  accent-color: var(--color-primary);
}

.form-checkbox-group,
.form-radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.form-checkbox-group-inline,
.form-radio-group-inline {
  flex-direction: row;
  gap: var(--space-6);
}

.form-checkbox-item,
.form-radio-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
}

.form-checkbox-item:hover,
.form-radio-item:hover {
  color: var(--color-primary);
}

/* 开关 */
.form-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.form-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.form-switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-secondary);
  transition: var(--transition-fast);
  border-radius: 24px;
}

.form-switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: var(--transition-fast);
  border-radius: 50%;
}

.form-switch input:checked + .form-switch-slider {
  background-color: var(--color-primary);
}

.form-switch input:checked + .form-switch-slider:before {
  transform: translateX(20px);
}

.form-switch input:focus + .form-switch-slider {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 文件上传 */
.form-file {
  position: relative;
  display: inline-block;
  width: 100%;
}

.form-file input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.form-file-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  border: 2px dashed var(--border-primary);
  border-radius: var(--radius-xl);
  background-color: var(--bg-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
}

.form-file-label:hover {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.form-file-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-2);
  color: var(--text-muted);
}

.form-file-text {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.form-file-hint {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.form-file.has-file .form-file-label {
  border-color: var(--color-success);
  background-color: var(--color-success-light);
}

/* 表单帮助文本 */
.form-help {
  font-size: var(--text-sm);
  color: var(--text-muted);
  line-height: var(--leading-normal);
}

/* 表单错误状态 */
.form-group.has-error .form-control {
  border-color: var(--color-error);
}

.form-group.has-error .form-control:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
  font-size: var(--text-sm);
  color: var(--color-error);
  line-height: var(--leading-normal);
}

/* 表单成功状态 */
.form-group.has-success .form-control {
  border-color: var(--color-success);
}

.form-group.has-success .form-control:focus {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-success {
  font-size: var(--text-sm);
  color: var(--color-success);
  line-height: var(--leading-normal);
}

/* 表单操作区 */
.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.form-actions-start {
  justify-content: flex-start;
}

.form-actions-center {
  justify-content: center;
}

.form-actions-between {
  justify-content: space-between;
}

/* 输入组 */
.input-group {
  display: flex;
  width: 100%;
}

.input-group .form-control {
  border-radius: 0;
  border-right-width: 0;
}

.input-group .form-control:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.input-group .form-control:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right-width: 1px;
}

.input-group-addon {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background-color: var(--bg-muted);
  border: 1px solid var(--border-primary);
  border-right-width: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.input-group-addon:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.input-group-addon:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right-width: 1px;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .form-inline {
    flex-direction: column;
    align-items: stretch;
  }

  .form-checkbox-group-inline,
  .form-radio-group-inline {
    flex-direction: column;
    gap: var(--space-3);
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions-between {
    flex-direction: column;
  }
}

/* IPA解析状态样式 */
.status-ready {
    color: #28a745;
    font-weight: 500;
}

.status-uploading {
    color: #007bff;
    font-weight: 500;
}

.status-error {
    color: #dc3545;
    font-weight: 500;
}

.status-parsing {
    color: #ffc107;
    font-weight: 500;
    animation: pulse 1.5s infinite;
}

.status-parsed {
    color: #28a745;
    font-weight: 500;
}

.status-parse-error {
    color: #fd7e14;
    font-weight: 500;
}

/* 解析动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}
