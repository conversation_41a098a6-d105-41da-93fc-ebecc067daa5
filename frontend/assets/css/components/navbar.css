/* 导航栏组件样式 */

/* 主导航栏 */
.navbar {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(8px);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

/* 品牌标识 */
.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.navbar-brand:hover {
  color: var(--color-primary-hover);
}

.navbar-brand-icon {
  font-size: var(--text-2xl);
}

.navbar-brand-logo {
  height: 2rem;
  width: auto;
  margin-right: var(--space-2);
  transition: transform var(--transition-base);
}

.navbar-brand:hover .navbar-brand-logo {
  transform: scale(1.05);
}

.navbar-brand-text {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
}

/* 导航菜单 */
.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-nav-item {
  position: relative;
}

.navbar-nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
}

.navbar-nav-link:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.navbar-nav-link:focus {
  outline: none;
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.navbar-nav-link.active {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
}

.navbar-nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: 1px;
}

.navbar-nav-icon {
  font-size: var(--text-lg);
}

/* 用户菜单 */
.navbar-user {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.navbar-user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-1);
}

.navbar-user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.navbar-user-role {
  font-size: var(--text-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.navbar-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: var(--text-inverse);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
}

/* 移动端菜单切换 */
.navbar-toggle {
  display: none;
  flex-direction: column;
  gap: 3px;
  padding: var(--space-2);
  background: none;
  border: none;
  cursor: pointer;
  border-radius: var(--radius-base);
  transition: background-color var(--transition-fast);
}

.navbar-toggle:hover {
  background-color: var(--bg-muted);
}

.navbar-toggle:focus {
  outline: none;
  background-color: var(--bg-muted);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.navbar-toggle-line {
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  border-radius: 1px;
  transition: all var(--transition-fast);
}

.navbar-toggle.active .navbar-toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.navbar-toggle.active .navbar-toggle-line:nth-child(2) {
  opacity: 0;
}

.navbar-toggle.active .navbar-toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* 移动端导航菜单 */
.navbar-mobile {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4);
}

.navbar-mobile.active {
  display: block;
}

.navbar-mobile-nav {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.navbar-mobile-nav .navbar-nav-link {
  justify-content: flex-start;
  padding: var(--space-4);
  border-radius: var(--radius-lg);
}

.navbar-mobile-nav .navbar-nav-link:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.navbar-mobile-user {
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.navbar-mobile-user .navbar-user-info {
  align-items: flex-start;
  flex: 1;
}

/* 标签页导航 */
.navbar-tabs {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.navbar-tabs::-webkit-scrollbar {
  display: none;
}

.navbar-tabs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  min-height: 48px;
}

.navbar-tabs-nav {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-tabs-item {
  white-space: nowrap;
}

.navbar-tabs-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all var(--transition-fast);
}

.navbar-tabs-link:hover {
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.navbar-tabs-link:focus {
  outline: none;
  color: var(--color-primary);
  background-color: var(--color-primary-light);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.navbar-tabs-link.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

/* 面包屑导航 */
.navbar-breadcrumb {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-3) 0;
}

.navbar-breadcrumb-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.navbar-breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.navbar-breadcrumb-item:not(:last-child)::after {
  content: '/';
  color: var(--text-muted);
}

.navbar-breadcrumb-link {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.navbar-breadcrumb-link:hover {
  color: var(--color-primary-hover);
}

.navbar-breadcrumb-link:focus {
  outline: none;
  color: var(--color-primary-hover);
  text-decoration: underline;
}

.navbar-breadcrumb-current {
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-toggle {
    display: flex;
  }

  .navbar-nav {
    display: none;
  }

  .navbar-user-info {
    display: none;
  }

  .navbar-container {
    padding: 0 var(--space-3);
  }

  .navbar-tabs-container,
  .navbar-breadcrumb-container {
    padding: 0 var(--space-3);
  }
}

@media (max-width: 640px) {
  .navbar-brand-text {
    display: none;
  }

  .navbar-container {
    height: 56px;
  }
}
