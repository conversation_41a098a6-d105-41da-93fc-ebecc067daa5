/* 按钮组件样式 */

/* 基础按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮尺寸 */
.btn-xs {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  border-radius: var(--radius-base);
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.btn-md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  border-radius: var(--radius-lg);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

/* 主要按钮 */
.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border: none;
  box-shadow: var(--shadow-colored);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover:not(:disabled) {
  background: var(--gradient-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow), var(--shadow-hover);
}

.btn-primary:hover:not(:disabled)::before {
  left: 100%;
}

.btn-primary:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-colored);
}

/* 次要按钮 */
.btn-secondary {
  background-color: var(--color-secondary);
  color: var(--text-inverse);
  border-color: var(--color-secondary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-secondary-hover);
  border-color: var(--color-secondary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 成功按钮 */
.btn-success {
  background-color: var(--color-success);
  color: var(--text-inverse);
  border-color: var(--color-success);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--color-success-hover);
  border-color: var(--color-success-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 警告按钮 */
.btn-warning {
  background-color: var(--color-warning);
  color: var(--text-inverse);
  border-color: var(--color-warning);
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--color-warning-hover);
  border-color: var(--color-warning-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 错误按钮 */
.btn-error {
  background-color: var(--color-error);
  color: var(--text-inverse);
  border-color: var(--color-error);
}

.btn-error:hover:not(:disabled) {
  background-color: var(--color-error-hover);
  border-color: var(--color-error-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 轮廓按钮 */
.btn-outline {
  background-color: transparent;
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--bg-muted);
  border-color: var(--border-secondary);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline-primary:hover:not(:disabled) {
  background-color: var(--color-primary);
  color: var(--text-inverse);
}

/* 幽灵按钮 */
.btn-ghost {
  background-color: transparent;
  color: var(--text-primary);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--bg-muted);
}

.btn-ghost-primary {
  background-color: transparent;
  color: var(--color-primary);
  border-color: transparent;
}

.btn-ghost-primary:hover:not(:disabled) {
  background-color: var(--color-primary-light);
}

/* 链接按钮 */
.btn-link {
  background-color: transparent;
  color: var(--color-primary);
  border-color: transparent;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.btn-link:hover:not(:disabled) {
  color: var(--color-primary-hover);
  background-color: transparent;
  transform: none;
  box-shadow: none;
}

/* 全宽按钮 */
.btn-block {
  width: 100%;
  justify-content: center;
}

/* 按钮组 */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right-width: 1px;
}

.btn-group .btn:hover {
  transform: none;
  z-index: 1;
  border-right-width: 1px;
}

/* 加载状态 */
.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: btn-spin 0.6s linear infinite;
}

@keyframes btn-spin {
  to {
    transform: rotate(360deg);
  }
}

/* 图标按钮 */
.btn-icon {
  padding: var(--space-3);
  aspect-ratio: 1;
}

.btn-icon.btn-xs {
  padding: var(--space-1);
}

.btn-icon.btn-sm {
  padding: var(--space-2);
}

.btn-icon.btn-lg {
  padding: var(--space-4);
}

.btn-icon.btn-xl {
  padding: var(--space-5);
}

/* 浮动操作按钮 */
.btn-fab {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
}

.btn-fab:hover {
  box-shadow: var(--shadow-xl);
}

/* 新增按钮变体 */

/* 强调按钮 */
.btn-accent {
  background: var(--gradient-accent);
  color: var(--text-inverse);
  border: none;
  box-shadow: 0 10px 25px -5px rgba(6, 182, 212, 0.25);
  position: relative;
  overflow: hidden;
}

.btn-accent:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.4), 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

/* 紫色按钮 */
.btn-purple {
  background: var(--gradient-purple);
  color: var(--text-inverse);
  border: none;
  box-shadow: 0 10px 25px -5px rgba(168, 85, 247, 0.25);
  position: relative;
  overflow: hidden;
}

.btn-purple:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.4), 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

/* 粉色按钮 */
.btn-pink {
  background: var(--gradient-pink);
  color: var(--text-inverse);
  border: none;
  box-shadow: 0 10px 25px -5px rgba(236, 72, 153, 0.25);
  position: relative;
  overflow: hidden;
}

.btn-pink:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(236, 72, 153, 0.4), 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

/* 彩虹按钮 */
.btn-rainbow {
  background: var(--gradient-rainbow);
  color: var(--text-inverse);
  border: none;
  box-shadow: var(--shadow-colored);
  position: relative;
  overflow: hidden;
  animation: rainbow-shift 3s ease-in-out infinite;
}

.btn-rainbow:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow), var(--shadow-hover);
}

@keyframes rainbow-shift {
  0%, 100% { filter: hue-rotate(0deg); }
  50% { filter: hue-rotate(180deg); }
}

/* 玻璃态按钮 */
.btn-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.btn-glass:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}



/* 响应式调整 */
@media (max-width: 640px) {
  .btn-fab {
    bottom: var(--space-4);
    right: var(--space-4);
  }
}
