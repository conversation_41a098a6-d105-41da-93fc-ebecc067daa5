/* Toast 通知组件样式 */

/* Toast 容器 */
.toast-container {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  pointer-events: none;
  max-width: 400px;
  width: 100%;
}

/* Toast 项目 */
.toast {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--space-4);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  min-height: 60px;
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--transition-base);
  border-left: 4px solid var(--border-primary);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

/* Toast 显示状态 */
.toast.show {
  transform: translateX(0);
  opacity: 1;
}

/* Toast 隐藏状态 */
.toast.hide {
  transform: translateX(100%);
  opacity: 0;
}

/* Toast 类型样式 */
.toast.success {
  border-left-color: var(--color-success);
  background: linear-gradient(135deg, var(--success-50) 0%, var(--bg-primary) 100%);
}

.toast.error {
  border-left-color: var(--color-error);
  background: linear-gradient(135deg, var(--error-50) 0%, var(--bg-primary) 100%);
}

.toast.warning {
  border-left-color: var(--color-warning);
  background: linear-gradient(135deg, var(--warning-50) 0%, var(--bg-primary) 100%);
}

.toast.info {
  border-left-color: var(--color-primary);
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--bg-primary) 100%);
}

/* Toast 图标 */
.toast-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  margin-top: 2px;
}

.toast.success .toast-icon {
  color: var(--color-success);
}

.toast.error .toast-icon {
  color: var(--color-error);
}

.toast.warning .toast-icon {
  color: var(--color-warning);
}

.toast.info .toast-icon {
  color: var(--color-primary);
}

/* Toast 内容 */
.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  line-height: var(--leading-tight);
}

.toast-message {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--leading-normal);
  word-wrap: break-word;
}

/* Toast 关闭按钮 */
.toast-close {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-base);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  margin-top: 2px;
}

.toast-close:hover {
  color: var(--text-primary);
  background-color: var(--bg-muted);
}

.toast-close:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 1px;
}

/* Toast 进度条 */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: var(--border-primary);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  overflow: hidden;
}

.toast-progress-bar {
  height: 100%;
  background-color: var(--color-primary);
  width: 100%;
  transform-origin: left;
  animation: toast-progress-countdown linear;
}

.toast.success .toast-progress-bar {
  background-color: var(--color-success);
}

.toast.error .toast-progress-bar {
  background-color: var(--color-error);
}

.toast.warning .toast-progress-bar {
  background-color: var(--color-warning);
}

.toast.info .toast-progress-bar {
  background-color: var(--color-primary);
}

@keyframes toast-progress-countdown {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* Toast 动画 */
@keyframes toast-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toast-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Toast 位置变体 */
.toast-container.top-left {
  top: var(--space-4);
  left: var(--space-4);
  right: auto;
}

.toast-container.top-left .toast {
  transform: translateX(-100%);
}

.toast-container.top-left .toast.show {
  transform: translateX(0);
}

.toast-container.top-left .toast.hide {
  transform: translateX(-100%);
}

.toast-container.top-center {
  top: var(--space-4);
  left: 50%;
  right: auto;
  transform: translateX(-50%);
}

.toast-container.top-center .toast {
  transform: translateY(-100%);
}

.toast-container.top-center .toast.show {
  transform: translateY(0);
}

.toast-container.top-center .toast.hide {
  transform: translateY(-100%);
}

.toast-container.bottom-right {
  top: auto;
  bottom: var(--space-4);
  right: var(--space-4);
}

.toast-container.bottom-right .toast {
  transform: translateX(100%);
}

.toast-container.bottom-right .toast.show {
  transform: translateX(0);
}

.toast-container.bottom-right .toast.hide {
  transform: translateX(100%);
}

.toast-container.bottom-left {
  top: auto;
  bottom: var(--space-4);
  left: var(--space-4);
  right: auto;
}

.toast-container.bottom-left .toast {
  transform: translateX(-100%);
}

.toast-container.bottom-left .toast.show {
  transform: translateX(0);
}

.toast-container.bottom-left .toast.hide {
  transform: translateX(-100%);
}

.toast-container.bottom-center {
  top: auto;
  bottom: var(--space-4);
  left: 50%;
  right: auto;
  transform: translateX(-50%);
}

.toast-container.bottom-center .toast {
  transform: translateY(100%);
}

.toast-container.bottom-center .toast.show {
  transform: translateY(0);
}

.toast-container.bottom-center .toast.hide {
  transform: translateY(100%);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .toast-container {
    left: var(--space-2);
    right: var(--space-2);
    max-width: none;
  }
  
  .toast {
    padding: var(--space-3);
    min-height: 50px;
  }
  
  .toast-title {
    font-size: var(--text-xs);
  }
  
  .toast-message {
    font-size: var(--text-xs);
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .toast.success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, var(--bg-primary) 100%);
  }
  
  .toast.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, var(--bg-primary) 100%);
  }
  
  .toast.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, var(--bg-primary) 100%);
  }
  
  .toast.info {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, var(--bg-primary) 100%);
  }
}
