/* 卡片组件样式 */

/* 基础卡片样式 */
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: all var(--transition-base);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-4px);
  border-color: var(--primary-200);
}

.card:hover::before {
  opacity: 1;
}

/* 卡片尺寸 */
.card-sm {
  border-radius: var(--radius-lg);
}

.card-md {
  border-radius: var(--radius-xl);
}

.card-lg {
  border-radius: var(--radius-2xl);
}

/* 卡片变体 */
.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
}

.card-outlined {
  border: 2px solid var(--border-primary);
  box-shadow: none;
}

.card-outlined:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

.card-filled {
  background-color: var(--bg-secondary);
  border: none;
  box-shadow: none;
}

.card-filled:hover {
  background-color: var(--bg-muted);
  box-shadow: var(--shadow-sm);
}

/* 卡片头部 */
.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.card-header:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.card-header-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-header-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: var(--space-1) 0 0 0;
}

.card-header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-left: auto;
}

.card-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
}

/* 卡片内容 */
.card-body {
  padding: var(--space-6);
}

.card-body:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.card-body:last-child {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}

/* 卡片底部 */
.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.card-footer:last-child {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}

.card-footer-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  justify-content: flex-end;
}

.card-footer-actions-start {
  justify-content: flex-start;
}

.card-footer-actions-center {
  justify-content: center;
}

.card-footer-actions-between {
  justify-content: space-between;
}

/* 卡片图片 */
.card-image {
  width: 100%;
  height: auto;
  display: block;
}

.card-image-top {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.card-image-bottom {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}

/* 卡片列表 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.card-list-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  transition: background-color var(--transition-fast);
}

.card-list-item:hover {
  background-color: var(--bg-muted);
}

.card-list-item-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-muted);
  border-radius: var(--radius-lg);
}

.card-list-item-content {
  flex: 1;
  min-width: 0;
}

.card-list-item-title {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
}

.card-list-item-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
}

.card-list-item-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* 卡片网格 */
.card-grid {
  display: grid;
  gap: var(--space-6);
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.card-grid-sm {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--space-4);
}

.card-grid-lg {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--space-8);
}

/* 特殊卡片类型 */
.card-stats {
  text-align: center;
  padding: var(--space-8);
}

.card-stats-value {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  margin: 0 0 var(--space-2) 0;
}

.card-stats-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

.card-stats-change {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  margin: var(--space-2) 0 0 0;
}

.card-stats-change.positive {
  color: var(--color-success);
}

.card-stats-change.negative {
  color: var(--color-error);
}

/* 可点击卡片 */
.card-clickable {
  cursor: pointer;
  user-select: none;
}

.card-clickable:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-clickable:active {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 加载状态 */
.card-loading {
  position: relative;
  overflow: hidden;
}

.card-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: card-shimmer 1.5s infinite;
}

@keyframes card-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card-header,
  .card-body,
  .card-footer {
    padding: var(--space-4);
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .card-grid-sm,
  .card-grid-lg {
    grid-template-columns: 1fr;
  }
}

/* 新增卡片变体 */

/* 渐变卡片 */
.card-gradient {
  background: var(--gradient-card);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
}

.card-gradient-primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border: none;
}

.card-gradient-accent {
  background: var(--gradient-accent);
  color: var(--text-inverse);
  border: none;
}

.card-gradient-purple {
  background: var(--gradient-purple);
  color: var(--text-inverse);
  border: none;
}

.card-gradient-pink {
  background: var(--gradient-pink);
  color: var(--text-inverse);
  border: none;
}

/* 玻璃态卡片 */
.card-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-glass:hover {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 发光卡片 */
.card-glow {
  box-shadow: var(--shadow-glow);
  border: 1px solid var(--primary-300);
}

.card-glow:hover {
  box-shadow: var(--shadow-glow), var(--shadow-hover);
  border-color: var(--primary-400);
}

/* 彩虹边框卡片 */
.card-rainbow {
  position: relative;
  background: var(--bg-primary);
  border: none;
}

.card-rainbow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-rainbow);
  border-radius: inherit;
  z-index: -1;
  animation: rainbow-shift 3s ease-in-out infinite;
}

/* 悬浮卡片 */
.card-floating {
  box-shadow: var(--shadow-xl);
  transform: translateY(-8px);
}

.card-floating:hover {
  transform: translateY(-12px);
  box-shadow: var(--shadow-2xl);
}

/* 记录卡片样式 */
.record-card {
  transition: all var(--transition-base);
  border-left: 4px solid transparent;
}

.record-card:hover {
  border-left-color: var(--primary-400);
  background-color: var(--bg-secondary);
}

.record-card .record-header {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  margin-bottom: var(--space-3);
}

.record-card .record-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-muted);
}

.record-card .record-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.record-card .record-info {
  flex: 1;
  min-width: 0;
}

.record-card .record-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.record-card .record-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.record-card .record-status {
  flex-shrink: 0;
  margin-left: var(--space-2);
}

.record-card .record-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-3);
  font-size: var(--text-sm);
}

.record-card .record-meta > div {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.record-card .record-meta strong {
  color: var(--text-primary);
  font-weight: var(--font-medium);
}

.record-card .record-meta span {
  color: var(--text-secondary);
}

.record-card .record-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

/* 记录状态样式 */
.record-card.status-success {
  border-left-color: var(--success-400);
}

.record-card.status-error {
  border-left-color: var(--error-400);
}

.record-card.status-warning {
  border-left-color: var(--warning-400);
}

.record-card.status-info {
  border-left-color: var(--info-400);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .record-card .record-meta {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .record-card .record-actions {
    flex-direction: column;
  }

  .record-card .record-actions .btn {
    width: 100%;
    justify-content: center;
  }
}
