/**
 * XIOS 主题样式
 * 统一的主题配色和视觉效果
 */

/* 全局主题样式 */
body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

/* 动态背景效果 */
body::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
  animation: float-bg 20s ease-in-out infinite;
  z-index: -2;
  pointer-events: none;
}

body::after {
  content: '';
  position: fixed;
  bottom: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(168, 85, 247, 0.03) 0%, transparent 70%);
  animation: float-bg 25s ease-in-out infinite reverse;
  z-index: -2;
  pointer-events: none;
}

@keyframes float-bg {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* 页面标题样式增强 */
.section-title {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--font-bold);
  text-align: center;
  margin-bottom: var(--space-4);
}

.section-subtitle {
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--space-8);
}

/* 卡片标题增强 */
.card-header-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header-subtitle {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin-top: var(--space-1);
}

/* 全局focus样式重置 */
*:focus {
  outline: none;
}

/* 为可访问性提供自定义focus样式 */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* 特殊元素的focus样式覆盖 */
.navbar-nav-link:focus,
.navbar-tabs-link:focus,
.navbar-breadcrumb-link:focus {
  /* 这些元素在navbar.css中已有专门的focus样式 */
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* 移除某些元素的focus样式 */
.navbar-brand:focus {
  box-shadow: none;
  outline: 2px solid rgba(99, 102, 241, 0.3);
  outline-offset: 2px;
}

/* 导航栏增强 */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.navbar-brand {
  font-weight: var(--font-bold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 导航链接增强效果 */
.navbar-nav-link {
  position: relative;
  transition: var(--transition-base);
}

/* 移除重复的after伪元素，使用navbar.css中的样式 */

/* 表单增强 */
.form-control:focus {
  border-color: var(--primary-400);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-label-required::after {
  content: ' *';
  color: var(--color-error);
}

/* 进度条增强 */
.progress {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  background: var(--gradient-primary);
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 状态徽章增强 */
.badge {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
}

.badge-secondary {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  color: white;
}

.badge-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.badge-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.badge-info {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: var(--text-inverse);
}

/* 模态框增强 */
.modal-backdrop {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-2xl), 0 0 40px rgba(99, 102, 241, 0.1);
}

/* 工具提示增强 */
.tooltip {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  color: var(--text-inverse);
  font-size: var(--text-sm);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-accent);
}

/* 选择文本样式 */
::selection {
  background: rgba(99, 102, 241, 0.2);
  color: var(--text-primary);
}

::-moz-selection {
  background: rgba(99, 102, 241, 0.2);
  color: var(--text-primary);
}

/* 焦点样式 */
*:focus {
  outline: 2px solid var(--primary-400);
  outline-offset: 2px;
}

/* 动画增强 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

/* 页面切换动画 */
.section {
  animation: fadeIn 0.3s ease-out;
}

.card {
  animation: scaleIn 0.3s ease-out;
}

.navbar-nav-link {
  animation: slideIn 0.3s ease-out;
}

/* 响应式增强 */
@media (max-width: 768px) {
  body::before,
  body::after {
    display: none;
  }
  
  .section-title {
    font-size: var(--text-2xl);
  }
  
  .card-header-title {
    font-size: var(--text-lg);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
  
  .navbar {
    background: rgba(26, 32, 44, 0.95);
    border-bottom-color: rgba(99, 102, 241, 0.2);
  }
  
  .modal-content {
    background: rgba(26, 32, 44, 0.95);
  }
}

/* 认证指南样式 */
.auth-guide {
  height: 100%;
}

.guide-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.guide-steps {
  list-style: none;
  counter-reset: step-counter;
  padding: 0;
}

.guide-steps li {
  counter-increment: step-counter;
  position: relative;
  padding: var(--space-3) 0 var(--space-3) var(--space-10);
  border-left: 2px solid var(--border-secondary);
  margin-bottom: var(--space-2);
}

.guide-steps li::before {
  content: counter(step-counter);
  position: absolute;
  left: -12px;
  top: var(--space-3);
  width: 24px;
  height: 24px;
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
}

.guide-steps li:last-child {
  border-left-color: transparent;
}

.guide-list {
  list-style: none;
  padding: 0;
}

.guide-list li {
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-secondary);
}

.guide-list li:last-child {
  border-bottom: none;
}

.guide-tips {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
}

.tip-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: var(--text-base);
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* 表单分组样式 */
.form-section {
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.form-section h4 {
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-secondary);
}

.form-actions {
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-secondary);
}

/* 文件上传区域样式 */
.file-upload-area {
  border: 2px dashed var(--border-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition-base);
  cursor: pointer;
  background: var(--bg-tertiary);
}

.file-upload-area:hover {
  border-color: var(--primary-400);
  background: var(--bg-secondary);
}

.file-upload-area.dragover {
  border-color: var(--primary-500);
  background: rgba(99, 102, 241, 0.05);
  transform: scale(1.02);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.7;
}

.upload-text {
  color: var(--text-secondary);
}

.upload-text p {
  margin: var(--space-1) 0;
}

.upload-text strong {
  color: var(--text-primary);
}

.upload-text small {
  font-size: var(--text-sm);
  opacity: 0.8;
}

/* 上传进度详情样式 */
.upload-info {
  font-size: var(--text-sm);
}

.upload-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.upload-percentage {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

.upload-details {
  width: 100%;
}

.upload-main-text {
  font-weight: var(--font-medium);
  margin-bottom: var(--space-1);
}

.upload-sub-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-xs);
  color: var(--text-secondary);
  gap: var(--space-4);
}

.upload-speed {
  color: var(--primary-600);
  font-weight: var(--font-medium);
}

.upload-remaining {
  color: var(--text-tertiary);
}

@media (max-width: 768px) {
  .upload-sub-text {
    flex-direction: column;
    gap: var(--space-1);
    align-items: flex-start;
  }
}

/* 步骤导航样式 */
.upload-steps-indicator {
  margin-bottom: var(--space-6);
}

.steps-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-4);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.step.active {
  opacity: 1;
}

.step.completed {
  opacity: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--gray-200);
  color: var(--gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-semibold);
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: var(--primary-600);
  color: white;
}

.step.completed .step-number {
  background: var(--success-600);
  color: white;
}

.step.completed .step-number::before {
  content: '✓';
}

.step-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  text-align: center;
}

.step.active .step-label {
  color: var(--primary-600);
}

.step.completed .step-label {
  color: var(--success-600);
}

.step-divider {
  width: 60px;
  height: 2px;
  background: var(--gray-200);
  transition: all 0.3s ease;
}

.step.completed + .step-divider {
  background: var(--success-600);
}

/* 步骤面板样式 */
.upload-step-content {
  min-height: 400px;
  position: relative;
}

.step-panel {
  display: none;
  animation: fadeIn 0.3s ease;
}

.step-panel.active {
  display: block;
}

.step-header {
  text-align: center;
  margin-bottom: var(--space-6);
}

.step-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.step-subtitle {
  color: var(--text-secondary);
  font-size: var(--text-base);
}

/* 步骤导航按钮 */
.step-navigation {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--space-3);
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-color);
}

.step-navigation #prev-step-btn {
  margin-right: auto;
}

/* 上传摘要样式 */
.upload-summary-card {
  background: var(--gray-50);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  margin-bottom: var(--space-4);
}

.summary-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.summary-grid {
  display: grid;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.summary-value {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.summary-warning {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--warning-50);
  border: 1px solid var(--warning-200);
  border-radius: var(--radius-md);
}

.warning-icon {
  font-size: var(--text-lg);
  flex-shrink: 0;
}

.warning-text {
  font-size: var(--text-sm);
  color: var(--warning-800);
}

/* 文件选择容器 */
.file-selection-container {
  max-width: 600px;
  margin: 0 auto;
}

.file-info-display {
  margin-top: var(--space-6);
  padding: var(--space-6);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #e2e8f0;
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.file-info-display h4 {
  color: #1e293b;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.file-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.file-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.file-info-label {
  font-size: var(--text-sm);
  color: #64748b;
  font-weight: var(--font-medium);
}

.file-info-value {
  font-size: var(--text-base);
  color: #1e293b;
  font-weight: var(--font-semibold);
}

/* 认证配置容器 */
.auth-config-container {
  max-width: 700px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.config-section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: #1e293b;
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.config-section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: var(--radius-full);
}

.optional-label {
  font-size: var(--text-sm);
  color: #64748b;
  font-weight: var(--font-normal);
}

/* 认证方式选择 */
.auth-method-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.auth-method-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

/* 认证字段区域 */
.auth-fields-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 认证指南区域 */
.auth-guide-section {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.auth-guide-compact {
  font-size: var(--text-sm);
  line-height: 1.6;
}

/* 上传摘要区域 */
.upload-summary-section,
.release-notes-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}



/* 认证字段网格布局 */
.auth-fields-grid {
  display: grid;
  gap: var(--space-4);
}

.auth-fields-grid .form-field {
  margin-bottom: 0;
}

/* 文件上传区域美化 */
.file-upload-area {
  border: 2px dashed #cbd5e1;
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: #6366f1;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.file-upload-area.dragover {
  border-color: #6366f1;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  transform: scale(1.02);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.7;
}

.upload-text h4 {
  margin: 0 0 var(--space-2) 0;
  color: #1e293b;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
}

.upload-text p {
  margin: 0 0 var(--space-1) 0;
  color: #64748b;
  font-size: var(--text-base);
}

.upload-text small {
  color: #94a3b8;
  font-size: var(--text-sm);
}

/* 上传成功状态 */
.upload-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
}

.upload-success .upload-icon {
  font-size: 3rem;
  color: #10b981;
}

.upload-success .upload-text h4 {
  margin: 0 0 var(--space-2) 0;
  color: #10b981;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
}

.upload-success .upload-text p {
  margin: 0 0 var(--space-1) 0;
  color: #1e293b;
  font-size: var(--text-base);
}

.upload-success .upload-text small {
  color: #64748b;
  font-size: var(--text-sm);
}

.upload-success .file-details {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  margin: var(--space-2) 0;
  font-size: var(--text-sm);
  color: #64748b;
}

.upload-success .file-details span {
  padding: var(--space-1) var(--space-2);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--radius-sm);
  border: 1px solid #e2e8f0;
}

.upload-success .file-details .status-ready {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
  font-weight: var(--font-medium);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .file-selection-container,
  .auth-config-container {
    padding: 0 var(--space-4);
  }

  .file-info-grid {
    grid-template-columns: 1fr;
  }

  .auth-method-options {
    grid-template-columns: 1fr;
  }

  .upload-icon {
    font-size: 2rem;
  }

  .upload-text h4 {
    font-size: var(--text-base);
  }
}

/* 分页组件样式 */
.pagination-container {
  margin-top: var(--space-6);
}

.pagination-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.page-size-selector {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: white;
  color: var(--text-primary);
  cursor: pointer;
}

.page-size-selector:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.pagination-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-50);
  border-color: var(--primary-200);
  color: var(--primary-600);
}

.pagination-btn.active {
  background: var(--primary-500);
  border-color: var(--primary-500);
  color: white;
  cursor: default;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-ellipsis {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  color: var(--text-muted);
  font-size: var(--text-sm);
}

/* 响应式分页 */
@media (max-width: 768px) {
  .pagination-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .pagination-info {
    text-align: center;
    order: 2;
  }

  .pagination-controls {
    justify-content: center;
    order: 1;
    flex-wrap: wrap;
  }

  .pagination-btn {
    min-width: 36px;
    height: 36px;
    font-size: var(--text-xs);
  }
}

/* 上传记录样式优化 */
.upload-record-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  font-size: var(--text-sm);
}

.upload-record-meta > div {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.upload-record-meta strong {
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* IP地址和用户名特殊样式 */
.record-ip {
  font-family: var(--font-mono);
  color: var(--primary-600);
  background: var(--primary-50);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
}

.record-username {
  color: var(--success-600);
  background: var(--success-50);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

/* 响应式记录显示 */
@media (max-width: 768px) {
  .upload-record-meta {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .steps-container {
    gap: var(--space-2);
  }

  .step-number {
    width: 32px;
    height: 32px;
    font-size: var(--text-sm);
  }

  .step-label {
    font-size: var(--text-xs);
  }

  .step-divider {
    width: 40px;
  }

  .step-navigation {
    flex-direction: column;
    gap: var(--space-3);
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .summary-value {
    max-width: 100%;
    text-align: left;
  }
}

/* 上传进度容器样式 */
.upload-progress-container {
  max-width: 600px;
  margin: 0 auto;
}

.progress-main {
  margin-bottom: var(--space-6);
}

.progress-bar-container {
  width: 100%;
  height: 12px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-4);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-text {
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.progress-percentage {
  font-weight: var(--font-bold);
  color: var(--primary-600);
  font-size: var(--text-lg);
}

/* 进度步骤样式 */
.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.progress-step.active {
  opacity: 1;
}

.progress-step .step-icon {
  font-size: var(--text-xl);
  margin-bottom: var(--space-1);
}

.progress-step .step-text {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  text-align: center;
}

.progress-step.active .step-text {
  color: var(--primary-600);
}

/* 进度详情样式 */
.progress-details {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.detail-value {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* 特殊的上传信息样式 */
#upload-speed {
  color: var(--primary-600);
  font-weight: var(--font-bold);
}

#upload-remaining {
  color: var(--warning-600);
  font-weight: var(--font-medium);
}

#upload-elapsed {
  color: var(--success-600);
  font-weight: var(--font-medium);
}

#upload-uploaded-size {
  color: var(--primary-700);
  font-weight: var(--font-bold);
}

/* 认证提示样式 */
.auth-tip {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  padding: var(--space-3);
  background: var(--blue-50);
  border: 1px solid var(--blue-200);
  border-radius: var(--radius-md);
}

.tip-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
}

.tip-text {
  font-size: var(--text-sm);
  color: var(--blue-800);
}

/* 管理后台专用样式 */
.admin-dashboard {
  animation: fadeIn 0.6s ease-out;
}

.admin-tab {
  animation: fadeIn 0.4s ease-out;
}

/* 用户统计横向布局 */
.user-stats-row {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.user-stat-item {
  flex: 1;
  text-align: center;
  padding: var(--space-3);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
  transition: all var(--transition-base);
}

.user-stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: var(--space-2);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: var(--font-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* 表格样式优化 */
.table-responsive {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  background: var(--bg-primary);
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.table thead {
  background: var(--bg-secondary);
}

.table th {
  padding: var(--space-4);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-primary);
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-secondary);
  color: var(--text-primary);
  vertical-align: middle;
}

.table tbody tr:hover {
  background: var(--bg-secondary);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 激活码显示样式 */
.code-display {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: var(--bg-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  border: 1px solid var(--border-secondary);
}

/* GitHub账号管理专用样式 */
.account-info {
  min-width: 180px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-dot.status-idle {
  background-color: var(--color-success);
}

.status-dot.status-in-use {
  background-color: var(--color-warning);
  animation: pulse 2s infinite;
}

.status-dot.status-danger {
  background-color: var(--color-error);
}

.status-dot.status-warning {
  background-color: var(--color-warning);
}

.usage-info {
  min-width: 200px;
}

.usage-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: var(--space-2);
}

.usage-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-success), var(--color-warning), var(--color-error));
  transition: width var(--transition-base);
}

.usage-text {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.usage-stats {
  font-size: var(--text-xs);
  line-height: 1.4;
}

.usage-stats > div {
  margin-bottom: 0.25rem;
}

/* GitHub统计横向布局 */
.github-stats-row {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.github-stat-item {
  flex: 1;
  text-align: center;
  padding: var(--space-3);
  background: var(--bg-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
  transition: all var(--transition-base);
}

.github-stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.github-stat-item.stat-primary {
  border-color: var(--color-primary);
}

.github-stat-item.stat-success {
  border-color: var(--color-success);
}

.github-stat-item.stat-warning {
  border-color: var(--color-warning);
}

.github-stat-item.stat-error {
  border-color: var(--color-error);
}

.github-stat-item .stat-value {
  color: var(--color-primary);
}

.github-stat-item.stat-success .stat-value {
  color: var(--color-success);
}

.github-stat-item.stat-warning .stat-value {
  color: var(--color-warning);
}

.github-stat-item.stat-error .stat-value {
  color: var(--color-error);
}

/* 证书配对样式 */
.certificate-pair-card {
  border: 1px solid var(--color-success);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(40, 167, 69, 0.05) 100%);
  transition: all var(--transition-base);
}

.certificate-pair-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.pair-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.pair-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.pair-title h5 {
  margin: 0;
  color: var(--text-primary);
}

.pair-content {
  border-top: 1px solid var(--border-secondary);
  padding-top: var(--space-3);
}

.pair-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.pair-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.info-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-secondary);
}

.info-value {
  font-size: var(--text-sm);
  color: var(--text-primary);
  word-break: break-all;
}

/* 横向统计卡片样式 */
.stats-grid-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.stat-card {
  display: flex;
  align-items: center;
  padding: var(--space-4);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  min-width: 200px;
  flex: 1;
  transition: all var(--transition-base);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  font-size: 2rem;
  margin-right: var(--space-3);
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: var(--font-bold);
  line-height: 1.2;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

/* 统计卡片颜色变体 */
.stat-card-primary {
  border-color: var(--color-primary);
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(59, 130, 246, 0.05) 100%);
}

.stat-card-primary .stat-value {
  color: var(--color-primary);
}

.stat-card-success {
  border-color: var(--color-success);
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(34, 197, 94, 0.05) 100%);
}

.stat-card-success .stat-value {
  color: var(--color-success);
}

.stat-card-error {
  border-color: var(--color-error);
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(239, 68, 68, 0.05) 100%);
}

.stat-card-error .stat-value {
  color: var(--color-error);
}

.stat-card-warning {
  border-color: var(--color-warning);
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(245, 158, 11, 0.05) 100%);
}

.stat-card-warning .stat-value {
  color: var(--color-warning);
}

.stat-card-info {
  border-color: var(--color-info);
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(14, 165, 233, 0.05) 100%);
}

.stat-card-info .stat-value {
  color: var(--color-info);
}

.stat-card-secondary {
  border-color: var(--border-secondary);
  background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(107, 114, 128, 0.05) 100%);
}

.stat-card-secondary .stat-value {
  color: var(--text-primary);
}

/* 统计摘要卡片样式 */
.stats-summary-card {
  margin-top: var(--space-6);
}

.stats-summary-card .card {
  border: 1px dashed var(--border-secondary);
  background: var(--bg-secondary);
  transition: all var(--transition-base);
}

.stats-summary-card .card:hover {
  border-color: var(--color-primary);
  background: var(--bg-primary);
  transform: translateY(-1px);
}

.stats-summary-card .btn-outline {
  border: 1px solid var(--border-secondary);
  background: transparent;
  color: var(--text-secondary);
  transition: all var(--transition-base);
}

.stats-summary-card .btn-outline:hover {
  border-color: var(--color-primary);
  background: var(--color-primary);
  color: white;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.navbar-tabs {
  border-bottom: 1px solid var(--border-primary);
  margin-bottom: var(--space-6);
}

.navbar-tabs-container {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.navbar-tabs-container::-webkit-scrollbar {
  display: none;
}

.navbar-tabs-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  min-width: max-content;
}

.navbar-tabs-item {
  margin: 0;
}

.navbar-tabs-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  color: var(--text-secondary);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all var(--transition-base);
  white-space: nowrap;
  font-weight: var(--font-medium);
}

.navbar-tabs-link:hover {
  color: var(--text-primary);
  background: var(--bg-secondary);
}

.navbar-tabs-link.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  background: var(--primary-50);
}

/* 统计卡片增强 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .navbar-tabs-link {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-sm);
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }

  /* 移动端用户统计调整 */
  .user-stats-row,
  .github-stats-row {
    flex-direction: column;
    gap: var(--space-2);
  }

  .user-stat-item,
  .github-stat-item {
    padding: var(--space-2);
  }

  .stat-icon {
    font-size: 1.5rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  /* 移动端统计摘要卡片优化 */
  .stats-summary-card .flex {
    flex-direction: column;
    gap: var(--space-2);
  }

  .stats-summary-card .btn-outline {
    width: 100%;
  }

  /* 移动端GitHub账号管理优化 */
  .account-info {
    min-width: auto;
  }

  .usage-info {
    min-width: auto;
  }

  .usage-bar {
    height: 6px;
  }

  .usage-text,
  .usage-stats {
    font-size: 0.65rem;
  }

  .status-indicator {
    font-size: var(--text-xs);
  }

  /* 移动端证书配对优化 */
  .pair-info-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .certificate-pair-card {
    padding: var(--space-3);
  }

  .pair-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .pair-title {
    width: 100%;
    justify-content: space-between;
  }

  /* 移动端统计卡片优化 */
  .stats-grid-horizontal {
    flex-direction: column;
    gap: var(--space-3);
  }

  .stat-card {
    min-width: auto;
    padding: var(--space-3);
  }

  .stat-icon {
    font-size: 1.5rem;
    margin-right: var(--space-2);
  }

  .stat-value {
    font-size: 1.25rem;
  }

  /* 移动端表格优化 */
  .table th,
  .table td {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .table th {
    font-size: 0.7rem;
  }

  /* 按钮组在移动端的优化 */
  .flex.gap-1.flex-wrap {
    gap: 0.25rem;
  }

  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
  }
}

/* 动画效果 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .progress-steps {
    flex-wrap: wrap;
    gap: var(--space-3);
  }

  .progress-step {
    flex: 1;
    min-width: 80px;
  }

  .progress-step .step-icon {
    font-size: var(--text-lg);
  }

  .progress-step .step-text {
    font-size: var(--text-xs);
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
}

/* 上传错误样式 */
.upload-error-container {
  text-align: center;
  padding: var(--space-8);
  max-width: 600px;
  margin: 0 auto;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
}

.error-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--error-600);
  margin-bottom: var(--space-3);
}

.error-message {
  font-size: var(--text-lg);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background: var(--error-50);
  border: 1px solid var(--error-200);
  border-radius: var(--radius-md);
}

.error-details {
  margin-bottom: var(--space-6);
  text-align: left;
}

.error-details-toggle {
  cursor: pointer;
  color: var(--primary-600);
  font-weight: var(--font-medium);
  padding: var(--space-2);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-md);
  background: var(--primary-50);
  transition: all 0.2s ease;
}

.error-details-toggle:hover {
  background: var(--primary-100);
}

.error-details-content {
  margin-top: var(--space-2);
  padding: var(--space-3);
  background: var(--gray-900);
  color: var(--gray-100);
  border-radius: var(--radius-md);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--text-sm);
  overflow-x: auto;
}

.error-details-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.error-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
  margin-bottom: var(--space-6);
}

.error-tips {
  text-align: left;
  padding: var(--space-4);
  background: var(--blue-50);
  border: 1px solid var(--blue-200);
  border-radius: var(--radius-md);
}

.error-tips h4 {
  margin-bottom: var(--space-3);
  color: var(--blue-800);
}

.error-tips ul {
  margin: 0;
  padding-left: var(--space-5);
  color: var(--blue-700);
}

.error-tips li {
  margin-bottom: var(--space-1);
}

/* 失败状态的进度步骤 */
.progress-step[data-step="failed"] {
  opacity: 1;
}

.progress-step[data-step="failed"] .step-icon {
  color: var(--error-600);
}

.progress-step[data-step="failed"] .step-text {
  color: var(--error-600);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .upload-error-container {
    padding: var(--space-4);
  }

  .error-icon {
    font-size: 3rem;
  }

  .error-title {
    font-size: var(--text-xl);
  }

  .error-message {
    font-size: var(--text-base);
  }

  .error-actions {
    flex-direction: column;
  }
}

/* 打印样式 */
@media print {
  body::before,
  body::after,
  .navbar,
  .btn,
  .modal-backdrop {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

/* 卡片头部操作按钮一致性 */
.card-header-actions {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.card-header-actions .btn {
  height: 40px; /* 确保所有按钮高度一致 */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-1);
  min-width: auto;
  padding: 0 var(--space-3);
}

.card-header-actions .btn span {
  display: flex;
  align-items: center;
}

/* 响应式按钮优化 */
@media (max-width: 768px) {
  .card-header-actions {
    flex-direction: column;
    gap: var(--space-2);
    width: 100%;
  }

  .card-header-actions .btn {
    width: 100%;
    justify-content: center;
  }
}
