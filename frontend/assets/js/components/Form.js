/**
 * 表单组件
 * 提供统一的表单创建和管理功能
 */

import { generateId, isEmpty } from '../core/utils.js';

export class FormField {
  constructor(options = {}) {
    this.options = {
      type: 'text', // text, email, password, number, tel, url, textarea, select, checkbox, radio, file
      name: '',
      label: '',
      placeholder: '',
      value: '',
      required: false,
      disabled: false,
      readonly: false,
      options: [], // for select, radio, checkbox
      multiple: false, // for select, file
      accept: '', // for file
      rows: 4, // for textarea
      min: null,
      max: null,
      pattern: null,
      validation: null, // custom validation function
      className: '',
      id: generateId('field'),
      onChange: null,
      onBlur: null,
      onFocus: null,
      ...options
    };

    this.element = null;
    this.inputElement = null;
    this.labelElement = null;
    this.errorElement = null;
    this.helpElement = null;
    this.isDestroyed = false;
    this.errors = [];
    
    this.create();
  }

  /**
   * 创建表单字段元素
   */
  create() {
    this.element = document.createElement('div');
    this.element.className = `form-group ${this.options.className}`.trim();
    this.element.id = this.options.id;
    
    this.createLabel();
    this.createInput();
    this.createHelp();
    this.createError();
    this.bindEvents();
  }

  /**
   * 创建标签
   */
  createLabel() {
    if (this.options.label) {
      this.labelElement = document.createElement('label');
      this.labelElement.className = 'form-label';
      this.labelElement.textContent = this.options.label;
      
      if (this.options.required) {
        this.labelElement.classList.add('form-label-required');
      }
      
      this.element.appendChild(this.labelElement);
    }
  }

  /**
   * 创建输入元素
   */
  createInput() {
    switch (this.options.type) {
      case 'textarea':
        this.createTextarea();
        break;
      case 'select':
        this.createSelect();
        break;
      case 'checkbox':
      case 'radio':
        this.createCheckboxRadio();
        break;
      case 'file':
        this.createFile();
        break;
      default:
        this.createTextInput();
        break;
    }
    
    this.updateInputState();
  }

  /**
   * 创建文本输入框
   */
  createTextInput() {
    this.inputElement = document.createElement('input');
    this.inputElement.type = this.options.type;
    this.inputElement.name = this.options.name;
    this.inputElement.className = 'form-control';
    this.inputElement.placeholder = this.options.placeholder;
    this.inputElement.value = this.options.value;
    
    if (this.options.pattern) {
      this.inputElement.pattern = this.options.pattern;
    }
    
    if (this.options.min !== null) {
      this.inputElement.min = this.options.min;
    }
    
    if (this.options.max !== null) {
      this.inputElement.max = this.options.max;
    }
    
    this.element.appendChild(this.inputElement);
    
    if (this.labelElement) {
      this.labelElement.setAttribute('for', this.inputElement.id = generateId('input'));
    }
  }

  /**
   * 创建文本域
   */
  createTextarea() {
    this.inputElement = document.createElement('textarea');
    this.inputElement.name = this.options.name;
    this.inputElement.className = 'form-control form-textarea';
    this.inputElement.placeholder = this.options.placeholder;
    this.inputElement.value = this.options.value;
    this.inputElement.rows = this.options.rows;
    
    this.element.appendChild(this.inputElement);
    
    if (this.labelElement) {
      this.labelElement.setAttribute('for', this.inputElement.id = generateId('textarea'));
    }
  }

  /**
   * 创建选择框
   */
  createSelect() {
    this.inputElement = document.createElement('select');
    this.inputElement.name = this.options.name;
    this.inputElement.className = 'form-control form-select';
    this.inputElement.multiple = this.options.multiple;
    
    // 添加选项
    this.options.options.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.textContent = option.label;
      
      if (option.value === this.options.value || 
          (Array.isArray(this.options.value) && this.options.value.includes(option.value))) {
        optionElement.selected = true;
      }
      
      this.inputElement.appendChild(optionElement);
    });
    
    this.element.appendChild(this.inputElement);
    
    if (this.labelElement) {
      this.labelElement.setAttribute('for', this.inputElement.id = generateId('select'));
    }
  }

  /**
   * 创建复选框/单选框
   */
  createCheckboxRadio() {
    const groupElement = document.createElement('div');
    groupElement.className = `form-${this.options.type}-group`;
    
    this.options.options.forEach((option, index) => {
      const itemElement = document.createElement('div');
      itemElement.className = `form-${this.options.type}-item`;
      
      const input = document.createElement('input');
      input.type = this.options.type;
      input.name = this.options.name;
      input.value = option.value;
      input.className = `form-${this.options.type}`;
      input.id = generateId(`${this.options.type}-${index}`);
      
      if (this.options.type === 'checkbox') {
        input.checked = Array.isArray(this.options.value) && this.options.value.includes(option.value);
      } else {
        input.checked = option.value === this.options.value;
      }
      
      const label = document.createElement('label');
      label.setAttribute('for', input.id);
      label.textContent = option.label;
      
      itemElement.appendChild(input);
      itemElement.appendChild(label);
      groupElement.appendChild(itemElement);
    });
    
    this.inputElement = groupElement;
    this.element.appendChild(groupElement);
  }

  /**
   * 创建文件输入框
   */
  createFile() {
    const fileContainer = document.createElement('div');
    fileContainer.className = 'form-file';
    
    this.inputElement = document.createElement('input');
    this.inputElement.type = 'file';
    this.inputElement.name = this.options.name;
    this.inputElement.multiple = this.options.multiple;
    this.inputElement.accept = this.options.accept;
    this.inputElement.id = generateId('file');
    
    const label = document.createElement('label');
    label.className = 'form-file-label';
    label.setAttribute('for', this.inputElement.id);
    
    label.innerHTML = `
      <div class="form-file-icon">📁</div>
      <div class="form-file-text">点击选择文件</div>
      <div class="form-file-hint">${this.options.placeholder || '支持拖拽上传'}</div>
    `;
    
    fileContainer.appendChild(this.inputElement);
    fileContainer.appendChild(label);
    this.element.appendChild(fileContainer);
    
    if (this.labelElement) {
      this.labelElement.setAttribute('for', this.inputElement.id);
    }
  }

  /**
   * 创建帮助文本
   */
  createHelp() {
    if (this.options.help) {
      this.helpElement = document.createElement('div');
      this.helpElement.className = 'form-help';
      this.helpElement.textContent = this.options.help;
      this.element.appendChild(this.helpElement);
    }
  }

  /**
   * 创建错误提示
   */
  createError() {
    this.errorElement = document.createElement('div');
    this.errorElement.className = 'form-error';
    this.errorElement.style.display = 'none';
    this.element.appendChild(this.errorElement);
  }

  /**
   * 更新输入状态
   */
  updateInputState() {
    if (!this.inputElement) return;
    
    if (this.options.type === 'checkbox' || this.options.type === 'radio') {
      const inputs = this.inputElement.querySelectorAll('input');
      inputs.forEach(input => {
        input.disabled = this.options.disabled;
        input.required = this.options.required;
      });
    } else {
      this.inputElement.disabled = this.options.disabled;
      this.inputElement.readOnly = this.options.readonly;
      this.inputElement.required = this.options.required;
    }
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (!this.inputElement) return;
    
    const handleEvent = (eventType, callback) => {
      if (callback) {
        if (this.options.type === 'checkbox' || this.options.type === 'radio') {
          const inputs = this.inputElement.querySelectorAll('input');
          inputs.forEach(input => {
            input.addEventListener(eventType, (e) => callback(e, this));
          });
        } else {
          this.inputElement.addEventListener(eventType, (e) => callback(e, this));
        }
      }
    };
    
    handleEvent('change', this.options.onChange);
    handleEvent('blur', this.options.onBlur);
    handleEvent('focus', this.options.onFocus);
    
    // 自动验证
    handleEvent('blur', () => this.validate());
  }

  /**
   * 获取值
   */
  getValue() {
    if (!this.inputElement) return null;
    
    switch (this.options.type) {
      case 'checkbox':
        const checkedBoxes = this.inputElement.querySelectorAll('input:checked');
        return Array.from(checkedBoxes).map(input => input.value);
      
      case 'radio':
        const checkedRadio = this.inputElement.querySelector('input:checked');
        return checkedRadio ? checkedRadio.value : null;
      
      case 'file':
        return this.inputElement.files;
      
      case 'number':
        return this.inputElement.value ? Number(this.inputElement.value) : null;
      
      default:
        return this.inputElement.value;
    }
  }

  /**
   * 设置值
   */
  setValue(value) {
    if (!this.inputElement) return this;
    
    this.options.value = value;
    
    switch (this.options.type) {
      case 'checkbox':
        const checkboxes = this.inputElement.querySelectorAll('input');
        checkboxes.forEach(input => {
          input.checked = Array.isArray(value) && value.includes(input.value);
        });
        break;
      
      case 'radio':
        const radios = this.inputElement.querySelectorAll('input');
        radios.forEach(input => {
          input.checked = input.value === value;
        });
        break;
      
      case 'file':
        // 文件输入框不能设置值
        break;
      
      default:
        this.inputElement.value = value || '';
        break;
    }
    
    return this;
  }

  /**
   * 验证字段
   */
  validate() {
    this.errors = [];
    const value = this.getValue();
    
    // 必填验证
    if (this.options.required && isEmpty(value)) {
      this.errors.push(`${this.options.label || this.options.name} 是必填项`);
    }
    
    // 自定义验证
    if (this.options.validation && typeof this.options.validation === 'function') {
      const result = this.options.validation(value, this);
      if (result !== true) {
        this.errors.push(result || '验证失败');
      }
    }
    
    this.updateErrorDisplay();
    return this.errors.length === 0;
  }

  /**
   * 更新错误显示
   */
  updateErrorDisplay() {
    if (this.errors.length > 0) {
      this.element.classList.add('has-error');
      this.errorElement.textContent = this.errors[0];
      this.errorElement.style.display = 'block';
    } else {
      this.element.classList.remove('has-error');
      this.errorElement.style.display = 'none';
    }
  }

  /**
   * 设置错误
   */
  setError(error) {
    this.errors = [error];
    this.updateErrorDisplay();
    return this;
  }

  /**
   * 清除错误
   */
  clearError() {
    this.errors = [];
    this.updateErrorDisplay();
    return this;
  }

  /**
   * 获取DOM元素
   */
  getElement() {
    return this.element;
  }

  /**
   * 获取输入元素
   */
  getInput() {
    return this.inputElement;
  }

  /**
   * 挂载到指定容器
   */
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }

    if (container && this.element) {
      container.appendChild(this.element);
    }

    return this;
  }

  /**
   * 从DOM中移除
   */
  unmount() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    return this;
  }

  /**
   * 获取焦点
   */
  focus() {
    if (this.inputElement) {
      if (this.options.type === 'checkbox' || this.options.type === 'radio') {
        const firstInput = this.inputElement.querySelector('input');
        if (firstInput) firstInput.focus();
      } else {
        this.inputElement.focus();
      }
    }
    return this;
  }

  /**
   * 销毁组件
   */
  destroy() {
    if (this.isDestroyed) return;

    this.unmount();

    this.element = null;
    this.inputElement = null;
    this.labelElement = null;
    this.errorElement = null;
    this.helpElement = null;
    this.options = null;
    this.isDestroyed = true;
  }
}

// 工厂函数
export function createFormField(options) {
  return new FormField(options);
}
