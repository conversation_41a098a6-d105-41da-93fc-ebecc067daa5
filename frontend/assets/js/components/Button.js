/**
 * 按钮组件
 * 提供统一的按钮创建和管理功能
 */

import { generateId } from '../core/utils.js';

export class Button {
  constructor(options = {}) {
    this.options = {
      text: '',
      type: 'primary', // primary, secondary, success, warning, error, outline, ghost, link
      size: 'md', // xs, sm, md, lg, xl
      icon: null,
      iconPosition: 'left', // left, right
      disabled: false,
      loading: false,
      block: false,
      onClick: null,
      className: '',
      id: generateId('btn'),
      ...options
    };

    this.element = null;
    this.isDestroyed = false;
    
    this.create();
  }

  /**
   * 创建按钮元素
   */
  create() {
    this.element = document.createElement('button');
    this.element.type = 'button';
    this.element.id = this.options.id;
    
    this.updateClasses();
    this.updateContent();
    this.updateState();
    this.bindEvents();
  }

  /**
   * 更新CSS类名
   */
  updateClasses() {
    const classes = ['btn'];
    
    // 添加类型类名
    classes.push(`btn-${this.options.type}`);
    
    // 添加尺寸类名
    if (this.options.size !== 'md') {
      classes.push(`btn-${this.options.size}`);
    }
    
    // 添加块级类名
    if (this.options.block) {
      classes.push('btn-block');
    }
    
    // 添加加载状态类名
    if (this.options.loading) {
      classes.push('btn-loading');
    }
    
    // 添加自定义类名
    if (this.options.className) {
      classes.push(this.options.className);
    }
    
    this.element.className = classes.join(' ');
  }

  /**
   * 更新按钮内容
   */
  updateContent() {
    const content = [];
    
    // 添加图标（左侧）
    if (this.options.icon && this.options.iconPosition === 'left') {
      content.push(`<span class="btn-icon">${this.options.icon}</span>`);
    }
    
    // 添加文本
    if (this.options.text) {
      content.push(`<span class="btn-text">${this.options.text}</span>`);
    }
    
    // 添加图标（右侧）
    if (this.options.icon && this.options.iconPosition === 'right') {
      content.push(`<span class="btn-icon">${this.options.icon}</span>`);
    }
    
    this.element.innerHTML = content.join('');
  }

  /**
   * 更新按钮状态
   */
  updateState() {
    this.element.disabled = this.options.disabled || this.options.loading;
    
    if (this.options.loading) {
      this.element.setAttribute('aria-busy', 'true');
    } else {
      this.element.removeAttribute('aria-busy');
    }
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (this.options.onClick) {
      this.element.addEventListener('click', (e) => {
        if (!this.options.disabled && !this.options.loading) {
          this.options.onClick(e, this);
        }
      });
    }
  }

  /**
   * 设置文本
   */
  setText(text) {
    this.options.text = text;
    this.updateContent();
    return this;
  }

  /**
   * 设置图标
   */
  setIcon(icon, position = 'left') {
    this.options.icon = icon;
    this.options.iconPosition = position;
    this.updateContent();
    return this;
  }

  /**
   * 设置类型
   */
  setType(type) {
    this.options.type = type;
    this.updateClasses();
    return this;
  }

  /**
   * 设置尺寸
   */
  setSize(size) {
    this.options.size = size;
    this.updateClasses();
    return this;
  }

  /**
   * 设置禁用状态
   */
  setDisabled(disabled) {
    this.options.disabled = disabled;
    this.updateState();
    return this;
  }

  /**
   * 设置加载状态
   */
  setLoading(loading) {
    this.options.loading = loading;
    this.updateClasses();
    this.updateState();
    return this;
  }

  /**
   * 设置块级显示
   */
  setBlock(block) {
    this.options.block = block;
    this.updateClasses();
    return this;
  }

  /**
   * 添加CSS类
   */
  addClass(className) {
    this.element.classList.add(className);
    return this;
  }

  /**
   * 移除CSS类
   */
  removeClass(className) {
    this.element.classList.remove(className);
    return this;
  }

  /**
   * 切换CSS类
   */
  toggleClass(className) {
    this.element.classList.toggle(className);
    return this;
  }

  /**
   * 获取DOM元素
   */
  getElement() {
    return this.element;
  }

  /**
   * 挂载到指定容器
   */
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }

  /**
   * 从DOM中移除
   */
  unmount() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    return this;
  }

  /**
   * 销毁组件
   */
  destroy() {
    if (this.isDestroyed) return;
    
    this.unmount();
    this.element = null;
    this.options = null;
    this.isDestroyed = true;
  }

  /**
   * 模拟点击
   */
  click() {
    if (this.element && !this.options.disabled && !this.options.loading) {
      this.element.click();
    }
    return this;
  }

  /**
   * 获取焦点
   */
  focus() {
    if (this.element) {
      this.element.focus();
    }
    return this;
  }

  /**
   * 失去焦点
   */
  blur() {
    if (this.element) {
      this.element.blur();
    }
    return this;
  }
}

/**
 * 按钮组组件
 */
export class ButtonGroup {
  constructor(options = {}) {
    this.options = {
      buttons: [],
      className: '',
      id: generateId('btn-group'),
      ...options
    };

    this.element = null;
    this.buttons = [];
    this.isDestroyed = false;
    
    this.create();
  }

  /**
   * 创建按钮组元素
   */
  create() {
    this.element = document.createElement('div');
    this.element.className = `btn-group ${this.options.className}`.trim();
    this.element.id = this.options.id;
    
    // 创建按钮
    this.options.buttons.forEach(buttonOptions => {
      this.addButton(buttonOptions);
    });
  }

  /**
   * 添加按钮
   */
  addButton(buttonOptions) {
    const button = new Button(buttonOptions);
    this.buttons.push(button);
    this.element.appendChild(button.getElement());
    return button;
  }

  /**
   * 移除按钮
   */
  removeButton(index) {
    if (index >= 0 && index < this.buttons.length) {
      const button = this.buttons[index];
      button.destroy();
      this.buttons.splice(index, 1);
    }
    return this;
  }

  /**
   * 获取按钮
   */
  getButton(index) {
    return this.buttons[index] || null;
  }

  /**
   * 获取所有按钮
   */
  getButtons() {
    return [...this.buttons];
  }

  /**
   * 获取DOM元素
   */
  getElement() {
    return this.element;
  }

  /**
   * 挂载到指定容器
   */
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }

  /**
   * 从DOM中移除
   */
  unmount() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    return this;
  }

  /**
   * 销毁组件
   */
  destroy() {
    if (this.isDestroyed) return;
    
    this.buttons.forEach(button => button.destroy());
    this.unmount();
    
    this.element = null;
    this.buttons = [];
    this.options = null;
    this.isDestroyed = true;
  }
}

// 工厂函数
export function createButton(options) {
  return new Button(options);
}

export function createButtonGroup(options) {
  return new ButtonGroup(options);
}
