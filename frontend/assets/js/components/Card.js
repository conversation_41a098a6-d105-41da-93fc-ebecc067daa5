/**
 * 卡片组件
 * 提供统一的卡片创建和管理功能
 */

import { generateId } from '../core/utils.js';

export class Card {
  constructor(options = {}) {
    this.options = {
      title: '',
      subtitle: '',
      content: '',
      footer: '',
      image: null,
      variant: 'default', // default, elevated, outlined, filled
      size: 'md', // sm, md, lg
      clickable: false,
      loading: false,
      className: '',
      id: generateId('card'),
      onClick: null,
      ...options
    };

    this.element = null;
    this.headerElement = null;
    this.bodyElement = null;
    this.footerElement = null;
    this.isDestroyed = false;
    
    this.create();
  }

  /**
   * 创建卡片元素
   */
  create() {
    this.element = document.createElement('div');
    this.element.id = this.options.id;
    
    this.updateClasses();
    this.createStructure();
    this.updateContent();
    this.bindEvents();
  }

  /**
   * 更新CSS类名
   */
  updateClasses() {
    const classes = ['card'];
    
    // 添加变体类名
    if (this.options.variant !== 'default') {
      classes.push(`card-${this.options.variant}`);
    }
    
    // 添加尺寸类名
    if (this.options.size !== 'md') {
      classes.push(`card-${this.options.size}`);
    }
    
    // 添加可点击类名
    if (this.options.clickable) {
      classes.push('card-clickable');
    }
    
    // 添加加载状态类名
    if (this.options.loading) {
      classes.push('card-loading');
    }
    
    // 添加自定义类名
    if (this.options.className) {
      classes.push(this.options.className);
    }
    
    this.element.className = classes.join(' ');
  }

  /**
   * 创建卡片结构
   */
  createStructure() {
    // 清空现有内容
    this.element.innerHTML = '';
    
    // 创建图片（如果有）
    if (this.options.image) {
      const imageElement = document.createElement('img');
      imageElement.className = 'card-image card-image-top';
      imageElement.src = this.options.image.src;
      imageElement.alt = this.options.image.alt || '';
      this.element.appendChild(imageElement);
    }
    
    // 创建头部（如果有标题或副标题）
    if (this.options.title || this.options.subtitle) {
      this.headerElement = document.createElement('div');
      this.headerElement.className = 'card-header';
      this.element.appendChild(this.headerElement);
    }
    
    // 创建主体
    this.bodyElement = document.createElement('div');
    this.bodyElement.className = 'card-body';
    this.element.appendChild(this.bodyElement);
    
    // 创建底部（如果有）
    if (this.options.footer) {
      this.footerElement = document.createElement('div');
      this.footerElement.className = 'card-footer';
      this.element.appendChild(this.footerElement);
    }
  }

  /**
   * 更新卡片内容
   */
  updateContent() {
    // 更新头部内容
    if (this.headerElement) {
      const headerContent = [];
      
      if (this.options.title || this.options.subtitle) {
        headerContent.push('<div class="card-header-content">');
        
        if (this.options.title) {
          headerContent.push(`<h3 class="card-header-title">${this.options.title}</h3>`);
        }
        
        if (this.options.subtitle) {
          headerContent.push(`<p class="card-header-subtitle">${this.options.subtitle}</p>`);
        }
        
        headerContent.push('</div>');
      }
      
      this.headerElement.innerHTML = headerContent.join('');
    }
    
    // 更新主体内容
    if (this.bodyElement) {
      if (typeof this.options.content === 'string') {
        this.bodyElement.innerHTML = this.options.content;
      } else if (this.options.content instanceof HTMLElement) {
        this.bodyElement.innerHTML = '';
        this.bodyElement.appendChild(this.options.content);
      }
    }
    
    // 更新底部内容
    if (this.footerElement) {
      if (typeof this.options.footer === 'string') {
        this.footerElement.innerHTML = this.options.footer;
      } else if (this.options.footer instanceof HTMLElement) {
        this.footerElement.innerHTML = '';
        this.footerElement.appendChild(this.options.footer);
      }
    }
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    if (this.options.clickable && this.options.onClick) {
      this.element.addEventListener('click', (e) => {
        if (!this.options.loading) {
          this.options.onClick(e, this);
        }
      });
      
      // 添加键盘支持
      this.element.setAttribute('tabindex', '0');
      this.element.setAttribute('role', 'button');
      
      this.element.addEventListener('keydown', (e) => {
        if ((e.key === 'Enter' || e.key === ' ') && !this.options.loading) {
          e.preventDefault();
          this.options.onClick(e, this);
        }
      });
    }
  }

  /**
   * 设置标题
   */
  setTitle(title) {
    this.options.title = title;
    
    if (!this.headerElement && title) {
      this.createStructure();
    }
    
    this.updateContent();
    return this;
  }

  /**
   * 设置副标题
   */
  setSubtitle(subtitle) {
    this.options.subtitle = subtitle;
    
    if (!this.headerElement && subtitle) {
      this.createStructure();
    }
    
    this.updateContent();
    return this;
  }

  /**
   * 设置内容
   */
  setContent(content) {
    this.options.content = content;
    this.updateContent();
    return this;
  }

  /**
   * 设置底部内容
   */
  setFooter(footer) {
    this.options.footer = footer;
    
    if (!this.footerElement && footer) {
      this.createStructure();
    } else if (this.footerElement && !footer) {
      this.footerElement.remove();
      this.footerElement = null;
    }
    
    this.updateContent();
    return this;
  }

  /**
   * 设置图片
   */
  setImage(image) {
    this.options.image = image;
    this.createStructure();
    this.updateContent();
    return this;
  }

  /**
   * 设置变体
   */
  setVariant(variant) {
    this.options.variant = variant;
    this.updateClasses();
    return this;
  }

  /**
   * 设置加载状态
   */
  setLoading(loading) {
    this.options.loading = loading;
    this.updateClasses();
    return this;
  }

  /**
   * 设置可点击状态
   */
  setClickable(clickable, onClick = null) {
    this.options.clickable = clickable;
    if (onClick) {
      this.options.onClick = onClick;
    }
    this.updateClasses();
    this.bindEvents();
    return this;
  }

  /**
   * 添加CSS类
   */
  addClass(className) {
    this.element.classList.add(className);
    return this;
  }

  /**
   * 移除CSS类
   */
  removeClass(className) {
    this.element.classList.remove(className);
    return this;
  }

  /**
   * 获取头部元素
   */
  getHeader() {
    return this.headerElement;
  }

  /**
   * 获取主体元素
   */
  getBody() {
    return this.bodyElement;
  }

  /**
   * 获取底部元素
   */
  getFooter() {
    return this.footerElement;
  }

  /**
   * 获取DOM元素
   */
  getElement() {
    return this.element;
  }

  /**
   * 挂载到指定容器
   */
  mount(container) {
    if (typeof container === 'string') {
      container = document.querySelector(container);
    }
    
    if (container && this.element) {
      container.appendChild(this.element);
    }
    
    return this;
  }

  /**
   * 从DOM中移除
   */
  unmount() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }
    return this;
  }

  /**
   * 销毁组件
   */
  destroy() {
    if (this.isDestroyed) return;
    
    this.unmount();
    this.element = null;
    this.headerElement = null;
    this.bodyElement = null;
    this.footerElement = null;
    this.options = null;
    this.isDestroyed = true;
  }
}

/**
 * 统计卡片组件
 */
export class StatsCard extends Card {
  constructor(options = {}) {
    const statsOptions = {
      value: 0,
      label: '',
      change: null,
      changeType: 'positive', // positive, negative, neutral
      ...options,
      className: `card-stats ${options.className || ''}`.trim()
    };

    super(statsOptions);
    this.updateStatsContent();
  }

  /**
   * 更新统计内容
   */
  updateStatsContent() {
    const content = [];
    
    content.push(`<div class="card-stats-value">${this.options.value}</div>`);
    content.push(`<div class="card-stats-label">${this.options.label}</div>`);
    
    if (this.options.change !== null) {
      const changeClass = `card-stats-change ${this.options.changeType}`;
      content.push(`<div class="${changeClass}">${this.options.change}</div>`);
    }
    
    this.setContent(content.join(''));
  }

  /**
   * 设置数值
   */
  setValue(value) {
    this.options.value = value;
    this.updateStatsContent();
    return this;
  }

  /**
   * 设置标签
   */
  setLabel(label) {
    this.options.label = label;
    this.updateStatsContent();
    return this;
  }

  /**
   * 设置变化
   */
  setChange(change, type = 'positive') {
    this.options.change = change;
    this.options.changeType = type;
    this.updateStatsContent();
    return this;
  }
}

// 工厂函数
export function createCard(options) {
  return new Card(options);
}

export function createStatsCard(options) {
  return new StatsCard(options);
}
