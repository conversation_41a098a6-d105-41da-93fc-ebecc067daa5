/**
 * Modal 模态框组件
 * 提供模态框、确认对话框、警告框等功能
 */

class Modal {
  constructor(options = {}) {
    this.options = {
      size: 'md', // sm, md, lg, xl, full
      closable: true, // 是否可关闭
      closeOnBackdrop: true, // 点击背景关闭
      closeOnEscape: true, // ESC键关闭
      showCloseButton: true, // 显示关闭按钮
      backdrop: true, // 显示背景遮罩
      keyboard: true, // 键盘导航支持
      focus: true, // 自动聚焦
      ...options
    };

    this.isOpen = false;
    this.element = null;
    this.backdrop = null;
    this.focusedElementBeforeModal = null;
    this.onClose = null;
    this.onConfirm = null;

    // 绑定方法上下文
    this.handleKeydown = this.handleKeydown.bind(this);
    this.handleBackdropClick = this.handleBackdropClick.bind(this);
    this.close = this.close.bind(this);
  }

  /**
   * 创建模态框
   */
  create(content, options = {}) {
    const config = { ...this.options, ...options };
    
    // 创建背景遮罩
    if (config.backdrop) {
      this.backdrop = document.createElement('div');
      this.backdrop.className = 'modal-backdrop';
      this.backdrop.setAttribute('aria-hidden', 'true');
    }

    // 创建模态框
    this.element = document.createElement('div');
    this.element.className = `modal modal-${config.size}`;
    this.element.setAttribute('role', 'dialog');
    this.element.setAttribute('aria-modal', 'true');
    this.element.setAttribute('tabindex', '-1');

    // 设置内容
    if (typeof content === 'string') {
      this.element.innerHTML = content;
    } else if (content instanceof HTMLElement) {
      this.element.appendChild(content);
    }

    // 绑定事件
    this.bindEvents(config);

    return this;
  }

  /**
   * 绑定事件
   */
  bindEvents(config) {
    // 背景点击关闭
    if (this.backdrop && config.closeOnBackdrop) {
      this.backdrop.addEventListener('click', this.handleBackdropClick);
    }

    // 关闭按钮
    if (config.showCloseButton) {
      const closeButtons = this.element.querySelectorAll('.modal-close');
      closeButtons.forEach(btn => {
        btn.addEventListener('click', this.close);
      });
    }

    // 键盘事件
    if (config.keyboard) {
      document.addEventListener('keydown', this.handleKeydown);
    }
  }

  /**
   * 显示模态框
   */
  show() {
    if (this.isOpen) return this;

    // 保存当前焦点元素
    this.focusedElementBeforeModal = document.activeElement;

    // 添加到页面
    if (this.backdrop) {
      document.body.appendChild(this.backdrop);
    }
    document.body.appendChild(this.element);

    // 防止背景滚动
    document.body.classList.add('modal-open');

    // 触发显示动画
    requestAnimationFrame(() => {
      if (this.backdrop) {
        this.backdrop.classList.add('active');
      }
      this.element.classList.add('active');
    });

    // 设置焦点
    if (this.options.focus) {
      this.setFocus();
    }

    this.isOpen = true;
    return this;
  }

  /**
   * 隐藏模态框
   */
  hide() {
    if (!this.isOpen) return this;

    // 移除激活状态
    if (this.backdrop) {
      this.backdrop.classList.remove('active');
    }
    this.element.classList.remove('active');

    // 动画完成后移除元素
    setTimeout(() => {
      this.destroy();
    }, 250); // 与 CSS 动画时长匹配

    this.isOpen = false;
    return this;
  }

  /**
   * 关闭模态框
   */
  close() {
    if (this.onClose && typeof this.onClose === 'function') {
      const result = this.onClose();
      if (result === false) return; // 阻止关闭
    }
    this.hide();
  }

  /**
   * 销毁模态框
   */
  destroy() {
    // 移除事件监听器
    document.removeEventListener('keydown', this.handleKeydown);

    // 移除元素
    if (this.backdrop && this.backdrop.parentNode) {
      this.backdrop.parentNode.removeChild(this.backdrop);
    }
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
    }

    // 恢复背景滚动
    document.body.classList.remove('modal-open');

    // 恢复焦点
    if (this.focusedElementBeforeModal) {
      this.focusedElementBeforeModal.focus();
    }

    // 清理引用
    this.element = null;
    this.backdrop = null;
    this.focusedElementBeforeModal = null;
  }

  /**
   * 设置焦点
   */
  setFocus() {
    // 查找可聚焦元素
    const focusableElements = this.element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    } else {
      this.element.focus();
    }
  }

  /**
   * 键盘事件处理
   */
  handleKeydown(e) {
    if (!this.isOpen) return;

    if (e.key === 'Escape' && this.options.closeOnEscape) {
      e.preventDefault();
      this.close();
    }

    // Tab 键循环焦点
    if (e.key === 'Tab') {
      this.handleTabKey(e);
    }
  }

  /**
   * Tab 键处理
   */
  handleTabKey(e) {
    const focusableElements = this.element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  }

  /**
   * 背景点击处理
   */
  handleBackdropClick(e) {
    if (e.target === this.backdrop) {
      this.close();
    }
  }

  /**
   * 设置关闭回调
   */
  onCloseCallback(callback) {
    this.onClose = callback;
    return this;
  }

  /**
   * 设置确认回调
   */
  onConfirmCallback(callback) {
    this.onConfirm = callback;
    return this;
  }
}

/**
 * Modal 工厂类 - 提供便捷的静态方法
 */
class ModalFactory {
  /**
   * 显示确认对话框
   */
  static confirm(options = {}) {
    const config = {
      title: '确认',
      message: '您确定要执行此操作吗？',
      confirmText: '确认',
      cancelText: '取消',
      type: 'warning', // success, error, warning, info
      ...options
    };

    return new Promise((resolve) => {
      const modal = new Modal({
        size: 'sm',
        closeOnBackdrop: false,
        closeOnEscape: true
      });

      const content = `
        <div class="modal-content">
          <div class="modal-body modal-confirm">
            <div class="modal-confirm-icon ${config.type}">
              ${ModalFactory.getIcon(config.type)}
            </div>
            <h3 class="modal-confirm-title">${config.title}</h3>
            <p class="modal-confirm-message">${config.message}</p>
          </div>
          <div class="modal-footer modal-footer-center">
            <button type="button" class="btn btn-secondary modal-cancel">${config.cancelText}</button>
            <button type="button" class="btn btn-${config.type === 'error' ? 'danger' : 'primary'} modal-confirm-btn">${config.confirmText}</button>
          </div>
        </div>
      `;

      modal.create(content);

      // 绑定按钮事件
      const confirmBtn = modal.element.querySelector('.modal-confirm-btn');
      const cancelBtn = modal.element.querySelector('.modal-cancel');

      confirmBtn.addEventListener('click', () => {
        resolve(true);
        modal.hide();
      });

      cancelBtn.addEventListener('click', () => {
        resolve(false);
        modal.hide();
      });

      modal.onCloseCallback(() => {
        resolve(false);
      });

      modal.show();
    });
  }

  /**
   * 显示警告框
   */
  static alert(options = {}) {
    const config = {
      title: '提示',
      message: '',
      buttonText: '确定',
      type: 'info', // success, error, warning, info
      ...options
    };

    return new Promise((resolve) => {
      const modal = new Modal({
        size: 'sm',
        closeOnBackdrop: true,
        closeOnEscape: true
      });

      const content = `
        <div class="modal-content">
          <div class="modal-body modal-confirm">
            <div class="modal-confirm-icon ${config.type}">
              ${ModalFactory.getIcon(config.type)}
            </div>
            <h3 class="modal-confirm-title">${config.title}</h3>
            <p class="modal-confirm-message">${config.message}</p>
          </div>
          <div class="modal-footer modal-footer-center">
            <button type="button" class="btn btn-primary modal-ok">${config.buttonText}</button>
          </div>
        </div>
      `;

      modal.create(content);

      // 绑定按钮事件
      const okBtn = modal.element.querySelector('.modal-ok');
      okBtn.addEventListener('click', () => {
        resolve(true);
        modal.hide();
      });

      modal.onCloseCallback(() => {
        resolve(true);
      });

      modal.show();
    });
  }

  /**
   * 获取图标
   */
  static getIcon(type) {
    const icons = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    };
    return icons[type] || icons.info;
  }
}

// 导出类
export { Modal, ModalFactory };
export default ModalFactory;
