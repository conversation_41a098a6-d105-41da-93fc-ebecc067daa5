/**
 * Toast 通知组件
 * 提供轻量级的通知消息功能
 */

class Toast {
  constructor(options = {}) {
    this.options = {
      position: 'top-right', // top-left, top-center, top-right, bottom-left, bottom-center, bottom-right
      duration: 4000, // 显示时长（毫秒），0 表示不自动关闭
      closable: true, // 是否显示关闭按钮
      showProgress: true, // 是否显示进度条
      pauseOnHover: true, // 鼠标悬停时暂停自动关闭
      maxToasts: 5, // 最大同时显示的 toast 数量
      ...options
    };

    this.toasts = new Map(); // 存储所有 toast 实例
    this.container = null;
    this.toastId = 0;

    this.init();
  }

  /**
   * 初始化 Toast 系统
   */
  init() {
    this.createContainer();
    this.bindEvents();
  }

  /**
   * 创建 Toast 容器
   */
  createContainer() {
    // 检查是否已存在容器
    let existingContainer = document.querySelector('.toast-container');
    if (existingContainer) {
      this.container = existingContainer;
      return;
    }

    this.container = document.createElement('div');
    this.container.className = `toast-container ${this.options.position}`;
    this.container.setAttribute('aria-live', 'polite');
    this.container.setAttribute('aria-label', '通知消息');
    document.body.appendChild(this.container);
  }

  /**
   * 绑定全局事件
   */
  bindEvents() {
    // 键盘事件处理
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.clearAll();
      }
    });
  }

  /**
   * 显示 Toast 通知
   */
  show(message, type = 'info', options = {}) {
    const config = { ...this.options, ...options };
    const toastId = ++this.toastId;

    // 检查最大数量限制
    if (this.toasts.size >= config.maxToasts) {
      const oldestToast = this.toasts.values().next().value;
      this.hide(oldestToast.id);
    }

    const toast = this.createToast(toastId, message, type, config);
    this.toasts.set(toastId, toast);

    // 添加到容器
    this.container.appendChild(toast.element);

    // 触发显示动画
    requestAnimationFrame(() => {
      toast.element.classList.add('show');
    });

    // 设置自动关闭
    if (config.duration > 0) {
      this.setAutoClose(toast, config.duration);
    }

    return toastId;
  }

  /**
   * 创建 Toast 元素
   */
  createToast(id, message, type, config) {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');

    // 图标映射
    const icons = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    };

    // 构建 HTML
    toast.innerHTML = `
      <div class="toast-icon">${icons[type] || icons.info}</div>
      <div class="toast-content">
        <div class="toast-message">${this.escapeHtml(message)}</div>
      </div>
      ${config.closable ? '<button class="toast-close" aria-label="关闭通知">×</button>' : ''}
      ${config.showProgress && config.duration > 0 ? '<div class="toast-progress"><div class="toast-progress-bar"></div></div>' : ''}
    `;

    const toastData = {
      id,
      element: toast,
      type,
      message,
      config,
      timer: null,
      startTime: null,
      remainingTime: config.duration
    };

    // 绑定事件
    this.bindToastEvents(toastData);

    return toastData;
  }

  /**
   * 绑定 Toast 事件
   */
  bindToastEvents(toast) {
    const { element, config } = toast;

    // 关闭按钮事件
    const closeBtn = element.querySelector('.toast-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hide(toast.id);
      });
    }

    // 鼠标悬停暂停
    if (config.pauseOnHover && config.duration > 0) {
      element.addEventListener('mouseenter', () => {
        this.pauseAutoClose(toast);
      });

      element.addEventListener('mouseleave', () => {
        this.resumeAutoClose(toast);
      });
    }

    // 点击整个 toast 关闭（可选）
    if (config.clickToClose) {
      element.addEventListener('click', () => {
        this.hide(toast.id);
      });
    }
  }

  /**
   * 设置自动关闭
   */
  setAutoClose(toast, duration) {
    toast.startTime = Date.now();
    toast.remainingTime = duration;

    // 设置进度条动画
    const progressBar = toast.element.querySelector('.toast-progress-bar');
    if (progressBar) {
      progressBar.style.animationDuration = `${duration}ms`;
    }

    toast.timer = setTimeout(() => {
      this.hide(toast.id);
    }, duration);
  }

  /**
   * 暂停自动关闭
   */
  pauseAutoClose(toast) {
    if (toast.timer) {
      clearTimeout(toast.timer);
      toast.remainingTime = toast.remainingTime - (Date.now() - toast.startTime);

      // 暂停进度条动画
      const progressBar = toast.element.querySelector('.toast-progress-bar');
      if (progressBar) {
        progressBar.style.animationPlayState = 'paused';
      }
    }
  }

  /**
   * 恢复自动关闭
   */
  resumeAutoClose(toast) {
    if (toast.remainingTime > 0) {
      toast.startTime = Date.now();

      // 恢复进度条动画
      const progressBar = toast.element.querySelector('.toast-progress-bar');
      if (progressBar) {
        progressBar.style.animationPlayState = 'running';
        progressBar.style.animationDuration = `${toast.remainingTime}ms`;
      }

      toast.timer = setTimeout(() => {
        this.hide(toast.id);
      }, toast.remainingTime);
    }
  }

  /**
   * 隐藏指定的 Toast
   */
  hide(toastId) {
    const toast = this.toasts.get(toastId);
    if (!toast) return;

    // 清除定时器
    if (toast.timer) {
      clearTimeout(toast.timer);
    }

    // 添加隐藏动画
    toast.element.classList.add('hide');
    toast.element.classList.remove('show');

    // 动画完成后移除元素
    setTimeout(() => {
      if (toast.element.parentNode) {
        toast.element.parentNode.removeChild(toast.element);
      }
      this.toasts.delete(toastId);
    }, 250); // 与 CSS 动画时长匹配
  }

  /**
   * 清除所有 Toast
   */
  clearAll() {
    this.toasts.forEach((toast) => {
      this.hide(toast.id);
    });
  }

  /**
   * 便捷方法
   */
  success(message, options = {}) {
    return this.show(message, 'success', options);
  }

  error(message, options = {}) {
    return this.show(message, 'error', options);
  }

  warning(message, options = {}) {
    return this.show(message, 'warning', options);
  }

  info(message, options = {}) {
    return this.show(message, 'info', options);
  }

  /**
   * HTML 转义
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 更新配置
   */
  updateConfig(newOptions) {
    this.options = { ...this.options, ...newOptions };
    
    // 更新容器位置
    if (newOptions.position && this.container) {
      this.container.className = `toast-container ${this.options.position}`;
    }
  }

  /**
   * 销毁 Toast 系统
   */
  destroy() {
    this.clearAll();
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    this.toasts.clear();
  }
}

// 创建全局实例
const toast = new Toast();

// 导出类和实例
export { Toast };
export default toast;
