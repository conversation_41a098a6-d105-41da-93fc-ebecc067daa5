/**
 * 证书管理页面模块
 * 处理CSR生成和CER转P12功能
 */

import api from '../core/api.js';
import { createFormField } from '../components/Form.js';
import { createButton } from '../components/Button.js';
import { formatDateTime, downloadFile, formatFileSize } from '../core/utils.js';

class CertificatePage {
  constructor() {
    this.recordsContainer = null;
    this.isInitialized = false;
    this.currentPage = 1;
    this.currentPageSize = 10;

    this.init();
  }

  /**
   * 初始化页面
   */
  init() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  /**
   * 设置页面
   */
  setup() {
    this.recordsContainer = document.getElementById('cert-records-container');

    if (!this.recordsContainer) {
      console.error('Certificate page containers not found');
      return;
    }

    this.setupEventListeners();

    // 设置全局引用，用于分页按钮调用
    window.certificatePage = this;

    this.isInitialized = true;

    // 监听路由变化
    window.addEventListener('router:navigated', (e) => {
      if (e.detail.route.path === '/certificate') {
        this.loadRecords();
      }
    });
  }

  /**
   * 显示CSR生成弹窗
   */
  showCSRModal() {
    // 创建模态框背景
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop active';

    // 创建模态框容器
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-content" style="max-width: 700px; width: 90vw;">
        <div class="modal-header">
          <h3 class="modal-title">📋 CSR生成</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div id="csr-form-container">
            <!-- CSR表单将在这里生成 -->
          </div>
        </div>
      </div>
    `;

    // 添加到页面
    document.body.appendChild(backdrop);
    document.body.appendChild(modal);

    // 确保正确的层级
    backdrop.style.zIndex = '1040';
    modal.style.zIndex = '1050';

    // 创建CSR表单
    this.createCSRForm(modal.querySelector('#csr-form-container'));

    // 绑定关闭事件
    const closeModal = () => {
      backdrop.remove();
      modal.remove();
      document.removeEventListener('keydown', handleEsc);
    };

    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    backdrop.addEventListener('click', closeModal);

    // ESC键关闭
    const handleEsc = (e) => {
      if (e.key === 'Escape') {
        closeModal();
      }
    };
    document.addEventListener('keydown', handleEsc);

    // 保存模态框引用
    this.currentCSRModal = { backdrop, modal, closeModal };
  }

  /**
   * 显示CER转P12弹窗
   */
  showCERModal() {
    // 创建模态框背景
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop active';

    // 创建模态框容器
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-content" style="max-width: 600px; width: 90vw;">
        <div class="modal-header">
          <h3 class="modal-title">🔄 CER转P12</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div id="cer-converter-container">
            <!-- CER转换表单将在这里生成 -->
          </div>
        </div>
      </div>
    `;

    // 添加到页面
    document.body.appendChild(backdrop);
    document.body.appendChild(modal);

    // 确保正确的层级
    backdrop.style.zIndex = '1040';
    modal.style.zIndex = '1050';

    // 创建CER表单
    this.createCERForm(modal.querySelector('#cer-converter-container'));

    // 绑定关闭事件
    const closeModal = () => {
      backdrop.remove();
      modal.remove();
      document.removeEventListener('keydown', handleEsc);
    };

    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    backdrop.addEventListener('click', closeModal);

    // ESC键关闭
    const handleEsc = (e) => {
      if (e.key === 'Escape') {
        closeModal();
      }
    };
    document.addEventListener('keydown', handleEsc);

    // 保存模态框引用
    this.currentCERModal = { backdrop, modal, closeModal };
  }

  /**
   * 显示转换指南弹窗
   */
  async showConversionGuide() {
    try {
      const response = await api.get('/api/certificates/cer/guide');

      if (response.data.success) {
        const guide = response.data.guide;

        // 创建模态框背景
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop active';

        // 创建模态框容器
        const modal = document.createElement('div');
        modal.className = 'modal active';
        modal.innerHTML = `
          <div class="modal-content" style="max-width: 800px; width: 90vw;">
            <div class="modal-header">
              <h3 class="modal-title">📖 ${guide.title}</h3>
              <button class="modal-close">×</button>
            </div>
            <div class="modal-body">
              <div class="conversion-guide">
                <div class="guide-steps mb-6">
                  <h4 class="text-lg font-medium mb-4">操作步骤</h4>
                  ${guide.steps.map(step => `
                    <div class="step-item mb-4 p-4 bg-gray-50 rounded-lg">
                      <div class="flex items-start gap-3">
                        <div class="step-number bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                          ${step.step}
                        </div>
                        <div class="flex-1">
                          <h5 class="font-medium text-primary mb-2">${step.title}</h5>
                          <p class="text-sm text-secondary mb-2">${step.description}</p>
                          <p class="text-xs text-muted">${step.note}</p>
                        </div>
                      </div>
                    </div>
                  `).join('')}
                </div>

                <div class="guide-requirements mb-6">
                  <h4 class="text-lg font-medium mb-4">使用要求</h4>
                  <ul class="list-disc list-inside space-y-2 text-sm">
                    ${guide.requirements.map(req => `<li>${req}</li>`).join('')}
                  </ul>
                </div>

                <div class="guide-tips">
                  <h4 class="text-lg font-medium mb-4">重要提示</h4>
                  <ul class="list-disc list-inside space-y-2 text-sm">
                    ${guide.tips.map(tip => `<li>${tip}</li>`).join('')}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        `;

        // 添加到页面
        document.body.appendChild(backdrop);
        document.body.appendChild(modal);

        // 确保正确的层级
        backdrop.style.zIndex = '1040';
        modal.style.zIndex = '1050';

        // 绑定关闭事件
        const closeModal = () => {
          backdrop.remove();
          modal.remove();
          document.removeEventListener('keydown', handleEsc);
        };

        modal.querySelector('.modal-close').addEventListener('click', closeModal);
        backdrop.addEventListener('click', closeModal);

        // ESC键关闭
        const handleEsc = (e) => {
          if (e.key === 'Escape') {
            closeModal();
          }
        };
        document.addEventListener('keydown', handleEsc);
      }
    } catch (error) {
      console.error('Failed to load conversion guide:', error);
      alert('❌ 加载转换指南失败');
    }
  }

  /**
   * 创建CSR生成表单
   */
  createCSRForm(container) {
    // 通用名称
    const commonNameField = createFormField({
      type: 'email',
      name: 'common_name',
      label: 'Common Name *',
      placeholder: '<EMAIL>',
      required: true,
      help: '通常使用您的邮箱地址'
    });

    // 组织名称
    const organizationField = createFormField({
      type: 'text',
      name: 'organization',
      label: '组织名称 *',
      placeholder: 'Your Company Name',
      required: true,
      help: '您的公司或组织名称'
    });

    // 组织单位
    const orgUnitField = createFormField({
      type: 'text',
      name: 'organizational_unit',
      label: '组织单位（可选）',
      placeholder: 'IT Department',
      help: '部门名称，可以留空'
    });

    // 国家代码
    const countryField = createFormField({
      type: 'select',
      name: 'country',
      label: '国家代码 *',
      value: '',
      required: true,
      options: [
        { value: '', label: '请选择国家' },
        { value: 'CN', label: '中国 (CN)' },
        { value: 'US', label: '美国 (US)' },
        { value: 'HK', label: '香港 (HK)' },
        { value: 'TW', label: '台湾 (TW)' },
        { value: 'JP', label: '日本 (JP)' },
        { value: 'KR', label: '韩国 (KR)' },
        { value: 'SG', label: '新加坡 (SG)' }
      ]
    });

    // 密钥长度（固定为2048位，不显示给用户）
    const keyLengthField = createFormField({
      type: 'hidden',
      name: 'key_length',
      value: '2048'
    });

    // 生成按钮
    const generateButton = createButton({
      text: '📋 生成CSR文件',
      type: 'accent',
      size: 'lg',
      block: true,
      onClick: () => this.generateCSR()
    });

    // 组装表单
    const csrFormHTML = `
      <div class="form">
        <div id="common-name-container"></div>
        <div id="organization-container"></div>
        <div id="org-unit-container"></div>
        <div id="country-container"></div>

        <div class="alert alert-info mt-4">
          <h5>💡 生成说明</h5>
          <ul class="mb-0">
            <li>密钥长度固定为2048位（推荐设置）</li>
            <li>生成的CSR文件用于向Apple申请证书</li>
            <li>私钥文件请妥善保管，用于后续证书转换</li>
            <li>Common Name通常使用您的邮箱地址</li>
          </ul>
        </div>

        <div class="form-actions">
          <div id="generate-csr-button-container"></div>
        </div>
      </div>
    `;

    container.innerHTML = csrFormHTML;

    // 挂载表单字段
    commonNameField.mount(container.querySelector('#common-name-container'));
    organizationField.mount(container.querySelector('#organization-container'));
    orgUnitField.mount(container.querySelector('#org-unit-container'));
    countryField.mount(container.querySelector('#country-container'));

    // 挂载隐藏的密钥长度字段到表单容器
    keyLengthField.mount(container.querySelector('.form'));

    generateButton.mount(container.querySelector('#generate-csr-button-container'));

    // 保存字段引用
    this.csrFields = {
      commonName: commonNameField,
      organization: organizationField,
      orgUnit: orgUnitField,
      country: countryField,
      keyLength: keyLengthField
    };

    this.generateCSRButton = generateButton;
  }

  /**
   * 创建CER转P12表单
   */
  createCERForm(container) {
    // CER文件上传
    const cerFileField = createFormField({
      type: 'file',
      name: 'cer_file',
      label: 'CER证书文件',
      accept: '.cer,.crt',
      required: true,
      help: '从Apple Developer下载的证书文件'
    });

    // 证书名称
    const certNameField = createFormField({
      type: 'text',
      name: 'certificate_name',
      label: '证书名称',
      placeholder: '输入证书名称',
      required: true,
      help: '为转换后的P12证书设置一个名称'
    });

    // 证书类型
    const certTypeField = createFormField({
      type: 'select',
      name: 'certificate_type',
      label: '证书类型',
      value: 'distribution',
      required: true,
      options: [
        { value: 'development', label: '开发证书 (Development)' },
        { value: 'distribution', label: '发布证书 (Distribution)' }
      ]
    });

    // P12密码
    const passwordField = createFormField({
      type: 'password',
      name: 'p12_password',
      label: 'P12密码',
      placeholder: '设置P12文件的密码（至少6位）',
      required: true,
      help: '用于保护P12文件的密码，至少6位字符'
    });

    // 转换按钮
    const convertButton = createButton({
      text: '🔄 转换为P12',
      type: 'pink',
      size: 'lg',
      block: true,
      disabled: true,
      onClick: () => this.convertToP12()
    });

    // 组装表单
    const cerFormHTML = `
      <div class="form">
        <div id="cer-file-container"></div>
        <div id="cert-name-container"></div>
        <div id="cert-type-container"></div>
        <div id="password-container"></div>

        <div class="alert alert-info mt-4">
          <h5>💡 智能转换说明</h5>
          <ul class="mb-0">
            <li>系统会自动匹配对应的CSR记录和私钥</li>
            <li>确保之前已通过本系统生成过对应的CSR</li>
            <li>证书的Common Name应与CSR记录匹配</li>
            <li>如果自动匹配失败，系统会提供手动选择选项</li>
          </ul>
        </div>

        <div class="form-actions">
          <div id="convert-button-container"></div>
        </div>
      </div>
    `;

    container.innerHTML = cerFormHTML;

    // 挂载表单字段
    cerFileField.mount(container.querySelector('#cer-file-container'));
    certNameField.mount(container.querySelector('#cert-name-container'));
    certTypeField.mount(container.querySelector('#cert-type-container'));
    passwordField.mount(container.querySelector('#password-container'));
    convertButton.mount(container.querySelector('#convert-button-container'));

    // 保存字段引用
    this.cerFields = {
      cerFile: cerFileField,
      certName: certNameField,
      certType: certTypeField,
      password: passwordField
    };

    this.convertButton = convertButton;

    // 监听文件选择变化
    setTimeout(() => {
      const fileInput = container.querySelector('input[name="cer_file"]');
      if (fileInput) {
        fileInput.addEventListener('change', (e) => {
          this.handleCERFileChange(e.target.files[0]);
        });
      }

      // 监听其他字段变化
      const inputs = container.querySelectorAll('input, select');
      inputs.forEach(input => {
        input.addEventListener('change', () => {
          this.validateCERForm();
        });
        input.addEventListener('input', () => {
          this.validateCERForm();
        });
      });
    }, 100);
  }

  /**
   * 处理CER文件变化
   */
  handleCERFileChange(file) {
    if (file) {
      // 自动填充证书名称
      const baseName = file.name.replace(/\.[^/.]+$/, "");
      this.cerFields.certName.setValue(baseName);
    }
    this.validateCERForm();
  }

  /**
   * 验证CER表单
   */
  validateCERForm() {
    if (!this.cerFields || !this.convertButton) return;

    const cerFiles = this.cerFields.cerFile.getValue();
    const certName = this.cerFields.certName.getValue();
    const password = this.cerFields.password.getValue();

    const hasFile = cerFiles && cerFiles.length > 0;
    const hasName = certName && certName.trim() !== '';
    const hasPassword = password && password.trim().length >= 6;

    this.convertButton.setDisabled(!(hasFile && hasName && hasPassword));
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 刷新记录按钮
    const refreshBtn = document.getElementById('refresh-cert-records-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadRecords());
    }

    // CSR生成按钮
    const csrBtn = document.getElementById('generate-csr-btn');
    if (csrBtn) {
      csrBtn.addEventListener('click', () => this.showCSRModal());
    }

    // CER转P12按钮
    const cerBtn = document.getElementById('convert-cer-btn');
    if (cerBtn) {
      cerBtn.addEventListener('click', () => this.showCERModal());
    }

    // 转换指南按钮
    const guideBtn = document.getElementById('conversion-guide-btn');
    if (guideBtn) {
      guideBtn.addEventListener('click', () => this.showConversionGuide());
    }
  }

  /**
   * 生成CSR文件
   */
  async generateCSR() {
    try {
      this.generateCSRButton.setLoading(true);

      const data = {
        common_name: this.csrFields.commonName.getValue(),
        organization: this.csrFields.organization.getValue(),
        organizational_unit: this.csrFields.orgUnit.getValue(),
        country: this.csrFields.country.getValue(),
        key_length: parseInt(this.csrFields.keyLength.getValue())
      };

      // 验证必填字段（根据后端要求）
      if (!data.common_name) {
        alert('❌ 请填写Common Name');
        return;
      }

      // 验证Common Name格式（必须是邮箱地址）
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.common_name)) {
        alert('❌ Common Name必须是有效的邮箱地址');
        return;
      }

      if (!data.organization) {
        alert('❌ 请填写组织名称');
        return;
      }

      if (!data.country) {
        alert('❌ 请选择国家代码');
        return;
      }

      // 调试信息
      console.log('Sending CSR data:', data);

      // 发送CSR生成请求
      const response = await api.post('/api/certificates/csr/generate', data);

      if (response.data.success) {
        // 下载CSR文件
        const csrContent = response.data.csr;
        const keyContent = response.data.private_key;

        // 下载CSR文件
        const csrBlob = new Blob([csrContent], { type: 'text/plain' });
        const csrUrl = URL.createObjectURL(csrBlob);
        downloadFile(csrUrl, `${data.common_name}.csr`);
        URL.revokeObjectURL(csrUrl);

        // 下载私钥文件
        const keyBlob = new Blob([keyContent], { type: 'text/plain' });
        const keyUrl = URL.createObjectURL(keyBlob);
        downloadFile(keyUrl, `${data.common_name}.key`);
        URL.revokeObjectURL(keyUrl);

        // 关闭弹窗并刷新记录
        setTimeout(() => {
          if (this.currentCSRModal) {
            this.currentCSRModal.closeModal();
          }
          this.loadRecords();
        }, 1500);
      } else {
        alert('❌ CSR生成失败：' + (response.data.message || '未知错误'));
      }

    } catch (error) {
      console.error('CSR generation failed:', error);
      let errorMessage = 'CSR生成失败';

      if (error.status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert('❌ ' + errorMessage);
    } finally {
      this.generateCSRButton.setLoading(false);
    }
  }

  /**
   * 转换CER为P12
   */
  async convertToP12() {
    try {
      this.convertButton.setLoading(true);

      const cerFiles = this.cerFields.cerFile.getValue();
      const certName = this.cerFields.certName.getValue();
      const certType = this.cerFields.certType.getValue();
      const password = this.cerFields.password.getValue();

      // 验证必填字段
      if (!cerFiles || cerFiles.length === 0) {
        alert('❌ 请选择CER证书文件');
        return;
      }

      if (!certName || certName.trim() === '') {
        alert('❌ 请输入证书名称');
        return;
      }

      if (!password || password.length < 6) {
        alert('❌ P12密码长度至少6位');
        return;
      }

      // 准备表单数据
      const formData = new FormData();
      formData.append('cer_file', cerFiles[0]);
      formData.append('certificate_name', certName);
      formData.append('certificate_type', certType);
      formData.append('password', password);

      // 发送转换请求
      const response = await api.upload('/api/certificates/cer/convert', formData);

      if (response.data.success) {
        // 下载P12文件
        const downloadUrl = response.data.download_url;

        if (downloadUrl) {
          // 如果返回下载链接，直接下载
          window.open(downloadUrl, '_blank');
        } else if (response.data.p12_content) {
          // 如果返回文件内容，创建下载
          const p12Content = atob(response.data.p12_content);
          const bytes = new Uint8Array(p12Content.length);
          for (let i = 0; i < p12Content.length; i++) {
            bytes[i] = p12Content.charCodeAt(i);
          }

          const blob = new Blob([bytes], { type: 'application/x-pkcs12' });
          const url = URL.createObjectURL(blob);
          downloadFile(url, response.data.filename || 'certificate.p12');
          URL.revokeObjectURL(url);
        }

        // 关闭弹窗并刷新记录
        setTimeout(() => {
          if (this.currentCERModal) {
            this.currentCERModal.closeModal();
          }
          this.loadRecords();
        }, 1500);
      } else {
        alert('❌ P12转换失败：' + (response.data.message || '未知错误'));
      }

    } catch (error) {
      console.error('P12 conversion failed:', error);
      let errorMessage = 'P12转换失败';

      if (error.status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (error.status === 400) {
        errorMessage = '文件格式错误或密码不正确';
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert('❌ ' + errorMessage);
    } finally {
      this.convertButton.setLoading(false);
    }
  }



  /**
   * 加载证书记录（支持分页）
   */
  async loadRecords(page = 1, pageSize = 10) {
    if (!this.recordsContainer) return;

    try {
      // 保存当前分页信息
      this.currentPage = page;
      this.currentPageSize = pageSize;

      // 只在第一页时显示加载中，其他页面显示在分页区域
      if (page === 1) {
        this.recordsContainer.innerHTML = '<div class="text-center py-8">加载中...</div>';
      } else {
        this.showPaginationLoading();
      }

      // 构建请求URL
      let url = '/api/certificates/cer/records';
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('page_size', pageSize.toString());
      url += '?' + params.toString();

      const response = await api.get(url);

      if (response.data.success) {
        const { records, pagination } = response.data;

        if (records && records.length > 0) {
          this.renderRecords(records);
          this.renderPagination(pagination);
        } else if (page === 1) {
          this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无证书记录</div>';
          this.clearPagination();
        }
      } else {
        if (page === 1) {
          this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无证书记录</div>';
        }
        this.clearPagination();
      }
    } catch (error) {
      console.error('Failed to load certificate records:', error);

      if (error.status === 401) {
        this.recordsContainer.innerHTML = '<div class="text-center py-8 text-error">认证失败，请重新登录</div>';
      } else {
        this.recordsContainer.innerHTML = '<div class="text-center py-8 text-error">加载失败，请稍后重试</div>';
      }
      this.clearPagination();
    }
  }

  /**
   * 显示分页加载状态
   */
  showPaginationLoading() {
    const paginationContainer = this.getPaginationContainer();
    if (paginationContainer) {
      paginationContainer.innerHTML = '<div class="text-center py-2 text-muted">加载中...</div>';
    }
  }

  /**
   * 清除分页
   */
  clearPagination() {
    const paginationContainer = this.getPaginationContainer();
    if (paginationContainer) {
      paginationContainer.innerHTML = '';
    }
  }

  /**
   * 获取或创建分页容器
   */
  getPaginationContainer() {
    let container = document.getElementById('cert-records-pagination');
    if (!container) {
      container = document.createElement('div');
      container.id = 'cert-records-pagination';
      container.className = 'pagination-container mt-6';

      // 插入到记录容器后面
      if (this.recordsContainer && this.recordsContainer.parentNode) {
        this.recordsContainer.parentNode.insertBefore(container, this.recordsContainer.nextSibling);
      }
    }
    return container;
  }

  /**
   * 渲染分页组件
   */
  renderPagination(pagination) {
    if (!pagination) return;

    const container = this.getPaginationContainer();
    const { current_page, total_pages, total_count, page_size } = pagination;

    if (total_pages <= 1) {
      container.innerHTML = '';
      return;
    }

    // 计算显示的页码范围
    const maxVisiblePages = 5;
    let startPage = Math.max(1, current_page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(total_pages, startPage + maxVisiblePages - 1);

    // 调整起始页
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    let paginationHTML = `
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="text-muted">共 ${total_count} 条记录</span>
          <select class="page-size-selector" onchange="certificatePage.changePageSize(this.value)">
            <option value="10" ${page_size === 10 ? 'selected' : ''}>每页 10 条</option>
            <option value="20" ${page_size === 20 ? 'selected' : ''}>每页 20 条</option>
            <option value="50" ${page_size === 50 ? 'selected' : ''}>每页 50 条</option>
          </select>
        </div>
        <div class="pagination-controls">
    `;

    // 上一页按钮
    if (current_page > 1) {
      paginationHTML += `
        <button class="pagination-btn" onclick="certificatePage.loadRecords(${current_page - 1})">
          <span>‹ 上一页</span>
        </button>
      `;
    }

    // 第一页
    if (startPage > 1) {
      paginationHTML += `
        <button class="pagination-btn" onclick="certificatePage.loadRecords(1)">1</button>
      `;
      if (startPage > 2) {
        paginationHTML += '<span class="pagination-ellipsis">...</span>';
      }
    }

    // 页码按钮
    for (let i = startPage; i <= endPage; i++) {
      const isActive = i === current_page;
      paginationHTML += `
        <button class="pagination-btn ${isActive ? 'active' : ''}"
                onclick="certificatePage.loadRecords(${i})"
                ${isActive ? 'disabled' : ''}>
          ${i}
        </button>
      `;
    }

    // 最后一页
    if (endPage < total_pages) {
      if (endPage < total_pages - 1) {
        paginationHTML += '<span class="pagination-ellipsis">...</span>';
      }
      paginationHTML += `
        <button class="pagination-btn" onclick="certificatePage.loadRecords(${total_pages})">
          ${total_pages}
        </button>
      `;
    }

    // 下一页按钮
    if (current_page < total_pages) {
      paginationHTML += `
        <button class="pagination-btn" onclick="certificatePage.loadRecords(${current_page + 1})">
          <span>下一页 ›</span>
        </button>
      `;
    }

    paginationHTML += `
        </div>
      </div>
    `;

    container.innerHTML = paginationHTML;
  }

  /**
   * 改变每页显示数量
   */
  changePageSize(newPageSize) {
    this.loadRecords(1, parseInt(newPageSize));
  }

  /**
   * 渲染证书记录
   */
  renderRecords(records) {
    if (!records || records.length === 0) {
      this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无证书记录</div>';
      return;
    }
    
    const recordsHTML = records.map(record => {
      // 处理转换类型
      const conversionType = record.conversion_type || 'unknown';
      const isCSR = conversionType.includes('csr') || record.source_file_type === 'csr';
      const isCER = conversionType.includes('cer') || record.source_file_type === 'cer';

      // 确定显示类型
      let displayType = 'UNKNOWN';
      let badgeType = 'secondary';
      let actionText = '未知操作';

      if (isCSR) {
        displayType = 'CSR';
        badgeType = 'primary';
        actionText = 'CSR生成';
      } else if (isCER || conversionType === 'cer_to_p12') {
        displayType = 'P12';
        badgeType = 'success';
        actionText = 'CER转P12';
      }

      return `
        <div class="card mb-4">
          <div class="card-body">
            <div class="flex justify-between items-start mb-3">
              <div>
                <h4 class="font-medium">${record.certificate_name || record.source_file_name || record.target_file_name || '未知证书'}</h4>
                <p class="text-sm text-secondary">${actionText}</p>
              </div>
              <span class="badge badge-${badgeType}">${displayType}</span>
            </div>

            <div class="grid grid-cols-2 gap-4 text-sm mb-3">
              ${record.certificate_type ? `<div><strong>证书类型:</strong> ${record.certificate_type === 'development' ? '开发证书' : '发布证书'}</div>` : ''}
              ${record.source_csr_name ? `<div><strong>源CSR:</strong> ${record.source_csr_name}</div>` : ''}
              <div><strong>创建时间:</strong> ${formatDateTime(record.created_at)}</div>
              <div><strong>状态:</strong> <span class="badge badge-${record.status === 'completed' ? 'success' : 'danger'}">${record.status === 'completed' ? '已完成' : '失败'}</span></div>
              ${record.conversion_method ? `<div><strong>转换方式:</strong> ${record.conversion_method === 'auto' ? '自动匹配' : '手动选择'}</div>` : ''}
              ${record.file_size ? `<div><strong>文件大小:</strong> ${formatFileSize(record.file_size)}</div>` : ''}
            </div>

            ${record.target_file_path ? `
              <div class="mt-4">
                <button class="btn btn-primary btn-sm" onclick="certificatePage.downloadFile('${record._id}', '${record.target_file_name || 'certificate'}')">
                  📥 下载文件
                </button>
              </div>
            ` : ''}

            ${record.error_message ? `
              <div class="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                <p class="text-sm text-red-600"><strong>错误信息:</strong> ${record.error_message}</p>
              </div>
            ` : ''}
          </div>
        </div>
      `;
    }).join('');
    
    this.recordsContainer.innerHTML = recordsHTML;
  }

  /**
   * 下载证书文件
   */
  downloadFile(recordId, filename) {
    const downloadUrl = `/api/certificates/cer/${recordId}/download`;

    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// 创建页面实例
new CertificatePage();
