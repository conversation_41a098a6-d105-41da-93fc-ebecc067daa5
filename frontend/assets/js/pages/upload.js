/**
 * 上传页面模块
 * 处理IPA文件上传和记录管理
 */

import api from '../core/api.js';
import auth from '../core/auth.js';
import { createFormField } from '../components/Form.js';
import { formatFileSize, formatDateTime } from '../core/utils.js';
import notifications from '../core/notifications.js';

class UploadPage {
  constructor() {
    this.formContainer = null;
    this.recordsContainer = null;
    this.uploadForm = null;
    this.currentFile = null;
    this.isInitialized = false;
    
    this.init();
  }

  /**
   * 初始化页面
   */
  init() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  /**
   * 设置页面
   */
  setup() {
    this.recordsContainer = document.getElementById('upload-records-container');

    if (!this.recordsContainer) {
      return;
    }

    this.setupEventListeners();

    // 设置全局引用，用于分页按钮调用
    window.uploadPage = this;

    this.isInitialized = true;

    // 监听路由变化，当进入上传页面时加载记录
    window.addEventListener('router:navigated', (e) => {
      if (e.detail.route.path === '/upload' || e.detail.route.path === '/') {
        this.loadRecords();
      }
    });
  }

  /**
   * 显示上传弹窗
   */
  showUploadModal() {
    // 创建模态框背景
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop active';

    // 创建模态框容器
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-content" style="max-width: 1000px; width: 90vw;">
        <div class="modal-header">
          <h3 class="modal-title">📤 添加上传任务</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <!-- 步骤指示器 -->
          <div class="upload-steps-indicator mb-6">
            <div class="steps-container">
              <div class="step active" data-step="1">
                <div class="step-number">1</div>
                <div class="step-label">选择文件</div>
              </div>
              <div class="step-divider"></div>
              <div class="step" data-step="2">
                <div class="step-number">2</div>
                <div class="step-label">认证配置</div>
              </div>
              <div class="step-divider"></div>
              <div class="step" data-step="3">
                <div class="step-number">3</div>
                <div class="step-label">上传到TestFlight</div>
              </div>
            </div>
          </div>

          <!-- 步骤内容 -->
          <div class="upload-step-content">
            <!-- 步骤1：文件选择 -->
            <div class="step-panel active" id="step-1-panel">
              <div class="step-header">
                <h3 class="step-title">📱 选择IPA文件</h3>
                <p class="step-subtitle">支持拖拽上传，文件大小最大2GB</p>
              </div>
              <div class="file-selection-container">
                <div id="modal-upload-form-container">
                  <!-- 只包含文件选择区域 -->
                </div>
                <div class="file-info-display" id="modal-file-info" style="display: none;">
                  <!-- 文件信息展示 -->
                </div>
              </div>
            </div>

            <!-- 步骤2：认证配置 -->
            <div class="step-panel" id="step-2-panel">
              <div class="step-header">
                <h3 class="step-title">🔑 配置认证信息</h3>
                <p class="step-subtitle">选择认证方式并填写相关信息</p>
              </div>
              <div class="auth-config-container">
                <!-- 认证方式选择 -->
                <div class="auth-method-section">
                  <h4 class="config-section-title">认证方式</h4>
                  <div class="auth-method-options" id="modal-auth-method-container">
                    <!-- 认证方式选项 -->
                  </div>
                </div>

                <!-- 认证信息配置 -->
                <div class="auth-fields-section">
                  <h4 class="config-section-title">认证信息</h4>
                  <div id="modal-auth-fields-container">
                    <!-- 认证字段 -->
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤3：开始上传到TestFlight -->
            <div class="step-panel" id="step-3-panel">
              <div class="step-header">
                <h3 class="step-title">🚀 开始上传到TestFlight</h3>
                <p class="step-subtitle">确认信息并开始上传</p>
              </div>

              <!-- 上传信息摘要 -->
              <div class="upload-summary-section">
                <h4 class="config-section-title">上传信息确认</h4>
                <div class="upload-summary" id="upload-summary">
                  <!-- 上传信息摘要 -->
                </div>
              </div>

              <!-- 发布说明 -->
              <div class="release-notes-section">
                <h4 class="config-section-title">发布说明 <span class="optional-label">(可选)</span></h4>
                <div id="modal-release-notes-container">
                  <!-- 发布说明输入 -->
                </div>
              </div>
            </div>

            <!-- 上传进度 -->
            <div class="step-panel" id="upload-progress-panel">
              <div class="step-header">
                <h3 class="step-title">� 上传进度</h3>
                <p class="step-subtitle">请耐心等待，不要关闭窗口</p>
              </div>
              <div id="modal-upload-progress-content">
                <!-- 详细的上传进度 -->
              </div>
            </div>
          </div>

          <!-- 步骤导航 -->
          <div class="step-navigation">
            <button class="btn btn-secondary" id="prev-step-btn" style="display: none;">
              ← 上一步
            </button>
            <button class="btn btn-primary" id="next-step-btn">
              下一步 →
            </button>
          </div>
        </div>
      </div>
    `;

    // 先添加背景，再添加模态框
    document.body.appendChild(backdrop);
    document.body.appendChild(modal);

    // 初始化步骤导航
    this.currentStep = 1;
    this.totalSteps = 3;

    // 创建表单
    this.createModalUploadForm();

    // 设置步骤导航
    this.setupStepNavigation();

    // 关闭模态框的函数
    const closeModal = () => {
      modal.remove();
      backdrop.remove();
    };

    // 点击背景关闭
    backdrop.addEventListener('click', closeModal);

    // 点击关闭按钮
    const closeBtn = modal.querySelector('.modal-close');
    if (closeBtn) {
      closeBtn.onclick = closeModal;
    }

    // ESC键关闭（上传过程中禁用）
    const handleEsc = (e) => {
      if (e.key === 'Escape' && !this.uploadInProgress) {
        closeModal();
        document.removeEventListener('keydown', handleEsc);
      }
    };
    document.addEventListener('keydown', handleEsc);

    // 保存ESC处理函数引用，用于后续移除
    this.modalEscHandler = handleEsc;
  }

  /**
   * 创建弹窗中的上传表单
   */
  createModalUploadForm() {
    // 第一步：只创建文件选择
    this.createFileSelectionStep();

    // 第二步：创建认证配置
    this.createAuthConfigStep();

    // 第三步：创建发布说明和上传按钮
    this.createUploadActionStep();

    // 最后绑定所有事件
    this.bindFileUploadEvents();
  }

  /**
   * 创建文件选择步骤
   */
  createFileSelectionStep() {
    const container = document.getElementById('modal-upload-form-container');
    if (!container) return;

    // 创建文件上传区域
    const fileUploadHTML = `
      <div class="file-upload-area" id="modal-file-upload-area">
        <div class="upload-placeholder">
          <div class="upload-icon">📱</div>
          <div class="upload-text">
            <h4>选择或拖拽IPA文件</h4>
            <p>支持.ipa格式，最大2GB</p>
            <small>点击此区域选择文件，或直接拖拽文件到此处</small>
          </div>
        </div>
        <input type="file" id="modal-ipa-file" accept=".ipa" style="display: none;">
      </div>
    `;

    container.innerHTML = fileUploadHTML;
  }

  /**
   * 创建认证配置步骤
   */
  createAuthConfigStep() {
    // 认证方式选择
    this.createAuthMethodSelection();

    // 认证字段
    this.createAuthFields();
  }

  /**
   * 创建认证方式选择
   */
  createAuthMethodSelection() {
    const container = document.getElementById('modal-auth-method-container');
    if (!container) return;

    const authMethodField = createFormField({
      type: 'radio',
      name: 'auth_method',
      label: '',
      value: 'api_key',
      required: true,
      options: [
        { value: 'api_key', label: 'API Key 认证（推荐）' },
        { value: 'apple_id', label: 'Apple ID 认证' }
      ]
    });

    container.appendChild(authMethodField.element);
    this.modalFormFields = { authMethod: authMethodField };
  }

  /**
   * 创建认证字段
   */
  createAuthFields() {
    const container = document.getElementById('modal-auth-fields-container');
    if (!container) return;

    // API Key配置字段
    const apiKeyIdField = createFormField({
      type: 'text',
      name: 'api_key_id',
      label: 'API Key ID',
      placeholder: '10位字符，如：ABC123DEFG',
      required: true,
      help: '在App Store Connect中创建的API Key ID'
    });

    const issuerIdField = createFormField({
      type: 'text',
      name: 'issuer_id',
      label: 'Issuer ID',
      placeholder: '36位UUID格式',
      required: true,
      help: '您的App Store Connect团队的Issuer ID'
    });

    const apiKeyContentField = createFormField({
      type: 'textarea',
      name: 'api_key_content',
      label: 'API Key 私钥内容',
      placeholder: '-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----',
      required: true,
      rows: 6,
      help: '完整的.p8私钥文件内容'
    });

    // Apple ID配置字段
    const appleIdField = createFormField({
      type: 'email',
      name: 'apple_id',
      label: 'Apple ID',
      placeholder: '<EMAIL>',
      required: true,
      help: '您的Apple开发者账号邮箱'
    });

    const appPasswordField = createFormField({
      type: 'password',
      name: 'app_password',
      label: '专用密码',
      placeholder: '16位专用密码',
      required: true,
      help: '在Apple ID设置中生成的专用密码'
    });

    // 创建认证字段HTML结构
    const authFieldsHTML = `
      <div id="modal-api-key-config" style="display: block;">
        <div class="auth-fields-grid" id="api-key-fields">
        </div>
      </div>
      <div id="modal-apple-id-config" style="display: none;">
        <div class="auth-fields-grid" id="apple-id-fields">
        </div>
      </div>
    `;

    container.innerHTML = authFieldsHTML;

    // 挂载字段到对应容器
    const apiKeyFieldsContainer = document.getElementById('api-key-fields');
    const appleIdFieldsContainer = document.getElementById('apple-id-fields');

    if (apiKeyFieldsContainer) {
      apiKeyFieldsContainer.appendChild(apiKeyIdField.element);
      apiKeyFieldsContainer.appendChild(issuerIdField.element);
      apiKeyFieldsContainer.appendChild(apiKeyContentField.element);
    }

    if (appleIdFieldsContainer) {
      appleIdFieldsContainer.appendChild(appleIdField.element);
      appleIdFieldsContainer.appendChild(appPasswordField.element);
    }

    // 保存字段引用
    this.modalFormFields = {
      ...this.modalFormFields,
      apiKeyId: apiKeyIdField,
      issuerId: issuerIdField,
      apiKeyContent: apiKeyContentField,
      appleId: appleIdField,
      appPassword: appPasswordField
    };
  }



  /**
   * 创建上传操作步骤
   */
  createUploadActionStep() {
    // 创建发布说明
    this.createReleaseNotesField();
  }

  /**
   * 创建发布说明字段
   */
  createReleaseNotesField() {
    const container = document.getElementById('modal-release-notes-container');
    if (!container) return;

    const releaseNotesField = createFormField({
      type: 'textarea',
      name: 'release_notes',
      label: '',
      placeholder: '输入本次更新的发布说明...',
      rows: 4,
      help: '发布说明将显示在TestFlight中，帮助测试人员了解本次更新内容'
    });

    container.appendChild(releaseNotesField.element);

    // 保存字段引用
    this.modalFormFields = {
      ...this.modalFormFields,
      releaseNotes: releaseNotesField
    };
  }



  /**
   * 绑定文件上传事件
   */
  bindFileUploadEvents() {
    // 立即检查元素是否存在
    const fileInput = document.getElementById('modal-ipa-file');
    const uploadArea = document.getElementById('modal-file-upload-area');

    if (!fileInput || !uploadArea) {
      // 尝试直接绑定事件
      this.bindFileEventsDirectly();
      return;
    }

    // 设置事件监听
    this.setupModalFormEvents();
  }

  /**
   * 直接绑定文件事件（备用方案）
   */
  bindFileEventsDirectly() {
    // 使用事件委托
    document.addEventListener('change', (e) => {
      if (e.target && e.target.id === 'modal-ipa-file') {
        if (e.target.files && e.target.files[0]) {
          this.handleModalFileSelect(e.target.files[0]);
        }
      }
    });

    document.addEventListener('click', (e) => {
      if (e.target && e.target.closest('#modal-file-upload-area')) {
        const fileInput = document.getElementById('modal-ipa-file');
        if (fileInput) {
          fileInput.click();
        }
      }
    });

    // 拖拽事件委托
    document.addEventListener('dragover', (e) => {
      if (e.target && e.target.closest('#modal-file-upload-area')) {
        e.preventDefault();
        e.stopPropagation();
        e.target.closest('#modal-file-upload-area').classList.add('dragover');
      }
    });

    document.addEventListener('drop', (e) => {
      if (e.target && e.target.closest('#modal-file-upload-area')) {
        e.preventDefault();
        e.stopPropagation();
        e.target.closest('#modal-file-upload-area').classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
          const file = files[0];
          if (file.name.toLowerCase().endsWith('.ipa')) {
            this.handleModalFileSelect(file);
          } else {
            alert('❌ 请选择.ipa格式的文件');
          }
        }
      }
    });

    document.addEventListener('dragleave', (e) => {
      if (e.target && e.target.closest('#modal-file-upload-area')) {
        e.preventDefault();
        e.stopPropagation();
        e.target.closest('#modal-file-upload-area').classList.remove('dragover');
      }
    });
  }

  /**
   * 设置弹窗表单事件
   */
  setupModalFormEvents() {
    // 立即设置事件，不使用延迟
    // 认证方式切换事件
    const authRadios = document.querySelectorAll('input[name="auth_method"]');
    authRadios.forEach(radio => {
      radio.addEventListener('change', (e) => {
        this.handleModalAuthMethodChange(e.target.value);
      });
    });

      // 文件上传处理 - 修复元素选择器
      const fileInput = document.getElementById('modal-ipa-file');
      const fileUploadArea = document.getElementById('modal-file-upload-area');

      if (fileInput) {
        fileInput.addEventListener('change', (e) => {
          if (e.target.files && e.target.files[0]) {
            this.handleModalFileSelect(e.target.files[0]);
          }
        });
      }

      // 点击上传区域选择文件（整个区域都可以点击）
      if (fileUploadArea) {
        fileUploadArea.addEventListener('click', () => {
          if (fileInput) {
            fileInput.click();
          }
        });
      }

      // 拖拽上传
      if (fileUploadArea) {
        // 阻止默认行为
        fileUploadArea.addEventListener('dragover', (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.handleDragOver(e);
        });

        fileUploadArea.addEventListener('dragenter', (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.handleDragOver(e);
        });

        fileUploadArea.addEventListener('dragleave', (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.handleDragLeave(e);
        });

        fileUploadArea.addEventListener('drop', (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.handleFileDrop(e);
        });
      }

      // 表单字段变化事件 - 使用更广泛的选择器
      const formInputs = document.querySelectorAll('#modal-auth-method-container input, #modal-auth-fields-container input, #modal-auth-fields-container textarea, #modal-release-notes-container textarea');
      formInputs.forEach(input => {
        input.addEventListener('input', () => {
          this.validateModalForm();
        });

        // 也监听change事件（对于radio按钮）
        input.addEventListener('change', () => {
          this.validateModalForm();
        });
      });
  }

  /**
   * 处理认证方式切换
   */
  handleModalAuthMethodChange(method) {
    const apiKeyConfig = document.getElementById('modal-api-key-config');
    const appleIdConfig = document.getElementById('modal-apple-id-config');

    if (method === 'api_key') {
      apiKeyConfig.style.display = 'block';
      appleIdConfig.style.display = 'none';
    } else {
      apiKeyConfig.style.display = 'none';
      appleIdConfig.style.display = 'block';
    }

    // 重新验证表单
    this.validateModalForm();
  }



  /**
   * 设置步骤导航
   */
  setupStepNavigation() {
    const nextBtn = document.getElementById('next-step-btn');
    const prevBtn = document.getElementById('prev-step-btn');

    if (nextBtn) {
      nextBtn.addEventListener('click', () => this.nextStep());
    }

    if (prevBtn) {
      prevBtn.addEventListener('click', () => this.prevStep());
    }
  }

  /**
   * 下一步
   */
  nextStep() {
    if (this.currentStep < this.totalSteps) {
      // 验证当前步骤
      if (this.validateCurrentStep()) {
        this.currentStep++;
        this.updateStepDisplay();
      }
    } else if (this.currentStep === this.totalSteps) {
      // 最后一步，开始上传
      this.handleModalSubmit();
    }
  }

  /**
   * 上一步
   */
  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
      this.updateStepDisplay();
    }
  }

  /**
   * 验证当前步骤
   */
  validateCurrentStep() {
    switch (this.currentStep) {
      case 1:
        // 验证文件选择
        if (!this.modalCurrentFile) {
          alert('请先选择IPA文件');
          return false;
        }
        return true;

      case 2:
        // 验证认证信息
        return this.validateAuthFields();

      case 3:
        // 最后确认
        return true;

      default:
        return true;
    }
  }

  /**
   * 验证认证字段
   */
  validateAuthFields() {
    if (!this.modalFormFields) return false;

    const authMethod = this.modalFormFields.authMethod.getValue();

    if (authMethod === 'api_key') {
      const apiKeyId = this.modalFormFields.apiKeyId.getValue().trim();
      const issuerId = this.modalFormFields.issuerId.getValue().trim();
      const apiKeyContent = this.modalFormFields.apiKeyContent.getValue().trim();

      if (!apiKeyId || !issuerId || !apiKeyContent) {
        alert('请填写完整的API Key信息');
        return false;
      }
    } else {
      const appleId = this.modalFormFields.appleId.getValue().trim();
      const appPassword = this.modalFormFields.appPassword.getValue().trim();

      if (!appleId || !appPassword) {
        alert('请填写完整的Apple ID信息');
        return false;
      }
    }

    return true;
  }

  /**
   * 更新步骤显示
   */
  updateStepDisplay() {
    // 更新步骤指示器
    const steps = document.querySelectorAll('.step');
    const panels = document.querySelectorAll('.step-panel');

    steps.forEach((step, index) => {
      const stepNumber = index + 1;
      if (stepNumber <= this.currentStep) {
        step.classList.add('active');
        if (stepNumber < this.currentStep) {
          step.classList.add('completed');
        }
      } else {
        step.classList.remove('active', 'completed');
      }
    });

    // 更新面板显示
    panels.forEach((panel, index) => {
      const stepNumber = index + 1;
      if (stepNumber === this.currentStep) {
        panel.classList.add('active');
      } else {
        panel.classList.remove('active');
      }
    });

    // 更新导航按钮
    const nextBtn = document.getElementById('next-step-btn');
    const prevBtn = document.getElementById('prev-step-btn');

    if (prevBtn) {
      prevBtn.style.display = this.currentStep > 1 ? 'block' : 'none';
    }

    if (nextBtn) {
      if (this.currentStep === this.totalSteps) {
        nextBtn.textContent = '🚀 开始上传';
        nextBtn.className = 'btn btn-primary btn-lg';
      } else {
        nextBtn.textContent = '下一步 →';
        nextBtn.className = 'btn btn-primary';
      }
    }

    // 如果是第3步，生成上传摘要
    if (this.currentStep === 3) {
      this.generateUploadSummary();
    }
  }

  /**
   * 生成上传摘要
   */
  generateUploadSummary() {
    const summaryContainer = document.getElementById('upload-summary');
    if (!summaryContainer || !this.modalCurrentFile || !this.modalFormFields) return;

    const authMethod = this.modalFormFields.authMethod.getValue();
    const authLabel = authMethod === 'api_key' ? 'API Key 认证' : 'Apple ID 认证';

    summaryContainer.innerHTML = `
      <div class="upload-summary-card">
        <h4 class="summary-title">📋 上传信息确认</h4>
        <div class="summary-grid">
          <div class="summary-item">
            <span class="summary-label">文件名称:</span>
            <span class="summary-value">${this.modalCurrentFile.name}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">文件大小:</span>
            <span class="summary-value">${formatFileSize(this.modalCurrentFile.size)}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">认证方式:</span>
            <span class="summary-value">${authLabel}</span>
          </div>
          ${authMethod === 'api_key' ? `
            <div class="summary-item">
              <span class="summary-label">API Key ID:</span>
              <span class="summary-value">${this.modalFormFields.apiKeyId.getValue()}</span>
            </div>
          ` : `
            <div class="summary-item">
              <span class="summary-label">Apple ID:</span>
              <span class="summary-value">${this.modalFormFields.appleId.getValue()}</span>
            </div>
          `}
        </div>
        <div class="summary-warning">
          <div class="warning-icon">⚠️</div>
          <div class="warning-text">
            <strong>注意：</strong>上传过程中请不要关闭窗口，整个过程可能需要5-15分钟。
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 保存认证信息到本地存储
   */
  saveAuthInfo() {
    if (!this.modalFormFields) return;

    const authMethod = this.modalFormFields.authMethod.getValue();
    const authInfo = {
      method: authMethod,
      timestamp: Date.now()
    };

    if (authMethod === 'api_key') {
      authInfo.api_key_id = this.modalFormFields.apiKeyId.getValue().trim();
      authInfo.issuer_id = this.modalFormFields.issuerId.getValue().trim();
      // 不保存私钥内容，出于安全考虑
    } else {
      authInfo.apple_id = this.modalFormFields.appleId.getValue().trim();
      // 不保存密码，出于安全考虑
    }

    localStorage.setItem('upload_auth_info', JSON.stringify(authInfo));
  }

  /**
   * 加载保存的认证信息
   */
  loadSavedAuthInfo() {
    try {
      const saved = localStorage.getItem('upload_auth_info');
      if (saved) {
        const authInfo = JSON.parse(saved);

        // 检查是否过期（7天）
        const isExpired = Date.now() - authInfo.timestamp > 7 * 24 * 60 * 60 * 1000;
        if (isExpired) {
          localStorage.removeItem('upload_auth_info');
          return null;
        }

        return authInfo;
      }
    } catch (error) {
      console.error('Failed to load saved auth info:', error);
    }
    return null;
  }

  /**
   * 应用保存的认证信息
   */
  applySavedAuthInfo() {
    const saved = this.loadSavedAuthInfo();
    if (!saved || !this.modalFormFields) return;

    // 设置认证方式
    this.modalFormFields.authMethod.setValue(saved.method);

    // 填充保存的信息
    if (saved.method === 'api_key' && saved.api_key_id && saved.issuer_id) {
      this.modalFormFields.apiKeyId.setValue(saved.api_key_id);
      this.modalFormFields.issuerId.setValue(saved.issuer_id);
    } else if (saved.method === 'apple_id' && saved.apple_id) {
      this.modalFormFields.appleId.setValue(saved.apple_id);
    }
  }



  /**
   * 处理拖拽悬停
   */
  handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
  }

  /**
   * 处理拖拽离开
   */
  handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
  }

  /**
   * 处理文件拖拽放下
   */
  handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.name.toLowerCase().endsWith('.ipa')) {
        // 设置文件到input元素
        const fileInput = document.getElementById('modal-ipa-file');
        if (fileInput) {
          // 创建新的FileList（虽然FileList是只读的，但我们可以直接处理文件）
          this.handleModalFileSelect(file);
        }
      } else {
        alert('❌ 请选择.ipa格式的文件');
      }
    }
  }

  /**
   * 处理文件选择（统一入口）
   */
  handleModalFileSelect(file) {
    try {
      this.processModalFile(file);
    } catch (error) {
      // 文件处理错误，静默处理
    }
  }

  /**
   * 处理文件（统一处理函数，参考old项目的processFile）
   */
  async processModalFile(file) {
    if (!file) {
      return;
    }

    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.ipa')) {
      alert('❌ 请选择.ipa格式的文件');
      return;
    }

    // 检查文件大小 (2GB = 2 * 1024 * 1024 * 1024 bytes)
    const maxSize = 2 * 1024 * 1024 * 1024; // 2GB
    if (file.size > maxSize) {
      alert('❌ 文件大小超过限制，最大支持2GB');
      return;
    }

    this.modalCurrentFile = file;

    // 显示基本文件信息
    this.displayFileInfo(file);

    // 开始客户端解析IPA文件
    await this.parseIPAClientSide(file);

    // 验证表单
    this.validateModalForm();
  }

  /**
   * 客户端解析IPA文件
   */
  async parseIPAClientSide(file) {
    try {
      // 显示解析状态
      this.updateFileInfoWithParsing('🔍 正在解析IPA文件...');

      // 使用客户端IPA解析器
      if (!window.XIOSIPAParser) {
        throw new Error('IPA解析器未加载');
      }

      // 解析IPA文件
      const parsedInfo = await window.XIOSIPAParser.parseIPA(file);

      // 保存解析结果
      this.modalCurrentFileInfo = parsedInfo;

      // 更新显示
      this.updateFileInfoWithParsedData(parsedInfo);

    } catch (error) {
      console.error('Client-side IPA parsing failed:', error);

      // 显示解析失败，但仍允许继续上传
      this.updateFileInfoWithParsingError(error.message);

      // 创建基本信息作为备选
      this.modalCurrentFileInfo = {
        bundle_id: 'unknown.bundle.id',
        app_name: file.name.replace('.ipa', ''),
        version: '1.0.0',
        build: '1',
        minimum_os_version: 'Unknown',
        supported_devices: [],
        required_capabilities: [],
        icon: null,
        file_size: file.size,
        file_name: file.name,
        parsing_error: error.message
      };
    }
  }

  /**
   * 显示文件信息（直接在上传区域显示）
   */
  displayFileInfo(file) {
    // 直接在上传区域显示文件信息
    const uploadArea = document.getElementById('modal-file-upload-area');
    if (!uploadArea) {
      return;
    }
    uploadArea.innerHTML = `
      <div class="upload-success">
        <div class="upload-icon">✅</div>
        <div class="upload-text">
          <h4>已选择文件</h4>
          <p><strong>${file.name}</strong></p>
          <div class="file-details">
            <span>大小: ${formatFileSize(file.size)}</span>
            <span>类型: .ipa</span>
            <span class="status-ready">✅ 准备就绪</span>
          </div>
          <small>点击重新选择文件</small>
        </div>
      </div>
    `;

    // 重新绑定点击事件
    uploadArea.addEventListener('click', () => {
      const fileInput = document.getElementById('modal-ipa-file');
      if (fileInput) fileInput.click();
    });
  }

  /**
   * 更新文件信息显示为解析中状态
   */
  updateFileInfoWithParsing(message) {
    const uploadArea = document.getElementById('modal-file-upload-area');
    if (!uploadArea) return;

    const statusElement = uploadArea.querySelector('.status-ready');
    if (statusElement) {
      statusElement.innerHTML = `<span class="status-parsing">🔍 ${message}</span>`;
      statusElement.className = 'status-parsing';
    }
  }

  /**
   * 更新文件信息显示为已解析状态
   */
  updateFileInfoWithParsedData(parsedInfo) {
    const uploadArea = document.getElementById('modal-file-upload-area');
    if (!uploadArea) return;

    // 构建图标显示
    const iconDisplay = parsedInfo.icon
      ? `<img src="${parsedInfo.icon}" alt="App Icon" style="width: 32px; height: 32px; border-radius: 6px; margin-right: 8px;">`
      : '✅';

    // 更新显示内容
    uploadArea.innerHTML = `
      <div class="upload-success">
        <div class="upload-icon">${iconDisplay}</div>
        <div class="upload-text">
          <h4>已解析IPA文件</h4>
          <p><strong>${parsedInfo.app_name}</strong></p>
          <div class="file-details">
            <span>Bundle ID: ${parsedInfo.bundle_id}</span>
            <span>版本: ${parsedInfo.version} (${parsedInfo.build})</span>
            <span>大小: ${formatFileSize(parsedInfo.file_size)}</span>
            <span class="status-parsed">✅ 解析完成</span>
            ${parsedInfo.icon ? '<span class="status-icon">🖼️ 图标已提取</span>' : ''}
          </div>
          <small>点击重新选择文件</small>
        </div>
      </div>
    `;

    // 重新绑定点击事件
    uploadArea.addEventListener('click', () => {
      const fileInput = document.getElementById('modal-ipa-file');
      if (fileInput) fileInput.click();
    });
  }

  /**
   * 更新文件信息显示为解析失败状态
   */
  updateFileInfoWithParsingError(errorMessage) {
    const uploadArea = document.getElementById('modal-file-upload-area');
    if (!uploadArea) return;

    const statusElement = uploadArea.querySelector('.status-parsing');
    if (statusElement) {
      statusElement.innerHTML = `<span class="status-parse-error">⚠️ 解析失败，将使用基本信息</span>`;
      statusElement.className = 'status-parse-error';
      statusElement.title = errorMessage;
    }
  }

  /**
   * 更新上传详情显示
   */
  updateUploadDetails(details) {
    // 更新新版本的详细信息显示
    const uploadedSizeEl = document.getElementById('upload-uploaded-size');
    const totalSizeEl = document.getElementById('upload-total-size');
    const speedEl = document.getElementById('upload-speed');
    const elapsedEl = document.getElementById('upload-elapsed');
    const remainingEl = document.getElementById('upload-remaining');

    if (uploadedSizeEl && details.uploaded) {
      uploadedSizeEl.textContent = details.uploaded;
    }

    if (totalSizeEl && details.total) {
      totalSizeEl.textContent = details.total;
    }

    if (speedEl && details.speed) {
      speedEl.textContent = details.speed;
    }

    if (elapsedEl && details.elapsed) {
      elapsedEl.textContent = details.elapsed;
    }

    if (remainingEl && details.remaining) {
      remainingEl.textContent = details.remaining;
    }

    // 兼容旧版本的进度显示（如果存在）
    const progressText = document.getElementById('modal-progress-text');
    const progressPercentage = document.getElementById('modal-progress-percentage');

    if (progressText && details.percent) {
      progressText.innerHTML = `
        <div class="upload-details">
          <div class="upload-main-text">📤 上传文件中... ${details.percent}%</div>
          <div class="upload-sub-text">
            <span>${details.uploaded} / ${details.total}</span>
            <span class="upload-speed">${details.speed}</span>
            <span class="upload-remaining">剩余 ${details.remaining}</span>
          </div>
        </div>
      `;
    }

    if (progressPercentage && details.percent) {
      progressPercentage.textContent = details.percent + '%';
    }
  }

  /**
   * 验证弹窗表单
   */
  validateModalForm() {
    if (!this.modalFormFields) {
      return;
    }

    const authMethod = this.modalFormFields.authMethod.getValue();

    let isValid = false;

    if (authMethod === 'api_key') {
      const apiKeyId = this.modalFormFields.apiKeyId?.getValue();
      const issuerId = this.modalFormFields.issuerId?.getValue();
      const apiKeyContent = this.modalFormFields.apiKeyContent?.getValue();

      isValid = apiKeyId && issuerId && apiKeyContent && this.modalCurrentFile;
    } else {
      const appleId = this.modalFormFields.appleId?.getValue();
      const appPassword = this.modalFormFields.appPassword?.getValue();

      isValid = appleId && appPassword && this.modalCurrentFile;
    }

    // 设置步骤导航按钮的状态
    const nextBtn = document.getElementById('next-step-btn');
    if (nextBtn && this.currentStep === this.totalSteps) {
      // 只在最后一步（上传步骤）时控制按钮状态
      nextBtn.disabled = !isValid;
    }
  }

  /**
   * 处理弹窗表单提交
   */
  async handleModalSubmit() {
    if (this.uploadInProgress) {
      return; // 防止重复提交
    }

    try {
      this.uploadInProgress = true;

      // 设置按钮loading状态
      const nextBtn = document.getElementById('next-step-btn');
      if (nextBtn) {
        nextBtn.classList.add('loading');
        nextBtn.disabled = true;
      }

      // 保存认证信息
      this.saveAuthInfo();

      // 切换到上传进度面板
      this.showUploadProgressPanel();

      // 禁用关闭按钮，防止用户在上传过程中关闭弹窗
      this.disableModalClose();

      // 检查文件是否已选择
      if (!this.modalCurrentFile) {
        throw new Error('请先选择IPA文件');
      }

      this.updateUploadProgress(5, '📋 验证上传信息...', 'preparing');

      // 第一步：上传IPA文件到服务器
      this.updateModalProgress(10, '📤 开始上传IPA文件...');

      // 显示文件信息
      this.updateUploadDetails({
        uploaded: '0 MB',
        total: formatFileSize(this.modalCurrentFile.size),
        speed: '准备中...',
        elapsed: '0秒',
        remaining: '计算中...',
        percent: 0
      });

      const fileUploadResult = await this.uploadIPAFile();

      if (!fileUploadResult.success) {
        throw new Error(fileUploadResult.error || '文件上传失败');
      }

      // 第二步：创建上传任务（使用上传后的文件信息）
      this.updateModalProgress(75, '📋 创建上传任务...');
      const uploadData = this.collectModalUploadData();
      uploadData.file_path = fileUploadResult.file_path;
      uploadData.file_name = fileUploadResult.file_name;
      uploadData.file_size = fileUploadResult.file_size;

      // 如果有应用信息，也添加进去
      if (fileUploadResult.app_info) {
        uploadData.app_info = fileUploadResult.app_info;
      }



      // 使用原生fetch创建上传任务（与old项目一致）
      const response = await fetch('https://api.ios.xxyx.cn/api/upload/ipa', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(uploadData)
      });

      // 检查响应状态（与old项目一致）
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`创建上传任务失败 (${response.status}): ${errorText}`);
      }

      const result = await response.json();

      if (result.success) {
        // 处理upload_id（与old项目一致）
        const uploadId = typeof result.upload_id === 'string' ?
          result.upload_id :
          (result.upload_id && (result.upload_id.$oid || result.upload_id));

        this.updateModalProgress(85, '🚀 任务已创建，开始处理...');

        // 第三步：开始轮询上传进度
        await this.startModalProgressPolling(uploadId);

      } else {
        throw new Error(result.error || '创建上传任务失败');
      }

    } catch (error) {
      console.error('Upload failed:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.status,
        response: error.response,
        stack: error.stack
      });

      let errorMessage = '上传失败';

      if (error.status === 400) {
        errorMessage = '请求参数错误，请检查填写的信息';
      } else if (error.status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (error.status === 413) {
        errorMessage = '文件太大，请选择小于2GB的文件';
      } else if (error.status === 500) {
        errorMessage = '服务器内部错误，请稍后重试';
      } else if (error.message) {
        errorMessage = error.message;
      }

      this.updateModalProgress(0, '❌ ' + errorMessage);
      this.enableModalClose();

      // 不显示alert，错误信息已经在进度条中显示了

    } finally {
      // 由于使用步骤导航按钮，需要找到实际的按钮来取消loading状态
      const nextBtn = document.getElementById('next-step-btn');
      if (nextBtn && nextBtn.classList.contains('loading')) {
        nextBtn.classList.remove('loading');
        nextBtn.disabled = false;
      }
      this.uploadInProgress = false;
    }
  }

  /**
   * 收集弹窗上传数据（完全按照old项目格式）
   */
  collectModalUploadData() {
    const authMethod = this.modalFormFields.authMethod.getValue();
    const releaseNotes = this.modalFormFields.releaseNotes.getValue().trim();

    // 检查是否有文件
    if (!this.modalCurrentFile) {
      throw new Error('请先选择IPA文件');
    }

    const data = {
      auth_method: authMethod,
      release_notes: releaseNotes,
      file_name: this.modalCurrentFile.name,
      file_size: this.modalCurrentFile.size
    };

    // 添加客户端解析的IPA信息
    if (this.modalCurrentFileInfo) {
      data.app_info = this.modalCurrentFileInfo;
      // 添加具体的应用信息字段，便于后端处理
      data.bundle_id = this.modalCurrentFileInfo.bundle_id;
      data.app_name = this.modalCurrentFileInfo.app_name;
      data.version = this.modalCurrentFileInfo.version;
      data.build = this.modalCurrentFileInfo.build;
      data.minimum_os_version = this.modalCurrentFileInfo.minimum_os_version;
      data.supported_devices = this.modalCurrentFileInfo.supported_devices;
      data.required_capabilities = this.modalCurrentFileInfo.required_capabilities;

      // 添加应用图标（base64格式）
      if (this.modalCurrentFileInfo.icon) {
        data.icon_base64 = this.modalCurrentFileInfo.icon;
      }

      // 如果有解析错误，也包含进去
      if (this.modalCurrentFileInfo.parsing_error) {
        data.parsing_error = this.modalCurrentFileInfo.parsing_error;
      }
    }

    // file_path 将在上传完成后添加

    if (authMethod === 'api_key') {
      data.api_key_id = this.modalFormFields.apiKeyId.getValue().trim();
      data.issuer_id = this.modalFormFields.issuerId.getValue().trim();
      data.api_key_content = this.modalFormFields.apiKeyContent.getValue().trim();

      // 验证API Key字段
      if (!data.api_key_id || !data.issuer_id || !data.api_key_content) {
        throw new Error('请填写完整的API Key信息');
      }
    } else {
      data.apple_id = this.modalFormFields.appleId.getValue().trim();
      data.app_password = this.modalFormFields.appPassword.getValue().trim();

      // 验证Apple ID字段
      if (!data.apple_id || !data.app_password) {
        throw new Error('请填写完整的Apple ID信息');
      }
    }



    return data;
  }

  /**
   * 创建上传FormData（参考old项目格式）
   */
  createUploadFormData() {
    const formData = new FormData();

    // 添加IPA文件
    if (this.modalCurrentFile) {
      formData.append('ipa_file', this.modalCurrentFile);
    }

    // 添加认证信息
    const authMethod = this.modalFormFields.authMethod.getValue();
    formData.append('auth_method', authMethod);

    if (authMethod === 'api_key') {
      formData.append('api_key_id', this.modalFormFields.apiKeyId.getValue().trim());
      formData.append('issuer_id', this.modalFormFields.issuerId.getValue().trim());
      formData.append('api_key_content', this.modalFormFields.apiKeyContent.getValue().trim());
    } else {
      formData.append('apple_id', this.modalFormFields.appleId.getValue().trim());
      formData.append('app_password', this.modalFormFields.appPassword.getValue().trim());
    }

    // 添加发布说明
    const releaseNotes = this.modalFormFields.releaseNotes.getValue();
    if (releaseNotes) {
      formData.append('release_notes', releaseNotes);
    }

    return formData;
  }

  /**
   * 上传IPA文件到服务器（带速度和进度显示）
   */
  async uploadIPAFile() {
    if (!this.modalCurrentFile) {
      throw new Error('没有选择文件');
    }

    const formData = new FormData();
    formData.append('ipa_file', this.modalCurrentFile);

    // 上传开始时间
    const startTime = Date.now();
    let lastLoaded = 0;
    let lastTime = startTime;

    try {
      // 使用XMLHttpRequest实现上传进度监听（现在只上传文件，不解析）
      const response = await this.uploadFileOnly(formData, startTime, lastLoaded, lastTime);

      // 检查响应状态（XMLHttpRequest返回格式）
      if (response.status < 200 || response.status >= 300) {
        throw new Error(`文件上传失败 (${response.status})`);
      }

      const data = response.data;

      if (data.success) {
        this.updateUploadProgress(70, '✅ 文件上传完成', 'processing');

        return {
          success: true,
          file_path: data.file_path,
          file_name: data.file_name,
          file_size: data.file_size,
          app_info: this.modalCurrentFileInfo // 使用客户端解析的信息
        };
      } else {
        throw new Error(data.error || '文件上传失败');
      }
    } catch (error) {
      console.error('File upload error:', error);

      // 尝试获取更详细的错误信息
      let errorMessage = '文件上传失败';

      if (error.response) {
        try {
          const errorText = await error.response.text();
          console.error('Error response text:', errorText);

          try {
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.error || errorData.message || errorMessage;
          } catch (parseError) {
            errorMessage = errorText || errorMessage;
          }
        } catch (textError) {
          console.error('Failed to read error response:', textError);
        }
      }

      if (error.status === 400) {
        errorMessage = `请求参数错误 (400): ${errorMessage}`;
      } else if (error.status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (error.status === 413) {
        errorMessage = '文件太大，请选择小于2GB的文件';
      } else if (error.status === 500) {
        errorMessage = '服务器内部错误，请稍后重试';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 开始轮询上传进度
   */
  async startModalProgressPolling(uploadId) {
    this.currentUploadId = uploadId;
    let pollCount = 0;
    const maxPolls = 120; // 最多轮询2分钟（每秒一次）

    const poll = async () => {
      try {
        // 使用原生fetch轮询进度（与old项目一致）
        const response = await fetch(`https://api.ios.xxyx.cn/api/upload/progress/${uploadId}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();

          if (data.success && data.progress) {
            const { status, progress } = data.progress;
            const message = data.progress.message || '';

            // 根据进度更新步骤状态
            let step = 'processing';
            if (progress >= 95) {
              step = 'completed';
            } else if (progress >= 50) {
              step = 'processing';
            } else {
              step = 'uploading';
            }

            this.updateUploadProgress(Math.max(85, progress), message, step);

            // 检查是否完成
            if (status === 'completed') {
              this.updateUploadProgress(100, '✅ 上传完成！', 'completed');

              setTimeout(() => {
                // 关闭弹窗并刷新记录
                this.closeModal();
                // 确保刷新当前页面的记录
                this.loadRecords(this.currentPage || 1);
              }, 1500);

              return;
            } else if (status === 'failed') {
              // 显示详细的失败信息
              const errorMsg = message || data.progress.error || '上传过程中发生未知错误';
              console.error('Upload failed with details:', data.progress);

              this.updateUploadProgress(0, '❌ 上传失败', 'failed');
              this.showUploadError(errorMsg, data.progress);
              return;
            }

            // 继续轮询
            pollCount++;
            if (pollCount < maxPolls) {
              setTimeout(poll, 1000); // 1秒后再次轮询
            } else {
              throw new Error('上传超时，请稍后查看上传记录');
            }
          } else {
            this.updateUploadProgress(85, '获取进度失败，正在重试...', 'processing');
            // 获取失败，继续轮询
            pollCount++;
            if (pollCount < maxPolls) {
              setTimeout(poll, 2000); // 2秒后重试
            } else {
              this.showUploadError('获取上传状态失败', { error: '服务器响应异常' });
              return;
            }
          }
        } else {
          this.updateUploadProgress(85, '网络错误，正在重试...', 'processing');
          // 网络错误，继续轮询
          pollCount++;
          if (pollCount < maxPolls) {
            setTimeout(poll, 2000); // 2秒后重试
          } else {
            this.showUploadError('网络连接失败', { error: `HTTP ${response.status}: ${response.statusText}` });
            return;
          }
        }
      } catch (error) {
        console.error('Poll progress error:', error);
        this.showUploadError(error.message || '轮询进度时发生错误', { error: error.stack });
      }
    };

    // 开始轮询
    poll();
  }

  /**
   * 禁用弹窗关闭
   */
  disableModalClose() {
    const closeBtn = document.querySelector('.modal-close');
    const backdrop = document.querySelector('.modal-backdrop');

    if (closeBtn) {
      closeBtn.style.display = 'none';
    }

    if (backdrop) {
      backdrop.style.pointerEvents = 'none';
    }

    // 显示提示信息
    const modalHeader = document.querySelector('.modal-header');
    if (modalHeader) {
      let uploadingTip = modalHeader.querySelector('.uploading-tip');
      if (!uploadingTip) {
        uploadingTip = document.createElement('div');
        uploadingTip.className = 'uploading-tip';
        uploadingTip.innerHTML = '<small style="color: #f59e0b;">⚠️ 上传进行中，请勿关闭窗口</small>';
        modalHeader.appendChild(uploadingTip);
      }
    }
  }

  /**
   * 启用弹窗关闭
   */
  enableModalClose() {
    const closeBtn = document.querySelector('.modal-close');
    const backdrop = document.querySelector('.modal-backdrop');

    if (closeBtn) {
      closeBtn.style.display = 'block';
    }

    if (backdrop) {
      backdrop.style.pointerEvents = 'auto';
    }

    // 移除提示信息
    const uploadingTip = document.querySelector('.uploading-tip');
    if (uploadingTip) {
      uploadingTip.remove();
    }
  }

  /**
   * 关闭弹窗
   */
  closeModal() {
    // 清理事件监听器
    if (this.modalEscHandler) {
      document.removeEventListener('keydown', this.modalEscHandler);
      this.modalEscHandler = null;
    }

    // 重置状态
    this.uploadInProgress = false;
    this.currentUploadId = null;
    this.modalCurrentFile = null;
    this.modalFormFields = null;

    const modal = document.querySelector('.modal');
    const backdrop = document.querySelector('.modal-backdrop');

    if (modal) modal.remove();
    if (backdrop) backdrop.remove();
  }

  /**
   * 关闭模态框并刷新记录列表
   */
  closeModalAndRefresh() {
    this.closeModal();
    // 刷新记录列表，保持当前页面
    this.loadRecords(this.currentPage || 1);
  }

  /**
   * 显示弹窗上传进度
   */
  showModalUploadProgress() {
    const progressContainer = document.getElementById('modal-upload-progress');
    if (progressContainer) {
      progressContainer.style.display = 'block';
    }
  }

  /**
   * 隐藏弹窗上传进度
   */
  hideModalUploadProgress() {
    const progressContainer = document.getElementById('modal-upload-progress');
    if (progressContainer) {
      progressContainer.style.display = 'none';
    }
  }

  /**
   * 更新弹窗上传进度
   */
  updateModalProgress(progress, text) {
    const progressFill = document.getElementById('modal-progress-fill');
    const progressText = document.getElementById('modal-progress-text');
    const progressPercentage = document.getElementById('modal-progress-percentage');

    if (progressFill) {
      progressFill.style.width = progress + '%';
    }

    if (progressText) {
      progressText.textContent = text || '处理中...';
    }

    if (progressPercentage) {
      progressPercentage.textContent = Math.round(progress) + '%';
    }
  }

  /**
   * 筛选记录
   */
  filterRecords() {
    const statusFilter = document.getElementById('status-filter');
    if (!statusFilter) return;

    const selectedStatus = statusFilter.value;
    const recordCards = document.querySelectorAll('#upload-records-container .card');

    recordCards.forEach(card => {
      if (!selectedStatus) {
        card.style.display = 'block';
      } else {
        const statusBadge = card.querySelector('.badge');
        if (statusBadge) {
          const cardStatus = this.getStatusFromBadge(statusBadge);
          card.style.display = cardStatus === selectedStatus ? 'block' : 'none';
        }
      }
    });
  }

  /**
   * 从徽章获取状态
   */
  getStatusFromBadge(badge) {
    const text = badge.textContent.toLowerCase();
    if (text.includes('等待') || text.includes('pending')) return 'pending';
    if (text.includes('处理') || text.includes('processing')) return 'processing';
    if (text.includes('完成') || text.includes('completed')) return 'completed';
    if (text.includes('失败') || text.includes('failed')) return 'failed';
    return '';
  }



  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 刷新记录按钮
    const refreshBtn = document.getElementById('refresh-records-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadRecords());
    }

    // 添加上传任务按钮
    const addTaskBtn = document.getElementById('add-upload-task-btn');
    if (addTaskBtn) {
      addTaskBtn.addEventListener('click', () => this.showUploadModal());
    }

    // 状态筛选
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', () => this.filterRecords());
    }

    // 绑定表单字段变化事件
    this.bindFormEvents();
  }

  /**
   * 绑定表单事件
   */
  bindFormEvents() {
    // 重新绑定事件，因为组件创建后需要重新设置
    setTimeout(() => {
      // 认证方式变化
      const authRadios = document.querySelectorAll('input[name="auth_method"]');
      authRadios.forEach(radio => {
        radio.addEventListener('change', (e) => {
          this.handleAuthMethodChange(e.target.value);
        });
      });

      // 文件变化
      const fileInput = document.querySelector('input[name="ipa_file"]');
      if (fileInput) {
        fileInput.addEventListener('change', (e) => {
          this.handleFileChange(e.target.files[0]);
        });
      }

      // 表单字段变化时验证
      const formInputs = document.querySelectorAll('.form-control');
      formInputs.forEach(input => {
        input.addEventListener('input', () => {
          this.validateForm();
        });
        input.addEventListener('change', () => {
          this.validateForm();
        });
      });
    }, 100);
  }

  /**
   * 处理认证方式变化
   */
  handleAuthMethodChange(method) {
    // 检查是否在弹窗模式下
    const formFields = this.modalFormFields || this.formFields;

    if (!formFields) {
      return;
    }

    const apiKeyConfig = document.getElementById('api-key-config');
    const appleIdConfig = document.getElementById('apple-id-config');

    if (method === 'api_key') {
      if (apiKeyConfig) apiKeyConfig.style.display = 'block';
      if (appleIdConfig) appleIdConfig.style.display = 'none';

      // 设置必填状态
      if (formFields.apiKeyId) formFields.apiKeyId.options.required = true;
      if (formFields.issuerId) formFields.issuerId.options.required = true;
      if (formFields.apiKeyContent) formFields.apiKeyContent.options.required = true;
      if (formFields.appleId) formFields.appleId.options.required = false;
      if (formFields.appPassword) formFields.appPassword.options.required = false;
    } else {
      if (apiKeyConfig) apiKeyConfig.style.display = 'none';
      if (appleIdConfig) appleIdConfig.style.display = 'block';

      // 设置必填状态
      if (formFields.apiKeyId) formFields.apiKeyId.options.required = false;
      if (formFields.issuerId) formFields.issuerId.options.required = false;
      if (formFields.apiKeyContent) formFields.apiKeyContent.options.required = false;
      if (formFields.appleId) formFields.appleId.options.required = true;
      if (formFields.appPassword) formFields.appPassword.options.required = true;
    }

    this.validateForm();
  }

  /**
   * 处理文件变化
   */
  handleFileChange(file) {
    this.currentFile = file;
    
    if (file) {
      // 显示文件信息
      document.getElementById('file-size').textContent = formatFileSize(file.size);
      document.getElementById('ipa-info').style.display = 'block';
      
      // 这里可以添加IPA文件解析逻辑
      // 暂时显示文件名作为应用名称
      document.getElementById('app-name').textContent = file.name.replace('.ipa', '');
    } else {
      document.getElementById('ipa-info').style.display = 'none';
    }
    
    this.validateForm();
  }

  /**
   * 验证表单（简化版，主要用于兼容旧代码）
   */
  validateForm() {
    // 如果在弹窗模式下，使用validateModalForm
    if (this.modalFormFields) {
      this.validateModalForm();
      return;
    }

    // 旧版本表单验证（保留以防万一）
    if (!this.formFields || !this.submitButton) {
      return;
    }

    // 简单的验证逻辑
    const hasFile = !!this.currentFile;
    if (this.submitButton) {
      this.submitButton.setDisabled(!hasFile);
    }
  }

  /**
   * 格式化上传速度
   */
  formatSpeed(bytesPerSecond) {
    if (bytesPerSecond === 0) return '0 B/s';
    const k = 1024;
    const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
    const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));
    return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * 格式化剩余时间
   */
  formatTime(seconds) {
    if (seconds === 0 || !isFinite(seconds)) return '--';

    if (seconds < 60) {
      return Math.round(seconds) + '秒';
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.round(seconds % 60);
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes}分钟`;
    }
  }

  /**
   * 重置表单
   */
  resetForm() {
    Object.values(this.formFields).forEach(field => {
      field.setValue('');
    });
    this.currentFile = null;
    document.getElementById('ipa-info').style.display = 'none';
    this.validateForm();
  }

  /**
   * 加载上传记录（支持分页）
   */
  async loadRecords(page = 1, pageSize = 10) {
    if (!this.recordsContainer) return;

    try {
      // 保存当前分页信息
      this.currentPage = page;
      this.pageSize = pageSize;

      // 只在第一页时显示加载中，其他页面显示在分页区域
      if (page === 1) {
        this.recordsContainer.innerHTML = '<div class="text-center py-8">加载中...</div>';
      } else {
        this.showPaginationLoading();
      }

      // 构建请求URL
      let url = '/api/upload/records';
      const params = new URLSearchParams();

      // 添加分页参数
      params.append('page', page.toString());
      params.append('page_size', pageSize.toString());

      url += '?' + params.toString();

      const response = await api.get(url);

      if (response.data.success) {
        const { records, pagination } = response.data;

        if (records && records.length > 0) {
          this.renderRecords(records);
          this.renderPagination(pagination);
        } else if (page === 1) {
          this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无上传记录</div>';
          this.clearPagination();
        }
      } else {
        if (page === 1) {
          this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无上传记录</div>';
        }
        this.clearPagination();
      }
    } catch (error) {
      console.error('Failed to load records:', error);

      // 如果是认证错误，显示相应提示
      if (error.status === 401) {
        this.recordsContainer.innerHTML = '<div class="text-center py-8 text-error">认证失败，请重新登录</div>';
      } else {
        this.recordsContainer.innerHTML = '<div class="text-center py-8 text-error">加载失败，请稍后重试</div>';
      }
      this.clearPagination();
    }
  }

  /**
   * 显示分页加载状态
   */
  showPaginationLoading() {
    const paginationContainer = this.getPaginationContainer();
    if (paginationContainer) {
      paginationContainer.innerHTML = '<div class="text-center py-2 text-muted">加载中...</div>';
    }
  }

  /**
   * 清除分页
   */
  clearPagination() {
    const paginationContainer = this.getPaginationContainer();
    if (paginationContainer) {
      paginationContainer.innerHTML = '';
    }
  }

  /**
   * 获取或创建分页容器
   */
  getPaginationContainer() {
    let container = document.getElementById('records-pagination');
    if (!container) {
      container = document.createElement('div');
      container.id = 'records-pagination';
      container.className = 'pagination-container mt-6';

      // 插入到记录容器后面
      if (this.recordsContainer && this.recordsContainer.parentNode) {
        this.recordsContainer.parentNode.insertBefore(container, this.recordsContainer.nextSibling);
      }
    }
    return container;
  }

  /**
   * 渲染分页组件
   */
  renderPagination(pagination) {
    if (!pagination) return;

    const container = this.getPaginationContainer();
    const { current_page, total_pages, total_count, page_size } = pagination;

    if (total_pages <= 1) {
      container.innerHTML = '';
      return;
    }

    // 计算显示的页码范围
    const maxVisiblePages = 5;
    let startPage = Math.max(1, current_page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(total_pages, startPage + maxVisiblePages - 1);

    // 调整起始页
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    let paginationHTML = `
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="text-muted">共 ${total_count} 条记录</span>
          <select class="page-size-selector" onchange="uploadPage.changePageSize(this.value)">
            <option value="10" ${page_size === 10 ? 'selected' : ''}>每页 10 条</option>
            <option value="20" ${page_size === 20 ? 'selected' : ''}>每页 20 条</option>
            <option value="50" ${page_size === 50 ? 'selected' : ''}>每页 50 条</option>
          </select>
        </div>
        <div class="pagination-controls">
    `;

    // 上一页按钮
    if (current_page > 1) {
      paginationHTML += `
        <button class="pagination-btn" onclick="uploadPage.loadRecords(${current_page - 1})">
          <span>‹ 上一页</span>
        </button>
      `;
    }

    // 第一页
    if (startPage > 1) {
      paginationHTML += `
        <button class="pagination-btn" onclick="uploadPage.loadRecords(1)">1</button>
      `;
      if (startPage > 2) {
        paginationHTML += '<span class="pagination-ellipsis">...</span>';
      }
    }

    // 页码按钮
    for (let i = startPage; i <= endPage; i++) {
      const isActive = i === current_page;
      paginationHTML += `
        <button class="pagination-btn ${isActive ? 'active' : ''}"
                onclick="uploadPage.loadRecords(${i})"
                ${isActive ? 'disabled' : ''}>
          ${i}
        </button>
      `;
    }

    // 最后一页
    if (endPage < total_pages) {
      if (endPage < total_pages - 1) {
        paginationHTML += '<span class="pagination-ellipsis">...</span>';
      }
      paginationHTML += `
        <button class="pagination-btn" onclick="uploadPage.loadRecords(${total_pages})">
          ${total_pages}
        </button>
      `;
    }

    // 下一页按钮
    if (current_page < total_pages) {
      paginationHTML += `
        <button class="pagination-btn" onclick="uploadPage.loadRecords(${current_page + 1})">
          <span>下一页 ›</span>
        </button>
      `;
    }

    paginationHTML += `
        </div>
      </div>
    `;

    container.innerHTML = paginationHTML;
  }

  /**
   * 改变每页显示数量
   */
  changePageSize(newPageSize) {
    this.loadRecords(1, parseInt(newPageSize));
  }

  /**
   * 渲染上传记录
   */
  renderRecords(records) {
    if (!records || records.length === 0) {
      this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无上传记录</div>';
      return;
    }

    const recordsHTML = records.map(record => {


      // 处理时间显示
      const createdAt = record.created_at.$date || record.created_at;
      const timeDisplay = formatDateTime(createdAt);

      // 处理错误信息显示
      let errorDisplay = '';
      if (record.status === 'failed') {
        const errorInfo = record.error_details || record.upload_result || null;

        if (errorInfo && typeof errorInfo === 'object') {
          // 详细错误信息
          const errorTitle = errorInfo.title || '上传失败';
          const errorMessage = errorInfo.message || '未知错误';
          const errorSolution = errorInfo.solution || '请检查配置';

          errorDisplay = `
            <div class="mt-3 p-4 bg-error-light border border-error-200 rounded-lg">
              <div class="mb-2">
                <strong class="text-error">❌ ${errorTitle}:</strong>
                <span class="text-error-600">${errorMessage}</span>
              </div>
              <div class="mb-2">
                <strong class="text-error">💡 解决方案:</strong>
                <span class="text-error-600">${errorSolution}</span>
              </div>
              ${errorInfo.details ? `
                <details class="mt-2">
                  <summary class="cursor-pointer text-error-700 hover:text-error-800">🔍 查看详细信息</summary>
                  <pre class="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">${errorInfo.details}</pre>
                </details>
              ` : ''}
              ${errorInfo.raw_error ? `
                <details class="mt-2">
                  <summary class="cursor-pointer text-error-700 hover:text-error-800">🔍 Apple 完整错误信息</summary>
                  <pre class="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">${errorInfo.raw_error}</pre>
                </details>
              ` : ''}
            </div>
          `;
        } else {
          // 简单错误信息
          const simpleError = record.error ||
                            (record.upload_result && record.upload_result.message) ||
                            (record.error_details && record.error_details.message) ||
                            '上传失败';
          errorDisplay = `
            <div class="mt-3 p-3 bg-error-light text-error rounded">
              <strong>❌ 上传失败:</strong> ${simpleError}
            </div>
          `;
        }
      }

      return `
        <div class="card record-card status-${record.status} mb-4">
          <div class="card-body">
            <!-- 记录头部 -->
            <div class="record-header">
              <!-- 应用图标 -->
              <div class="record-icon">
                ${this.renderRecordIcon(record)}
              </div>

              <!-- 应用信息 -->
              <div class="record-info">
                <h4 class="record-title">${record.app_name || record.filename || '未知应用'}</h4>
                <p class="record-subtitle">${record.bundle_id || '未知Bundle ID'}</p>
              </div>

              <!-- 状态标签 -->
              <div class="record-status">
                <span class="badge badge-${this.getStatusColor(record.status)}">${this.getStatusText(record.status)}</span>
              </div>
            </div>

            <!-- 详细信息网格 -->
            <div class="record-meta">
              <div><strong>版本:</strong> <span>${record.version ? `${record.version} (${record.build || '-'})` : '-'}</span></div>
              <div><strong>Bundle ID:</strong> <span>${record.bundle_id || '未知'}</span></div>
              <div><strong>大小:</strong> <span>${formatFileSize(record.file_size || 0)}</span></div>
              <div><strong>上传时间:</strong> <span>${timeDisplay}</span></div>
              <div><strong>认证方式:</strong> <span>${record.auth_method === 'api_key' ? 'API Key' : 'Apple ID'}</span></div>
              ${record.workflow_ip ? `
                <div><strong>IP地址:</strong> <span class="record-ip">${record.workflow_ip}</span></div>
              ` : ''}
              ${auth.isAdmin() && record.username ? `
                <div><strong>用户:</strong> <span class="record-username">${record.username}</span></div>
              ` : ''}
            </div>

            ${record.release_notes ? `
              <div class="record-meta">
                <div><strong>发布说明:</strong> <span>${record.release_notes}</span></div>
              </div>
            ` : ''}

            ${record.parsing_error ? `
              <div class="record-meta">
                <div><strong>解析状态:</strong> <span class="text-warning">⚠️ ${record.parsing_error}</span></div>
              </div>
            ` : ''}

            ${errorDisplay}

            <!-- 操作按钮 -->
            <div class="record-actions">
              ${record.status === 'completed' && record.testflight_url ? `
                <a href="${record.testflight_url}" target="_blank" class="btn btn-primary btn-sm">
                  🚀 TestFlight 链接
                </a>
              ` : ''}

              <!-- 删除按钮（所有记录都有） -->
              <button class="btn btn-error btn-sm" onclick="window.uploadPage.deleteUploadRecord('${record._id}')">
                🗑️ 删除记录
              </button>
            </div>
          </div>
        </div>
      `;
    }).join('');

    this.recordsContainer.innerHTML = recordsHTML;
  }

  /**
   * 渲染记录图标
   */
  renderRecordIcon(record) {
    if (!record.icon_base64) {
      console.log('No icon_base64 data for record:', record.app_name || record.filename);
      return '<span class="text-2xl">📱</span>';
    }

    try {
      // 验证base64数据
      const base64Data = record.icon_base64.trim();
      console.log('Icon base64 length:', base64Data.length, 'for record:', record.app_name || record.filename);

      if (!base64Data || base64Data.length < 10) {
        console.warn('Invalid base64 data length for record:', record.app_name || record.filename);
        return '<span class="text-2xl">📱</span>';
      }

      // 检查是否已经包含data URL前缀
      let iconSrc;
      if (base64Data.startsWith('data:image/')) {
        iconSrc = base64Data;
        console.log('Using complete data URL for record:', record.app_name || record.filename);
      } else {
        iconSrc = `data:image/png;base64,${base64Data}`;
        console.log('Adding data URL prefix for record:', record.app_name || record.filename);
      }

      return `<img src="${iconSrc}" alt="App Icon" onerror="console.error('Image load failed for ${record.app_name || record.filename}'); this.style.display='none'; this.nextElementSibling.style.display='inline';">
              <span class="text-2xl" style="display: none;">📱</span>`;
    } catch (error) {
      console.warn('图标渲染失败:', error, 'for record:', record.app_name || record.filename);
      return '<span class="text-2xl">📱</span>';
    }
  }

  /**
   * 获取状态颜色
   */
  getStatusColor(status) {
    const colors = {
      pending: 'warning',
      processing: 'primary',
      completed: 'success',
      failed: 'error'
    };
    return colors[status] || 'secondary';
  }

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const texts = {
      pending: '等待中',
      processing: '处理中',
      completed: '已完成',
      failed: '失败'
    };
    return texts[status] || '未知';
  }

  /**
   * 显示上传进度面板
   */
  showUploadProgressPanel() {
    // 隐藏所有步骤面板
    const panels = document.querySelectorAll('.step-panel');
    panels.forEach(panel => panel.classList.remove('active'));

    // 显示上传进度面板
    const progressPanel = document.getElementById('upload-progress-panel');
    if (progressPanel) {
      progressPanel.classList.add('active');
    }

    // 隐藏步骤导航
    const navigation = document.querySelector('.step-navigation');
    if (navigation) {
      navigation.style.display = 'none';
    }

    // 初始化进度内容
    const progressContent = document.getElementById('modal-upload-progress-content');
    if (progressContent) {
      progressContent.innerHTML = `
        <div class="upload-progress-container">
          <div class="progress-main">
            <div class="progress-bar-container">
              <div class="progress-bar" id="upload-progress-bar" style="width: 0%"></div>
            </div>
            <div class="progress-info">
              <div class="progress-text" id="upload-progress-text">准备开始上传...</div>
              <div class="progress-percentage" id="upload-progress-percentage">0%</div>
            </div>
          </div>

          <div class="progress-steps" id="upload-progress-steps">
            <div class="progress-step active" data-step="preparing">
              <div class="step-icon">📋</div>
              <div class="step-text">准备上传</div>
            </div>
            <div class="progress-step" data-step="uploading">
              <div class="step-icon">📤</div>
              <div class="step-text">上传文件</div>
            </div>
            <div class="progress-step" data-step="processing">
              <div class="step-icon">⚙️</div>
              <div class="step-text">处理中</div>
            </div>
            <div class="progress-step" data-step="completed">
              <div class="step-icon">✅</div>
              <div class="step-text">完成</div>
            </div>
          </div>

          <div class="progress-details" id="upload-progress-details">
            <div class="detail-item">
              <span class="detail-label">文件名称:</span>
              <span class="detail-value">${this.modalCurrentFile ? this.modalCurrentFile.name : '未知'}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">文件大小:</span>
              <span class="detail-value" id="upload-total-size">${this.modalCurrentFile ? formatFileSize(this.modalCurrentFile.size) : '未知'}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">已上传:</span>
              <span class="detail-value" id="upload-uploaded-size">0 MB</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">上传速度:</span>
              <span class="detail-value" id="upload-speed">计算中...</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">已用时间:</span>
              <span class="detail-value" id="upload-elapsed">0秒</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">剩余时间:</span>
              <span class="detail-value" id="upload-remaining">计算中...</span>
            </div>
          </div>
        </div>
      `;
    }
  }

  /**
   * 更新上传进度
   */
  updateUploadProgress(percentage, message, step = null) {
    // 更新进度条
    const progressBar = document.getElementById('upload-progress-bar');
    if (progressBar) {
      progressBar.style.width = percentage + '%';
    }

    // 更新进度文本
    const progressText = document.getElementById('upload-progress-text');
    if (progressText) {
      progressText.textContent = message;
    }

    // 更新百分比
    const progressPercentage = document.getElementById('upload-progress-percentage');
    if (progressPercentage) {
      progressPercentage.textContent = percentage + '%';
    }

    // 更新步骤状态
    if (step) {
      const steps = document.querySelectorAll('.progress-step');
      steps.forEach(stepEl => {
        const stepName = stepEl.getAttribute('data-step');
        if (stepName === step) {
          stepEl.classList.add('active');
        } else {
          stepEl.classList.remove('active');
        }
      });

      // 如果是失败状态，添加失败样式
      if (step === 'failed') {
        const failedStep = document.querySelector('.progress-step[data-step="failed"]');
        if (!failedStep) {
          // 如果没有失败步骤，创建一个
          const stepsContainer = document.getElementById('upload-progress-steps');
          if (stepsContainer) {
            stepsContainer.innerHTML += `
              <div class="progress-step active" data-step="failed">
                <div class="step-icon">❌</div>
                <div class="step-text">失败</div>
              </div>
            `;
          }
        }
      }
    }
  }

  /**
   * 上传文件（不解析）- 新的文件上传方法
   */
  uploadFileOnly(formData, startTime, initialLastLoaded, initialLastTime) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      let lastLoaded = initialLastLoaded;
      let lastTime = initialLastTime;

      // 上传进度监听
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const currentTime = Date.now();
          const currentLoaded = event.loaded;
          const total = event.total;

          // 计算进度百分比
          const percentCompleted = Math.round((currentLoaded * 100) / total);

          // 计算上传速度（每0.5秒更新一次）
          const timeDiff = (currentTime - lastTime) / 1000; // 秒
          const loadedDiff = currentLoaded - lastLoaded;

          if (timeDiff > 0.5) {
            const speed = loadedDiff / timeDiff; // 字节/秒
            const speedText = this.formatSpeed(speed);

            // 计算剩余时间
            const remainingBytes = total - currentLoaded;
            const remainingTime = speed > 0 ? remainingBytes / speed : 0;
            const remainingText = this.formatTime(remainingTime);

            // 计算已用时间
            const elapsedTime = (currentTime - startTime) / 1000;
            const elapsedText = this.formatTime(elapsedTime);

            // 更新进度显示
            this.updateUploadProgress(
              10 + (percentCompleted * 0.6), // 10-70%用于文件上传
              `📤 上传文件中... ${percentCompleted}%`,
              'uploading'
            );

            // 更新详细信息
            this.updateUploadDetails({
              uploaded: formatFileSize(currentLoaded),
              total: formatFileSize(total),
              speed: speedText,
              remaining: remainingText,
              elapsed: elapsedText,
              percent: percentCompleted
            });

            lastLoaded = currentLoaded;
            lastTime = currentTime;
          }
        }
      });

      // 请求完成监听
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve({ data: response, status: xhr.status });
          } catch (error) {
            reject(new Error('响应解析失败'));
          }
        } else {
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
        }
      });

      // 错误监听
      xhr.addEventListener('error', () => {
        reject(new Error('网络请求失败'));
      });

      // 超时监听
      xhr.addEventListener('timeout', () => {
        reject(new Error('请求超时'));
      });

      // 配置请求 - 使用新的文件上传端点
      xhr.open('POST', 'https://api.ios.xxyx.cn/api/upload/file');
      xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('token')}`);
      xhr.timeout = 300000; // 5分钟超时

      // 发送请求
      xhr.send(formData);
    });
  }

  /**
   * 使用XMLHttpRequest实现带进度监听的文件上传（旧方法，保留用于兼容）
   */
  uploadWithProgress(formData, startTime, initialLastLoaded, initialLastTime) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      let lastLoaded = initialLastLoaded;
      let lastTime = initialLastTime;

      // 上传进度监听
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const currentTime = Date.now();
          const currentLoaded = event.loaded;
          const total = event.total;

          // 计算进度百分比
          const percentCompleted = Math.round((currentLoaded * 100) / total);

          // 计算上传速度（每0.5秒更新一次）
          const timeDiff = (currentTime - lastTime) / 1000; // 秒
          const loadedDiff = currentLoaded - lastLoaded;

          if (timeDiff > 0.5) {
            const speed = loadedDiff / timeDiff; // 字节/秒
            const speedText = this.formatSpeed(speed);

            // 计算剩余时间
            const remainingBytes = total - currentLoaded;
            const remainingTime = speed > 0 ? remainingBytes / speed : 0;
            const remainingText = this.formatTime(remainingTime);

            // 计算已用时间
            const elapsedTime = (currentTime - startTime) / 1000;
            const elapsedText = this.formatTime(elapsedTime);

            // 更新进度显示
            this.updateUploadProgress(
              10 + (percentCompleted * 0.6), // 10-70%用于文件上传
              `📤 上传文件中... ${percentCompleted}%`,
              'uploading'
            );

            // 更新详细信息
            this.updateUploadDetails({
              uploaded: formatFileSize(currentLoaded),
              total: formatFileSize(total),
              speed: speedText,
              remaining: remainingText,
              elapsed: elapsedText,
              percent: percentCompleted
            });

            lastLoaded = currentLoaded;
            lastTime = currentTime;
          }
        }
      });

      // 请求完成监听
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve({ data: response, status: xhr.status });
          } catch (error) {
            reject(new Error('响应解析失败'));
          }
        } else {
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
        }
      });

      // 错误监听
      xhr.addEventListener('error', () => {
        reject(new Error('网络请求失败'));
      });

      // 超时监听
      xhr.addEventListener('timeout', () => {
        reject(new Error('请求超时'));
      });

      // 配置请求
      xhr.open('POST', 'https://api.ios.xxyx.cn/api/upload/parse-ipa');
      xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('token')}`);
      xhr.timeout = 300000; // 5分钟超时

      // 发送请求
      xhr.send(formData);
    });
  }

  /**
   * 显示上传错误
   */
  showUploadError(message, details = null) {
    console.error('Upload error:', message, details);

    // 更新进度显示为错误状态
    this.updateUploadProgress(0, '❌ 上传失败', 'failed');

    // 显示详细错误信息
    const progressContent = document.getElementById('modal-upload-progress-content');
    if (progressContent) {
      progressContent.innerHTML = `
        <div class="upload-error-container">
          <div class="error-icon">❌</div>
          <div class="error-title">上传失败</div>
          <div class="error-message">${message}</div>

          ${details ? `
            <div class="error-details">
              <div class="error-details-toggle" onclick="this.nextElementSibling.style.display = this.nextElementSibling.style.display === 'none' ? 'block' : 'none'">
                📋 查看详细信息
              </div>
              <div class="error-details-content" style="display: none;">
                <pre>${JSON.stringify(details, null, 2)}</pre>
              </div>
            </div>
          ` : ''}

          <div class="error-actions">
            <button class="btn btn-secondary" onclick="location.reload()">
              🔄 刷新页面
            </button>
            <button class="btn btn-primary" onclick="window.uploadPage.closeModalAndRefresh()">
              ✅ 我知道了
            </button>
          </div>

          <div class="error-tips">
            <h4>💡 可能的解决方案：</h4>
            <ul>
              <li>检查网络连接是否正常</li>
              <li>确认认证信息是否正确</li>
              <li>检查IPA文件是否有效</li>
              <li>稍后重试或联系技术支持</li>
            </ul>
          </div>
        </div>
      `;
    }

    // 启用关闭按钮
    this.enableModalClose();

    // 不显示alert，错误信息已经在界面中详细显示了
  }

  /**
   * 删除上传记录
   */
  async deleteUploadRecord(recordId) {
    // 确认删除
    const confirmed = await notifications.confirmDelete('这条上传记录', {
      message: '确定要删除这条上传记录吗？删除后将无法恢复，相关文件也会被删除。'
    });
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.delete(`/api/upload/records/${recordId}`);

      if (response.data.success) {
        notifications.success('上传记录删除成功');
        // 刷新记录列表
        this.loadRecords(this.currentPage);
      } else {
        throw new Error(response.data.error || '删除失败');
      }
    } catch (error) {
      console.error('Delete error:', error);
      const errorMessage = error.message || '删除失败，请重试';
      notifications.error(errorMessage);
    }
  }
}

// 创建页面实例
new UploadPage();
