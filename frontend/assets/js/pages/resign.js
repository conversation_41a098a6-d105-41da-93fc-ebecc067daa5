/**
 * 重签页面模块
 * 处理IPA文件重签和记录管理
 */

import api from '../core/api.js';
import auth from '../core/auth.js';
import { createFormField } from '../components/Form.js';
import { createButton } from '../components/Button.js';
import { formatFileSize, formatDateTime, generateQRCodeURL } from '../core/utils.js';
import notifications from '../core/notifications.js';

class ResignPage {
  constructor() {
    this.recordsContainer = null;
    this.currentFile = null;
    this.isInitialized = false;
    this.currentPage = 1;
    this.currentPageSize = 10;

    this.init();
  }

  /**
   * 初始化页面
   */
  init() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  /**
   * 设置页面
   */
  setup() {
    this.recordsContainer = document.getElementById('resign-records-container');

    if (!this.recordsContainer) {
      console.error('Resign page containers not found');
      return;
    }

    this.setupEventListeners();

    // 设置全局引用，用于分页按钮调用
    window.resignPage = this;

    this.isInitialized = true;

    // 监听路由变化
    window.addEventListener('router:navigated', (e) => {
      if (e.detail.route.path === '/resign') {
        this.loadRecords();
      }
    });
  }

  /**
   * 显示重签弹窗
   */
  showResignModal() {
    // 创建模态框背景
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop active';

    // 创建模态框容器
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-content" style="max-width: 800px; width: 90vw;">
        <div class="modal-header">
          <h3 class="modal-title">🔐 添加重签任务</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div id="resign-form-container">
            <!-- 重签表单将在这里生成 -->
          </div>
        </div>
      </div>
    `;

    // 添加到页面
    document.body.appendChild(backdrop);
    document.body.appendChild(modal);

    // 确保正确的层级
    backdrop.style.zIndex = '1040';
    modal.style.zIndex = '1050';

    // 创建重签表单
    this.createResignForm(modal.querySelector('#resign-form-container'));

    // 绑定关闭事件
    const closeModal = () => {
      backdrop.remove();
      modal.remove();
    };

    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    backdrop.addEventListener('click', closeModal);

    // ESC键关闭
    const handleEsc = (e) => {
      if (e.key === 'Escape') {
        closeModal();
        document.removeEventListener('keydown', handleEsc);
      }
    };
    document.addEventListener('keydown', handleEsc);

    // 保存模态框引用
    this.currentModal = { backdrop, modal, closeModal };
  }

  /**
   * 创建重签表单
   */
  createResignForm(container) {
    // IPA文件上传
    const ipaFileField = createFormField({
      type: 'file',
      name: 'ipa_file',
      label: 'IPA文件',
      accept: '.ipa',
      required: true,
      help: '支持最大2GB的IPA文件'
    });

    // 新Bundle ID
    const bundleIdField = createFormField({
      type: 'text',
      name: 'new_bundle_id',
      label: '新Bundle ID（可选）',
      placeholder: 'com.yourcompany.newapp',
      help: '留空则保持原Bundle ID'
    });

    // 新应用名称
    const appNameField = createFormField({
      type: 'text',
      name: 'new_app_name',
      label: '新应用名称（可选）',
      placeholder: '新应用名称',
      help: '留空则保持原应用名称'
    });

    // 强制重签选项
    const forceField = createFormField({
      type: 'checkbox',
      name: 'force',
      label: '',
      value: ['force'],
      options: [
        { value: 'force', label: '强制重签（推荐）' }
      ]
    });

    // 提交按钮
    const submitButton = createButton({
      text: '🔐 开始重签',
      type: 'purple',
      size: 'lg',
      block: true,
      disabled: true,
      onClick: () => this.handleSubmit()
    });

    // 组装表单
    const formHTML = `
      <div class="form">
        <div id="ipa-file-container"></div>

        <div id="ipa-info" style="display: none;">
          <div class="card card-filled mt-4">
            <div class="card-body">
              <h4 class="text-base font-medium mb-3">📱 应用信息</h4>
              <div class="flex items-center gap-4 mb-4">
                <div id="app-icon" class="flex-shrink-0">
                  <span class="text-2xl">📱</span>
                </div>
                <div class="flex-grow">
                  <div class="font-medium" id="app-name">-</div>
                  <div class="text-sm text-gray-600" id="bundle-id">-</div>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div><strong>版本号:</strong> <span id="app-version">-</span></div>
                <div><strong>文件大小:</strong> <span id="file-size">-</span></div>
              </div>
            </div>
          </div>
        </div>

        <div id="bundle-id-container"></div>
        <div id="app-name-container"></div>
        <div id="force-container"></div>

        <div class="form-actions">
          <div id="submit-button-container"></div>
        </div>

        <div id="resign-progress" style="display: none;">
          <div class="card card-filled mt-6">
            <div class="card-body">
              <h4 class="text-base font-medium mb-3">🔐 重签进度</h4>
              <div class="progress mb-2">
                <div class="progress-bar" id="progress-fill" style="width: 0%"></div>
              </div>
              <div class="flex justify-between text-sm text-secondary">
                <span id="progress-text">准备开始重签...</span>
                <span id="progress-percentage">0%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    container.innerHTML = formHTML;

    // 挂载表单字段
    ipaFileField.mount('#ipa-file-container');
    bundleIdField.mount('#bundle-id-container');
    appNameField.mount('#app-name-container');
    forceField.mount('#force-container');
    submitButton.mount('#submit-button-container');

    // 保存字段引用
    this.formFields = {
      ipaFile: ipaFileField,
      bundleId: bundleIdField,
      appName: appNameField,
      force: forceField
    };

    this.submitButton = submitButton;

    // 默认选中强制重签
    forceField.setValue(['force']);

    // 绑定文件上传事件
    setTimeout(() => {
      const fileInput = container.querySelector('input[name="ipa_file"]');
      if (fileInput) {
        fileInput.addEventListener('change', (e) => {
          this.handleFileChange(e.target.files[0]);
        });
      }
    }, 100);
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 刷新记录按钮
    const refreshBtn = document.getElementById('refresh-resign-records-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadRecords());
    }

    // 添加重签任务按钮
    const addTaskBtn = document.getElementById('add-resign-task-btn');
    if (addTaskBtn) {
      addTaskBtn.addEventListener('click', () => this.showResignModal());
    }
  }

  /**
   * 处理文件变化
   */
  async handleFileChange(file) {
    this.currentFile = file;

    if (file) {
      document.getElementById('file-size').textContent = formatFileSize(file.size);
      document.getElementById('ipa-info').style.display = 'block';

      // 开始客户端解析IPA文件
      await this.parseIPAClientSide(file);
    } else {
      document.getElementById('ipa-info').style.display = 'none';
    }

    this.submitButton.setDisabled(!file);
  }

  /**
   * 客户端解析IPA文件
   */
  async parseIPAClientSide(file) {
    try {
      // 显示解析状态
      document.getElementById('app-name').textContent = '🔍 正在解析IPA文件...';

      // 使用客户端IPA解析器
      if (!window.XIOSIPAParser) {
        throw new Error('IPA解析器未加载');
      }

      // 解析IPA文件
      const parsedInfo = await window.XIOSIPAParser.parseIPA(file);



      // 保存解析结果
      this.currentFileInfo = parsedInfo;

      // 更新显示
      this.updateFileInfoDisplay(parsedInfo);

    } catch (error) {
      console.error('Client-side IPA parsing failed:', error);

      // 显示解析失败，但仍允许继续
      document.getElementById('app-name').textContent = `⚠️ ${file.name.replace('.ipa', '')} (解析失败)`;

      // 创建基本信息作为备选
      this.currentFileInfo = {
        bundle_id: 'unknown.bundle.id',
        app_name: file.name.replace('.ipa', ''),
        version: '1.0.0',
        build: '1',
        minimum_os_version: 'Unknown',
        supported_devices: [],
        required_capabilities: [],
        icon: null,
        file_size: file.size,
        file_name: file.name,
        parsing_error: error.message
      };
    }
  }

  /**
   * 更新文件信息显示
   */
  updateFileInfoDisplay(parsedInfo) {
    // 更新应用名称
    document.getElementById('app-name').textContent = parsedInfo.app_name || 'Unknown App';

    // 更新Bundle ID
    const bundleIdElement = document.getElementById('bundle-id');
    if (bundleIdElement) {
      bundleIdElement.textContent = parsedInfo.bundle_id || 'Unknown';
    }

    // 更新版本号
    const versionElement = document.getElementById('app-version');
    if (versionElement) {
      versionElement.textContent = `${parsedInfo.version || 'Unknown'} (${parsedInfo.build || 'Unknown'})`;
    }

    // 更新应用图标
    const iconElement = document.getElementById('app-icon');
    if (iconElement && parsedInfo.icon) {
      iconElement.innerHTML = `<img src="${parsedInfo.icon}" alt="App Icon" style="width: 48px; height: 48px; border-radius: 8px;">`;
    } else if (iconElement) {
      iconElement.innerHTML = '<span class="text-2xl">📱</span>';
    }
  }

  /**
   * 处理表单提交
   */
  async handleSubmit() {
    try {
      this.submitButton.setLoading(true);
      this.showResignProgress();

      // 验证文件
      if (!this.currentFile) {
        notifications.error('请选择IPA文件');
        return;
      }

      // 准备表单数据
      const formData = new FormData();
      formData.append('ipa', this.currentFile);

      const bundleId = this.formFields.bundleId.getValue();
      if (bundleId) {
        formData.append('new_bundle_id', bundleId);
      }

      const appName = this.formFields.appName.getValue();
      if (appName) {
        formData.append('new_app_name', appName);
      }

      const force = this.formFields.force.getValue();
      if (force.includes('force')) {
        formData.append('force', 'true');
      }

      // 添加客户端解析的IPA信息
      if (this.currentFileInfo) {
        formData.append('bundle_id', this.currentFileInfo.bundle_id);
        formData.append('app_name', this.currentFileInfo.app_name);
        formData.append('version', this.currentFileInfo.version);
        formData.append('build', this.currentFileInfo.build);
        formData.append('minimum_os_version', this.currentFileInfo.minimum_os_version);

        if (this.currentFileInfo.icon) {
          formData.append('icon_base64', this.currentFileInfo.icon);
        }

        if (this.currentFileInfo.parsing_error) {
          formData.append('parsing_error', this.currentFileInfo.parsing_error);
        }
      }

      // 添加默认配置
      formData.append('auto_process', 'true');

      // 调试信息
      console.log('Submitting resign task with:', {
        file: this.currentFile.name,
        size: this.currentFile.size,
        bundleId: bundleId,
        appName: appName,
        force: force.includes('force')
      });

      // 模拟重签进度
      this.simulateResignProgress();

      // 发送重签请求
      const response = await api.upload('/api/resign/submit', formData);

      if (response.data.success) {
        // 不显示alert，直接关闭弹窗并刷新记录
        setTimeout(() => {
          if (this.currentModal) {
            this.currentModal.closeModal();
          }
          this.loadRecords();
        }, 1500);
      } else {
        notifications.error('重签失败：' + (response.data.message || '未知错误'));
      }

    } catch (error) {
      console.error('Resign failed:', error);
      let errorMessage = '重签失败';

      if (error.status === 400) {
        // 尝试解析错误响应
        try {
          if (error.response) {
            const errorData = await error.response.json();
            errorMessage = errorData.error || errorData.message || '请求参数错误';
          }
        } catch (parseError) {
          errorMessage = '请求参数错误';
        }
      } else if (error.status === 401) {
        errorMessage = '认证失败，请重新登录';
      } else if (error.status === 413) {
        errorMessage = '文件太大，请选择小于2GB的文件';
      } else if (error.message) {
        errorMessage = error.message;
      }

      notifications.error(errorMessage);
    } finally {
      this.submitButton.setLoading(false);
      this.hideResignProgress();
    }
  }

  /**
   * 模拟重签进度
   */
  simulateResignProgress() {
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    const progressPercentage = document.getElementById('progress-percentage');

    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 10;
      if (progress > 100) progress = 100;

      if (progressFill) progressFill.style.width = `${progress}%`;
      if (progressPercentage) progressPercentage.textContent = `${Math.round(progress)}%`;

      if (progress < 20) {
        if (progressText) progressText.textContent = '正在解析IPA文件...';
      } else if (progress < 40) {
        if (progressText) progressText.textContent = '正在修改Bundle ID...';
      } else if (progress < 60) {
        if (progressText) progressText.textContent = '正在重新签名...';
      } else if (progress < 80) {
        if (progressText) progressText.textContent = '正在生成安装页面...';
      } else if (progress < 100) {
        if (progressText) progressText.textContent = '即将完成...';
      } else {
        if (progressText) progressText.textContent = '重签完成！';
        clearInterval(interval);
      }
    }, 300);
  }

  /**
   * 显示重签进度
   */
  showResignProgress() {
    const progressContainer = document.getElementById('resign-progress');
    if (progressContainer) {
      progressContainer.style.display = 'block';
    }
  }

  /**
   * 隐藏重签进度
   */
  hideResignProgress() {
    const progressContainer = document.getElementById('resign-progress');
    if (progressContainer) {
      progressContainer.style.display = 'none';
    }
  }



  /**
   * 加载重签记录（支持分页）
   */
  async loadRecords(page = 1, pageSize = 10) {
    if (!this.recordsContainer) return;

    try {
      // 保存当前分页信息
      this.currentPage = page;
      this.currentPageSize = pageSize;

      // 只在第一页时显示加载中，其他页面显示在分页区域
      if (page === 1) {
        this.recordsContainer.innerHTML = '<div class="text-center py-8">加载中...</div>';
      } else {
        this.showPaginationLoading();
      }

      // 构建请求URL
      let url = '/api/resign/records';
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('page_size', pageSize.toString());
      url += '?' + params.toString();

      const response = await api.get(url);

      if (response.data.success) {
        const { records, pagination } = response.data;

        if (records && records.length > 0) {
          this.renderRecords(records);
          this.renderPagination(pagination);
        } else if (page === 1) {
          this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无重签记录</div>';
          this.clearPagination();
        }
      } else {
        if (page === 1) {
          this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无重签记录</div>';
        }
        this.clearPagination();
      }
    } catch (error) {
      console.error('Failed to load resign records:', error);

      if (error.status === 401) {
        this.recordsContainer.innerHTML = '<div class="text-center py-8 text-error">认证失败，请重新登录</div>';
      } else {
        this.recordsContainer.innerHTML = '<div class="text-center py-8 text-error">加载失败，请稍后重试</div>';
      }
      this.clearPagination();
    }
  }

  /**
   * 显示分页加载状态
   */
  showPaginationLoading() {
    const paginationContainer = this.getPaginationContainer();
    if (paginationContainer) {
      paginationContainer.innerHTML = '<div class="text-center py-2 text-muted">加载中...</div>';
    }
  }

  /**
   * 清除分页
   */
  clearPagination() {
    const paginationContainer = this.getPaginationContainer();
    if (paginationContainer) {
      paginationContainer.innerHTML = '';
    }
  }

  /**
   * 获取或创建分页容器
   */
  getPaginationContainer() {
    let container = document.getElementById('resign-records-pagination');
    if (!container) {
      container = document.createElement('div');
      container.id = 'resign-records-pagination';
      container.className = 'pagination-container mt-6';

      // 插入到记录容器后面
      if (this.recordsContainer && this.recordsContainer.parentNode) {
        this.recordsContainer.parentNode.insertBefore(container, this.recordsContainer.nextSibling);
      }
    }
    return container;
  }

  /**
   * 渲染分页组件
   */
  renderPagination(pagination) {
    if (!pagination) return;

    const container = this.getPaginationContainer();
    const { current_page, total_pages, total_count, page_size } = pagination;

    if (total_pages <= 1) {
      container.innerHTML = '';
      return;
    }

    // 计算显示的页码范围
    const maxVisiblePages = 5;
    let startPage = Math.max(1, current_page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(total_pages, startPage + maxVisiblePages - 1);

    // 调整起始页
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    let paginationHTML = `
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span class="text-muted">共 ${total_count} 条记录</span>
          <select class="page-size-selector" onchange="resignPage.changePageSize(this.value)">
            <option value="10" ${page_size === 10 ? 'selected' : ''}>每页 10 条</option>
            <option value="20" ${page_size === 20 ? 'selected' : ''}>每页 20 条</option>
            <option value="50" ${page_size === 50 ? 'selected' : ''}>每页 50 条</option>
          </select>
        </div>
        <div class="pagination-controls">
    `;

    // 上一页按钮
    if (current_page > 1) {
      paginationHTML += `
        <button class="pagination-btn" onclick="resignPage.loadRecords(${current_page - 1})">
          <span>‹ 上一页</span>
        </button>
      `;
    }

    // 第一页
    if (startPage > 1) {
      paginationHTML += `
        <button class="pagination-btn" onclick="resignPage.loadRecords(1)">1</button>
      `;
      if (startPage > 2) {
        paginationHTML += '<span class="pagination-ellipsis">...</span>';
      }
    }

    // 页码按钮
    for (let i = startPage; i <= endPage; i++) {
      const isActive = i === current_page;
      paginationHTML += `
        <button class="pagination-btn ${isActive ? 'active' : ''}"
                onclick="resignPage.loadRecords(${i})"
                ${isActive ? 'disabled' : ''}>
          ${i}
        </button>
      `;
    }

    // 最后一页
    if (endPage < total_pages) {
      if (endPage < total_pages - 1) {
        paginationHTML += '<span class="pagination-ellipsis">...</span>';
      }
      paginationHTML += `
        <button class="pagination-btn" onclick="resignPage.loadRecords(${total_pages})">
          ${total_pages}
        </button>
      `;
    }

    // 下一页按钮
    if (current_page < total_pages) {
      paginationHTML += `
        <button class="pagination-btn" onclick="resignPage.loadRecords(${current_page + 1})">
          <span>下一页 ›</span>
        </button>
      `;
    }

    paginationHTML += `
        </div>
      </div>
    `;

    container.innerHTML = paginationHTML;
  }

  /**
   * 改变每页显示数量
   */
  changePageSize(newPageSize) {
    this.loadRecords(1, parseInt(newPageSize));
  }

  /**
   * 渲染重签记录
   */
  renderRecords(records) {
    if (!records || records.length === 0) {
      this.recordsContainer.innerHTML = '<div class="text-center py-8 text-muted">暂无重签记录</div>';
      return;
    }

    const recordsHTML = records.map(record => {
      // 处理时间显示
      const createdAt = record.created_at.$date || record.created_at;
      const timeDisplay = formatDateTime(createdAt);

      // 处理错误信息
      let errorDisplay = '';
      if (record.status === 'failed') {
        const errorMessage = record.error || record.error_message || '重签失败';
        errorDisplay = `
          <div class="mt-3 p-3 bg-error-light text-error rounded">
            <strong>❌ 重签失败:</strong> ${errorMessage}
          </div>
        `;
      }

      return `
        <div class="card record-card status-${record.status} mb-4">
          <div class="card-body">
            <!-- 记录头部 -->
            <div class="record-header">
              <!-- 应用图标 -->
              <div class="record-icon">
                ${this.renderRecordIcon(record)}
              </div>

              <!-- 应用信息 -->
              <div class="record-info">
                <h4 class="record-title">${record.app_name || record.original_filename || '未知应用'}</h4>
                <p class="record-subtitle">${record.bundle_id || record.new_bundle_id || ''}</p>
              </div>

              <!-- 状态标签 -->
              <div class="record-status">
                <span class="badge badge-${this.getStatusColor(record.status)}">${this.getStatusText(record.status)}</span>
              </div>
            </div>

            <!-- 详细信息网格 -->
            <div class="record-meta">
              <div><strong>原文件:</strong> <span>${record.original_filename || record.filename || '-'}</span></div>
              <div><strong>文件大小:</strong> <span>${formatFileSize(record.resigned_file_size || 0)}</span></div>
              <div><strong>重签时间:</strong> <span>${timeDisplay}</span></div>
              <div><strong>新Bundle ID:</strong> <span>${record.new_bundle_id || '保持原有'}</span></div>
            </div>

            ${record.new_app_name ? `
              <div class="record-meta">
                <div><strong>新应用名称:</strong> <span>${record.new_app_name}</span></div>
              </div>
            ` : ''}

            ${errorDisplay}

            <!-- 操作按钮 -->
            <div class="record-actions">
              ${record.status === 'success' ? `
                <button class="btn btn-primary btn-sm" onclick="window.resignPage.downloadResignedIpa('${record._id}')">
                  📥 下载重签IPA
                </button>
                ${record.install_url || record.plist_url ? `
                  <a href="${record.install_url || record.plist_url}" class="btn btn-secondary btn-sm" target="_blank">
                    🌐 安装页面
                  </a>
                  <button class="btn btn-ghost btn-sm" onclick="window.resignPage.showQRCode('${record.install_url || record.plist_url}')">
                    📱 二维码
                  </button>
                  <button class="btn btn-outline btn-sm" onclick="window.resignPage.copyInstallLink('${record.install_url || record.plist_url}')">
                    📋 复制链接
                  </button>
                ` : ''}
              ` : ''}

              <!-- 删除按钮（所有记录都有） -->
              <button class="btn btn-error btn-sm" onclick="window.resignPage.deleteResignRecord('${record._id}')">
                🗑️ 删除记录
              </button>
            </div>
          </div>
        </div>
      `;
    }).join('');

    this.recordsContainer.innerHTML = recordsHTML;

    // 将页面实例绑定到window，供按钮调用
    window.resignPage = this;
  }

  /**
   * 渲染记录图标
   */
  renderRecordIcon(record) {
    if (!record.icon_base64) {
      console.log('No icon_base64 data for record:', record.app_name || record.original_filename);
      return '<span class="text-2xl">📱</span>';
    }

    try {
      // 验证base64数据
      const base64Data = record.icon_base64.trim();
      console.log('Icon base64 length:', base64Data.length, 'for record:', record.app_name || record.original_filename);

      if (!base64Data || base64Data.length < 10) {
        console.warn('Invalid base64 data length for record:', record.app_name || record.original_filename);
        return '<span class="text-2xl">📱</span>';
      }

      // 检查是否已经包含data URL前缀
      let iconSrc;
      if (base64Data.startsWith('data:image/')) {
        iconSrc = base64Data;
        console.log('Using complete data URL for record:', record.app_name || record.original_filename);
      } else {
        iconSrc = `data:image/png;base64,${base64Data}`;
        console.log('Adding data URL prefix for record:', record.app_name || record.original_filename);
      }

      return `<img src="${iconSrc}" alt="App Icon" onerror="console.error('Image load failed for ${record.app_name || record.original_filename}'); this.style.display='none'; this.nextElementSibling.style.display='inline';">
              <span class="text-2xl" style="display: none;">📱</span>`;
    } catch (error) {
      console.warn('图标渲染失败:', error, 'for record:', record.app_name || record.original_filename);
      return '<span class="text-2xl">📱</span>';
    }
  }

  /**
   * 获取状态颜色
   */
  getStatusColor(status) {
    const colors = {
      pending: 'warning',
      processing: 'primary',
      success: 'success',
      failed: 'error'
    };
    return colors[status] || 'secondary';
  }

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const texts = {
      pending: '等待中',
      processing: '处理中',
      success: '成功',
      failed: '失败'
    };
    return texts[status] || '未知';
  }

  /**
   * 显示二维码
   */
  showQRCode(url) {
    const qrCodeURL = generateQRCodeURL(url, 250);

    // 创建模态框背景
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop active';

    // 创建模态框容器
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">安装二维码</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body text-center">
          <img src="${qrCodeURL}" alt="安装二维码" style="max-width: 100%; height: auto; border-radius: 8px;">
          <p class="mt-4 text-sm text-secondary">使用手机扫描二维码安装应用</p>
          <div class="mt-4 p-3 bg-gray-100 rounded text-xs break-all">
            ${url}
          </div>
          <button class="btn btn-secondary btn-sm mt-3" onclick="navigator.clipboard.writeText('${url}').then(() => notifications.copySuccess('安装链接'))">
            📋 复制链接
          </button>
        </div>
      </div>
    `;

    // 添加到页面
    document.body.appendChild(backdrop);
    document.body.appendChild(modal);

    // 确保正确的层级
    backdrop.style.zIndex = '1040';
    modal.style.zIndex = '1050';

    // 关闭模态框的函数
    const closeModal = () => {
      backdrop.remove();
      modal.remove();
      document.removeEventListener('keydown', handleEsc);
    };

    // 绑定关闭事件
    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    backdrop.addEventListener('click', closeModal);

    // ESC键关闭
    const handleEsc = (e) => {
      if (e.key === 'Escape') {
        closeModal();
      }
    };
    document.addEventListener('keydown', handleEsc);
  }

  /**
   * 复制安装链接
   */
  async copyInstallLink(url) {
    try {
      await navigator.clipboard.writeText(url);
      notifications.copySuccess('安装链接');
    } catch (error) {
      console.error('Failed to copy link:', error);
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      notifications.copySuccess('安装链接');
    }
  }

  /**
   * 下载重签后的IPA文件
   */
  async downloadResignedIpa(recordId) {
    try {
      // 获取认证token
      const token = auth.getToken();
      if (!token) {
        notifications.warning('请先登录');
        return;
      }

      const url = `${window.location.origin}/api/resign/download/${recordId}`;

      // 使用原生fetch获取文件blob
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 获取文件blob
      const blob = await response.blob();

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `resigned_${recordId}.ipa`;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

    } catch (error) {
      console.error('Download error:', error);
      notifications.error('下载失败，请重试');
    }
  }

  /**
   * 删除重签记录
   */
  async deleteResignRecord(recordId) {
    // 确认删除
    const confirmed = await notifications.confirmDelete('这条重签记录', {
      message: '确定要删除这条重签记录吗？删除后将无法恢复，相关文件也会被删除。'
    });
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.delete(`/api/resign/records/${recordId}`);

      if (response.data.success) {
        notifications.success('重签记录删除成功');
        // 刷新记录列表
        this.loadRecords(this.currentPage);
      } else {
        throw new Error(response.data.error || '删除失败');
      }
    } catch (error) {
      console.error('Delete error:', error);
      const errorMessage = error.message || '删除失败，请重试';
      notifications.error(errorMessage);
    }
  }
}

// 创建页面实例
new ResignPage();
