/**
 * 管理后台页面模块
 * 处理用户管理、激活码管理、GitHub账号管理、证书管理、统计数据和系统管理功能
 */

import api from '../core/api.js';
import auth from '../core/auth.js';
import { createCard } from '../components/Card.js';
import { createButton } from '../components/Button.js';
import { formatDateTime, formatFileSize, formatDuration } from '../core/utils.js';
import notifications from '../core/notifications.js';

class AdminPage {
  constructor() {
    this.container = null;
    this.isInitialized = false;
    this.currentModule = 'dashboard';
    this.modules = new Map();

    this.init();
  }

  /**
   * 初始化页面
   */
  init() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  /**
   * 设置页面
   */
  setup() {
    this.container = document.getElementById('admin-content-container');

    if (!this.container) {
      console.error('Admin page container not found');
      return;
    }

    // 检查管理员权限
    if (!auth.isAdmin()) {
      this.showAccessDenied();
      return;
    }

    this.createAdminInterface();
    this.setupModules();
    this.isInitialized = true;

    // 监听路由变化
    window.addEventListener('router:navigated', (e) => {
      if (e.detail.route.path === '/admin') {
        this.loadDashboardData();
      }
    });
  }

  /**
   * 显示访问拒绝页面
   */
  showAccessDenied() {
    this.container.innerHTML = `
      <div class="text-center py-16">
        <div class="text-6xl mb-4">🚫</div>
        <h2 class="text-2xl font-bold mb-4">访问被拒绝</h2>
        <p class="text-secondary mb-8">您没有权限访问管理后台</p>
        <a href="#/" class="btn btn-primary">返回首页</a>
      </div>
    `;
  }

  /**
   * 创建管理界面
   */
  createAdminInterface() {
    const adminHTML = `
      <div class="admin-dashboard">
        <!-- 管理功能标签页 -->
        <div class="card">
          <div class="card-header">
            <div class="navbar-tabs">
              <div class="navbar-tabs-container">
                <ul class="navbar-tabs-nav">
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link active" data-tab="users">
                      👥 用户管理
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="activation-codes">
                      🎫 激活码管理
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="github-accounts">
                      🐙 GitHub账号
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="certificates">
                      📋 证书管理
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="upload-stats">
                      📊 上传统计
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="resign-stats">
                      📈 重签统计
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="system">
                      ⚙️ 系统设置
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="card-body">
            <!-- 用户管理 -->
            <div id="users-tab" class="admin-tab active">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">用户管理</h3>
                <button class="btn btn-primary" id="add-user-btn">
                  <span>➕</span>
                  <span>创建用户</span>
                </button>
              </div>

              <!-- 用户统计 -->
              <div id="user-stats-container" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <!-- 用户统计卡片 -->
              </div>

              <div id="users-list">
                <div class="text-center py-8">加载中...</div>
              </div>
            </div>

            <!-- 激活码管理 -->
            <div id="activation-codes-tab" class="admin-tab" style="display: none;">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">激活码管理</h3>
                <button class="btn btn-primary" id="create-activation-code-btn">
                  <span>🎫</span>
                  <span>生成激活码</span>
                </button>
              </div>
              <div id="activation-codes-list">
                <div class="text-center py-8">加载中...</div>
              </div>
            </div>

            <!-- GitHub账号管理 -->
            <div id="github-accounts-tab" class="admin-tab" style="display: none;">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">GitHub账号管理</h3>
                <div class="flex gap-2">
                  <button class="btn btn-primary" id="add-github-account-btn">
                    <span>➕</span>
                    <span>添加账号</span>
                  </button>
                  <button class="btn btn-secondary" id="refresh-github-accounts-btn">
                    <span>🔄</span>
                    <span>刷新状态</span>
                  </button>
                </div>
              </div>
              <div id="github-stats-container" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <!-- GitHub统计卡片 -->
              </div>
              <div id="github-accounts-list">
                <div class="text-center py-8">加载中...</div>
              </div>
            </div>

            <!-- 证书管理 -->
            <div id="certificates-tab" class="admin-tab" style="display: none;">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">证书管理</h3>
                <div class="flex gap-2">
                  <button class="btn btn-primary" id="upload-p12-btn">
                    <span>📋</span>
                    <span>上传P12</span>
                  </button>
                  <button class="btn btn-secondary" id="upload-provision-btn">
                    <span>�</span>
                    <span>上传描述文件</span>
                  </button>
                </div>
              </div>
              <!-- 证书配对 -->
              <div id="certificate-pairs-container" class="mb-8">
                <h4 class="text-md font-semibold mb-4">可用证书对</h4>
                <div id="certificate-pairs">
                  <div class="text-center py-4">加载中...</div>
                </div>
              </div>

              <!-- 证书列表 -->
              <div id="certificates-list-container">
                <h4 class="text-md font-semibold mb-4">证书列表</h4>
                <div id="certificates-list">
                  <div class="text-center py-4">加载中...</div>
                </div>
              </div>
            </div>

            <!-- 上传统计 -->
            <div id="upload-stats-tab" class="admin-tab" style="display: none;">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">上传统计</h3>
                <div class="flex gap-2">
                  <button class="btn btn-secondary" id="refresh-upload-stats-btn">
                    <span>🔄</span>
                    <span>刷新数据</span>
                  </button>
                  <button class="btn btn-warning" id="batch-delete-upload-btn">
                    <span>🗑️</span>
                    <span>批量删除</span>
                  </button>
                </div>
              </div>
              <div id="upload-stats-container" class="mb-6">
                <!-- 上传统计卡片 -->
              </div>
              <div class="stats-summary-card">
                <div class="card">
                  <div class="card-body text-center">
                    <h4 class="font-medium mb-2">📊 数据管理</h4>
                    <p class="text-sm text-secondary mb-4">使用批量删除功能清理历史上传记录，优化系统性能</p>
                    <div class="flex gap-2 justify-center">
                      <button class="btn btn-outline btn-sm" onclick="window.adminPage.loadUploadStats()">
                        📈 刷新统计
                      </button>
                      <button class="btn btn-outline btn-sm" onclick="window.adminPage.showBatchDeleteUploadModal()">
                        🧹 清理数据
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 重签统计 -->
            <div id="resign-stats-tab" class="admin-tab" style="display: none;">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">重签统计</h3>
                <div class="flex gap-2">
                  <button class="btn btn-secondary" id="refresh-resign-stats-btn">
                    <span>🔄</span>
                    <span>刷新数据</span>
                  </button>
                  <button class="btn btn-warning" id="batch-delete-resign-btn">
                    <span>🗑️</span>
                    <span>批量删除</span>
                  </button>
                </div>
              </div>
              <div id="resign-stats-container" class="mb-6">
                <!-- 重签统计卡片 -->
              </div>
              <div class="stats-summary-card">
                <div class="card">
                  <div class="card-body text-center">
                    <h4 class="font-medium mb-2">🔐 重签管理</h4>
                    <p class="text-sm text-secondary mb-4">管理重签记录，清理过期或失败的重签任务，保持系统整洁</p>
                    <div class="flex gap-2 justify-center">
                      <button class="btn btn-outline btn-sm" onclick="window.adminPage.loadResignStats()">
                        📊 刷新统计
                      </button>
                      <button class="btn btn-outline btn-sm" onclick="window.adminPage.showBatchDeleteModal()">
                        🧹 清理记录
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系统设置 -->
            <div id="system-tab" class="admin-tab" style="display: none;">
              <h3 class="text-lg font-semibold mb-6">系统设置</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="card card-filled">
                  <div class="card-body">
                    <h4 class="font-medium mb-3">系统信息</h4>
                    <div id="system-info">
                      <div class="text-center py-4">加载中...</div>
                    </div>
                  </div>
                </div>

                <!-- 系统操作功能已删除（后端未实现） -->
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    this.container.innerHTML = adminHTML;
    this.setupTabSwitching();
    this.setupEventListeners();
  }

  /**
   * 设置模块系统
   */
  setupModules() {
    // 注册所有模块
    this.modules.set('users', {
      name: '用户管理',
      loader: () => this.loadUsers(),
      initialized: false
    });

    this.modules.set('activation-codes', {
      name: '激活码管理',
      loader: () => this.loadActivationCodes(),
      initialized: false
    });

    this.modules.set('github-accounts', {
      name: 'GitHub账号管理',
      loader: () => this.loadGitHubAccounts(),
      initialized: false
    });

    this.modules.set('certificates', {
      name: '证书管理',
      loader: () => this.loadCertificates(),
      initialized: false
    });

    this.modules.set('upload-stats', {
      name: '上传统计',
      loader: () => this.loadUploadStats(),
      initialized: false
    });

    this.modules.set('resign-stats', {
      name: '重签统计',
      loader: () => this.loadResignStats(),
      initialized: false
    });

    this.modules.set('system', {
      name: '系统设置',
      loader: () => this.loadSystemInfo(),
      initialized: false
    });
  }

  /**
   * 设置标签页切换
   */
  setupTabSwitching() {
    const tabLinks = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('.admin-tab');

    tabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();

        const targetTab = link.getAttribute('data-tab');

        // 更新标签页状态
        tabLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');

        // 显示对应内容
        tabContents.forEach(content => {
          content.style.display = 'none';
        });

        const targetContent = document.getElementById(`${targetTab}-tab`);
        if (targetContent) {
          targetContent.style.display = 'block';
        }

        // 更新当前模块
        this.currentModule = targetTab;

        // 加载对应数据
        this.loadTabData(targetTab);
      });
    });
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 用户管理按钮
    const addUserBtn = document.getElementById('add-user-btn');
    if (addUserBtn) {
      addUserBtn.addEventListener('click', () => this.showAddUserModal());
    }

    // 激活码管理按钮
    const createActivationCodeBtn = document.getElementById('create-activation-code-btn');
    if (createActivationCodeBtn) {
      createActivationCodeBtn.addEventListener('click', () => this.showCreateActivationCodeModal());
    }

    // GitHub账号管理按钮
    const addGitHubAccountBtn = document.getElementById('add-github-account-btn');
    if (addGitHubAccountBtn) {
      addGitHubAccountBtn.addEventListener('click', () => this.showAddGitHubAccountModal());
    }

    const refreshGitHubAccountsBtn = document.getElementById('refresh-github-accounts-btn');
    if (refreshGitHubAccountsBtn) {
      refreshGitHubAccountsBtn.addEventListener('click', () => this.refreshGitHubAccounts());
    }

    // 证书管理按钮
    const uploadP12Btn = document.getElementById('upload-p12-btn');
    if (uploadP12Btn) {
      uploadP12Btn.addEventListener('click', () => this.showUploadP12Modal());
    }

    const uploadProvisionBtn = document.getElementById('upload-provision-btn');
    if (uploadProvisionBtn) {
      uploadProvisionBtn.addEventListener('click', () => this.showUploadProvisionModal());
    }

    // 统计刷新按钮
    const refreshUploadStatsBtn = document.getElementById('refresh-upload-stats-btn');
    if (refreshUploadStatsBtn) {
      refreshUploadStatsBtn.addEventListener('click', () => this.loadUploadStats());
    }

    const refreshResignStatsBtn = document.getElementById('refresh-resign-stats-btn');
    if (refreshResignStatsBtn) {
      refreshResignStatsBtn.addEventListener('click', () => this.loadResignStats());
    }

    const batchDeleteResignBtn = document.getElementById('batch-delete-resign-btn');
    if (batchDeleteResignBtn) {
      batchDeleteResignBtn.addEventListener('click', () => this.showBatchDeleteModal());
    }

    const batchDeleteUploadBtn = document.getElementById('batch-delete-upload-btn');
    if (batchDeleteUploadBtn) {
      batchDeleteUploadBtn.addEventListener('click', () => this.showBatchDeleteUploadModal());
    }

    // 系统操作按钮已删除（后端未实现）
  }

  /**
   * 加载仪表板数据
   */
  async loadDashboardData() {
    // 默认加载用户列表
    this.loadUsers();
  }

  /**
   * 加载标签页数据
   */
  loadTabData(tab) {
    const module = this.modules.get(tab);
    if (module && module.loader) {
      console.log(`Loading module: ${module.name}`);
      module.loader();
      module.initialized = true;
    } else {
      console.warn(`Unknown module: ${tab}`);
    }
  }

  /**
   * 加载用户列表
   */
  async loadUsers() {
    const usersList = document.getElementById('users-list');
    const statsContainer = document.getElementById('user-stats-container');

    if (!usersList) return;

    try {
      usersList.innerHTML = '<div class="text-center py-8">加载中...</div>';
      if (statsContainer) {
        statsContainer.innerHTML = '<div class="text-center py-4">加载统计中...</div>';
      }

      const response = await api.get('/api/admin/users');

      // 调试：打印接收到的数据
      console.log('用户API响应:', response);

      // 处理响应数据 - 兼容不同的响应格式
      let users = null;
      if (response.data) {
        // 新API格式：{data: {success: true, users: [...]}}
        if (response.data.success && response.data.users) {
          users = response.data.users;
        } else if (response.data.users) {
          users = response.data.users;
        }
      } else if (response.users) {
        // 原API格式：{users: [...]}
        users = response.users;
      }

      if (users && users.length > 0) {
        this.renderUserStats(users);
        this.renderUsers(users);
      } else {
        usersList.innerHTML = '<div class="text-center py-8 text-muted">暂无用户数据</div>';
        if (statsContainer) {
          statsContainer.innerHTML = '';
        }
      }
    } catch (error) {
      console.error('Failed to load users:', error);

      if (error.status === 401) {
        usersList.innerHTML = '<div class="text-center py-8 text-error">认证失败，请重新登录</div>';
      } else if (error.status === 403) {
        usersList.innerHTML = '<div class="text-center py-8 text-error">权限不足，需要管理员权限</div>';
      } else {
        usersList.innerHTML = '<div class="text-center py-8 text-error">加载失败，请稍后重试</div>';
      }
    }
  }

  /**
   * 渲染用户统计
   */
  renderUserStats(users) {
    const statsContainer = document.getElementById('user-stats-container');
    if (!statsContainer) return;

    const totalUsers = users.length;
    const activeUsers = users.filter(user => user.status === 'active').length;
    const expiredUsers = users.filter(user => {
      if (!user.expires_at) return false;
      return new Date(user.expires_at) < new Date();
    }).length;
    const adminUsers = users.filter(user => user.role === 'admin').length;

    const statsCards = [
      { label: '总用户数', value: totalUsers, icon: '👥' },
      { label: '活跃用户', value: activeUsers, icon: '✅' },
      { label: '已过期', value: expiredUsers, icon: '⏰' },
      { label: '管理员', value: adminUsers, icon: '👑' }
    ];

    const statsHTML = `
      <div class="user-stats-row">
        ${statsCards.map(stat => `
          <div class="user-stat-item">
            <div class="stat-icon">${stat.icon}</div>
            <div class="stat-value">${stat.value}</div>
            <div class="stat-label">${stat.label}</div>
          </div>
        `).join('')}
      </div>
    `;

    statsContainer.innerHTML = statsHTML;
  }

  /**
   * 渲染用户列表
   */
  renderUsers(users) {
    const usersList = document.getElementById('users-list');
    if (!usersList) return;

    if (!users || users.length === 0) {
      usersList.innerHTML = '<div class="text-center py-8 text-muted">暂无用户</div>';
      return;
    }

    const tableHTML = `
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>用户名</th>
              <th>邮箱</th>
              <th>角色</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>最后登录</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${users.map(user => {
              // 计算激活状态和剩余时长
              let statusDisplay = '';
              if (user.status === 'active') {
                if (user.expires_at) {
                  const expiresAt = new Date(user.expires_at);
                  const now = new Date();
                  const remainingMs = expiresAt.getTime() - now.getTime();

                  if (remainingMs > 0) {
                    const remainingDays = Math.ceil(remainingMs / (1000 * 60 * 60 * 24));
                    statusDisplay = `<span class="badge badge-success">✅ 已激活 (剩余${remainingDays}天)</span>`;
                  } else {
                    const expiredDays = Math.ceil(Math.abs(remainingMs) / (1000 * 60 * 60 * 24));
                    statusDisplay = `<span class="badge badge-warning">⏰ 已过期 (${expiredDays}天)</span>`;
                  }
                } else {
                  statusDisplay = `<span class="badge badge-success">✅ 已激活 (永久)</span>`;
                }
              } else {
                statusDisplay = `<span class="badge badge-error">❌ 未激活</span>`;
              }

              const createdAt = formatDateTime(user.created_at);
              const lastLogin = user.last_login ? formatDateTime(user.last_login) : '从未登录';

              return `
                <tr>
                  <td><strong>${user.username}</strong></td>
                  <td>${user.email}</td>
                  <td><span class="badge badge-${user.role === 'super_admin' ? 'danger' : user.role === 'admin' ? 'primary' : 'secondary'}">${user.role === 'super_admin' ? '超级管理员' : user.role === 'admin' ? '管理员' : '普通用户'}</span></td>
                  <td>${statusDisplay}</td>
                  <td>${createdAt}</td>
                  <td>${lastLogin}</td>
                  <td>
                    ${(() => {
                      const currentUser = auth.getUser();
                      const canDelete = currentUser && (
                        // 超级管理员可以删除任何人（除了自己）
                        (currentUser.role === 'super_admin' && user._id !== currentUser._id) ||
                        // 普通管理员只能删除普通用户
                        (currentUser.role === 'admin' && user.role === 'user')
                      );

                      const canManage = user.role === 'user' || (currentUser && currentUser.role === 'super_admin' && user._id !== currentUser._id);

                      if (canManage) {
                        return `
                          <div class="flex gap-1 flex-wrap">
                            ${(() => {
                              // 检查是否过期，显示续期按钮（只对普通用户）
                              if (user.role === 'user' && user.expires_at) {
                                const expiresAt = new Date(user.expires_at);
                                const now = new Date();
                                const isExpired = now.getTime() > expiresAt.getTime();

                                if (isExpired) {
                                  return `
                                    <button class="btn btn-success btn-sm" onclick="window.adminPage.extendUserExpiry('${user._id || user.id}', '${user.username}', 30)" title="延长30天">
                                      🔄 续期
                                    </button>
                                  `;
                                }
                              }
                              return '';
                            })()}
                            <button class="btn btn-warning btn-sm" onclick="window.adminPage.resetUserPassword('${user._id || user.id}', '${user.username}')" title="重置密码">
                              🔑 重置密码
                            </button>
                            ${canDelete ? `
                              <button class="btn btn-error btn-sm" onclick="window.adminPage.deleteUser('${user._id || user.id}', '${user.username}')" title="删除用户">
                                🗑️删除
                              </button>
                            ` : ''}
                          </div>
                        `;
                      } else {
                        const roleText = user.role === 'super_admin' ? '超级管理员账户' : '管理员账户';
                        return `<span class="text-muted">${roleText}</span>`;
                      }
                    })()}
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;

    usersList.innerHTML = tableHTML;

    // 将页面实例绑定到window，供按钮调用
    window.adminPage = this;
  }

  /**
   * 创建模态框的通用方法
   */
  createModal(content) {
    // 创建背景遮罩
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop active';

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = content;

    document.body.appendChild(backdrop);
    document.body.appendChild(modal);

    // 关闭模态框的函数
    const closeModal = () => {
      modal.remove();
      backdrop.remove();
    };

    // 点击背景关闭
    backdrop.addEventListener('click', closeModal);

    // 更新关闭按钮的事件
    const closeButtons = modal.querySelectorAll('.modal-close');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', closeModal);
    });

    // ESC键关闭
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        closeModal();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);

    return { modal, backdrop, close: closeModal };
  }

  /**
   * 延长用户有效期
   */
  async extendUserExpiry(userId, username, days) {
    const customDays = prompt(`为用户 "${username}" 延长有效期（天数）：`, days.toString());
    if (!customDays || isNaN(customDays) || parseInt(customDays) <= 0) {
      alert('请输入有效的天数');
      return;
    }

    if (!confirm(`确定要为用户 "${username}" 延长 ${customDays} 天有效期吗？`)) {
      return;
    }

    try {
      const response = await api.post('/api/admin/users/extend-expiry', {
        user_id: userId,
        days: parseInt(customDays)
      });

      if (response.data.success) {
        alert(`✅ 用户 "${username}" 有效期已延长 ${customDays} 天`);
        this.loadUsers(); // 重新加载用户列表
      } else {
        alert('❌ 延长有效期失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to extend user expiry:', error);
      alert('❌ 延长有效期失败：' + error.message);
    }
  }

  /**
   * 重置用户密码
   */
  async resetUserPassword(userId, username) {
    const newPassword = prompt(`为用户 "${username}" 设置新密码：`, '');
    if (!newPassword || newPassword.length < 6) {
      alert('密码长度至少6位');
      return;
    }

    if (!confirm(`确定要重置用户 "${username}" 的密码吗？`)) {
      return;
    }

    try {
      const response = await api.post('/api/admin/users/reset-password', {
        user_id: userId,
        new_password: newPassword
      });

      if (response.data.success) {
        alert(`✅ 用户 "${username}" 密码重置成功`);
      } else {
        alert('❌ 重置密码失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to reset user password:', error);
      alert('❌ 重置密码失败：' + error.message);
    }
  }

  /**
   * 删除用户
   */
  async deleteUser(userId, username) {
    const confirmed = await notifications.confirmDelete(`用户 "${username}"`, {
      message: `确定要删除用户 "${username}" 吗？此操作不可撤销！`
    });
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.delete(`/api/admin/users/${userId}`);

      if (response.data.success) {
        notifications.success(`用户 "${username}" 删除成功`);
        this.loadUsers(); // 重新加载用户列表
      } else {
        notifications.error('删除用户失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to delete user:', error);
      notifications.error('删除用户失败：' + error.message);
    }
  }

  /**
   * 加载激活码列表
   */
  async loadActivationCodes() {
    const codesList = document.getElementById('activation-codes-list');
    if (!codesList) return;

    try {
      codesList.innerHTML = '<div class="text-center py-8">加载中...</div>';

      const response = await api.get('/api/admin/activation-codes');

      // 调试：打印接收到的数据
      console.log('激活码API响应:', response);

      // 处理响应数据 - 兼容不同的响应格式
      let codes = null;
      if (response.data) {
        // 新API格式：{data: {success: true, codes: [...]}}
        if (response.data.success && response.data.codes) {
          codes = response.data.codes;
        } else if (response.data.codes) {
          codes = response.data.codes;
        }
      } else if (response.codes) {
        // 原API格式：{codes: [...]}
        codes = response.codes;
      }

      if (codes && codes.length > 0) {
        this.renderActivationCodes(codes);
      } else {
        codesList.innerHTML = '<div class="text-center py-8 text-muted">暂无激活码</div>';
      }
    } catch (error) {
      console.error('Failed to load activation codes:', error);
      codesList.innerHTML = '<div class="text-center py-8 text-error">加载失败，请稍后重试</div>';
    }
  }

  /**
   * 渲染激活码列表
   */
  renderActivationCodes(codes) {
    const codesList = document.getElementById('activation-codes-list');
    if (!codesList) return;

    if (!codes || codes.length === 0) {
      codesList.innerHTML = '<div class="text-center py-8 text-muted">暂无激活码</div>';
      return;
    }

    const tableHTML = `
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>激活码</th>
              <th>状态</th>
              <th>用户有效期</th>
              <th>使用情况</th>
              <th>创建时间</th>
              <th>使用时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${codes.map(code => {
              const createdAt = formatDateTime(code.created_at);
              const usedAt = code.used_at ? formatDateTime(code.used_at) : '未使用';
              const validityDays = code.user_validity_days || 30;

              let statusClass = 'success';
              let statusText = '有效';

              if (code.status === 'used') {
                statusClass = 'secondary';
                statusText = '已使用';
              } else if (code.status === 'expired') {
                statusClass = 'error';
                statusText = '已过期';
              } else if (code.status === 'inactive') {
                statusClass = 'warning';
                statusText = '已禁用';
              }

              return `
                <tr>
                  <td><code class="code-display">${code.code}</code></td>
                  <td><span class="badge badge-${statusClass}">${statusText}</span></td>
                  <td>${validityDays}天</td>
                  <td>${code.used_count || 0}/${code.max_uses || 1}</td>
                  <td>${createdAt}</td>
                  <td>${usedAt}</td>
                  <td>
                    <div class="flex gap-1 flex-wrap">
                      ${code.status === 'active' ? `
                        <button class="btn btn-warning btn-sm" onclick="window.adminPage.deactivateActivationCode('${code._id || code.id}')" title="禁用激活码">
                          🚫 禁用
                        </button>
                      ` : ''}
                      <button class="btn btn-error btn-sm" onclick="window.adminPage.deleteActivationCode('${code._id || code.id}', '${code.code}')" title="删除激活码">
                        🗑️ 删除
                      </button>
                    </div>
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;

    codesList.innerHTML = tableHTML;
  }

  /**
   * 显示创建激活码模态框
   */
  showCreateActivationCodeModal() {
    const content = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">生成激活码</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <form id="create-activation-code-form">
            <div class="form-group">
              <label class="form-label form-label-required">用户有效期（天）</label>
              <input type="number" name="validity_days" class="form-control" value="30" min="1" max="365" required>
              <small class="form-help">激活后用户账号的有效期</small>
            </div>
            <div class="form-group">
              <label class="form-label">最大使用次数</label>
              <input type="number" name="max_uses" class="form-control" value="1" min="1" max="100">
              <small class="form-help">该激活码可以使用的次数</small>
            </div>
            <div class="form-group">
              <label class="form-label">备注</label>
              <input type="text" name="note" class="form-control" placeholder="可选的备注信息">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">取消</button>
          <button class="btn btn-primary" onclick="window.adminPage.submitCreateActivationCode()">生成激活码</button>
        </div>
      </div>
    `;

    const modalInfo = this.createModal(content);
    // 保存关闭函数供后续使用
    this.currentModalClose = modalInfo.close;
  }

  /**
   * 提交创建激活码
   */
  async submitCreateActivationCode() {
    const form = document.getElementById('create-activation-code-form');
    const formData = new FormData(form);

    const validityDays = parseInt(formData.get('validity_days'));
    const maxUses = parseInt(formData.get('max_uses'));

    // 使用与原项目兼容的数据格式
    const codeData = {
      expires_in: validityDays * 24, // 转换为小时
      max_uses: maxUses,
      expires_in_days: validityDays, // 保存原始天数用于显示
      note: formData.get('note') || ''
    };

    try {
      const response = await api.post('/api/admin/activation-codes', codeData);

      console.log('激活码生成响应:', response);

      // 处理不同的响应格式
      let success = false;
      let code = '';
      let message = '';

      if (response.data) {
        success = response.data.success;
        code = response.data.code;
        message = response.data.message;
      } else if (response.code) {
        // 原API格式直接返回code
        success = true;
        code = response.code;
      }

      if (success && code) {
        alert(`✅ 激活码生成成功！\n激活码: ${code}`);
        // 关闭模态框
        if (this.currentModalClose) {
          this.currentModalClose();
          this.currentModalClose = null;
        }
        this.loadActivationCodes();
      } else {
        alert('❌ 生成失败：' + (message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to create activation code:', error);
      alert('❌ 生成失败：' + error.message);
    }
  }

  /**
   * 禁用激活码
   */
  async deactivateActivationCode(codeId) {
    const confirmed = await notifications.confirm('确定要禁用这个激活码吗？');
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.put(`/api/admin/activation-codes/${codeId}`, {
        status: 'inactive'
      });

      console.log('禁用激活码响应:', response);

      // 处理不同的响应格式
      let success = false;
      let message = '';

      if (response.data) {
        success = response.data.success;
        message = response.data.message;
      } else if (response.success !== false) {
        success = true;
      }

      if (success) {
        notifications.success('激活码已禁用！');
        this.loadActivationCodes();
      } else {
        notifications.error('禁用失败：' + (message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to deactivate activation code:', error);
      notifications.error('禁用失败：' + error.message);
    }
  }

  /**
   * 删除激活码
   */
  async deleteActivationCode(codeId, code) {
    const confirmed = await notifications.confirmDelete(`激活码 "${code}"`, {
      message: `确定要删除激活码 "${code}" 吗？此操作不可恢复！`
    });
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.delete(`/api/admin/activation-codes/${codeId}`);

      console.log('删除激活码响应:', response);

      // 处理不同的响应格式
      let success = false;
      let message = '';

      if (response.data) {
        success = response.data.success;
        message = response.data.message;
      } else if (response.success !== false) {
        success = true;
      }

      if (success) {
        notifications.success('激活码已删除！');
        this.loadActivationCodes();
      } else {
        notifications.error('删除失败：' + (message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to delete activation code:', error);
      notifications.error('删除失败：' + error.message);
    }
  }

  /**
   * 加载GitHub账号列表
   */
  async loadGitHubAccounts() {
    const accountsList = document.getElementById('github-accounts-list');
    const statsContainer = document.getElementById('github-stats-container');

    if (!accountsList) return;

    try {
      accountsList.innerHTML = '<div class="text-center py-8">加载中...</div>';
      if (statsContainer) {
        statsContainer.innerHTML = '<div class="text-center py-4">加载统计中...</div>';
      }

      // 先尝试使用包含使用统计的API端点，如果失败则使用标准端点
      let response;
      try {
        response = await api.get('/api/admin/github-usage-stats');
      } catch (error) {
        console.warn('GitHub usage stats API not available, falling back to standard API');
        response = await api.get('/api/admin/github-accounts');
      }

      // 调试：打印接收到的数据
      console.log('GitHub账号API响应:', response);

      // 处理响应数据 - 兼容不同的响应格式
      let accounts = null;
      if (response.data) {
        // 新API格式：{data: {success: true, accounts: [...]}}
        if (response.data.success && response.data.accounts) {
          accounts = response.data.accounts;
        } else if (response.data.accounts) {
          accounts = response.data.accounts;
        }
      } else if (response.accounts) {
        // 原API格式：{accounts: [...]}
        accounts = response.accounts;
      }

      console.log('解析后的账号数据:', accounts);

      if (accounts && accounts.length > 0) {
        this.renderGitHubAccounts(accounts);
        this.renderGitHubStats(accounts);
      } else {
        accountsList.innerHTML = '<div class="text-center py-8 text-muted">暂无GitHub账号</div>';
        if (statsContainer) {
          statsContainer.innerHTML = '';
        }
      }
    } catch (error) {
      console.error('Failed to load GitHub accounts:', error);
      accountsList.innerHTML = '<div class="text-center py-8 text-error">加载失败，请稍后重试</div>';
      if (statsContainer) {
        statsContainer.innerHTML = '<div class="text-center py-4 text-error">加载统计失败</div>';
      }
    }
  }

  /**
   * 渲染GitHub账号列表
   */
  renderGitHubAccounts(accounts) {
    const accountsList = document.getElementById('github-accounts-list');
    if (!accountsList) return;

    if (!accounts || accounts.length === 0) {
      accountsList.innerHTML = '<div class="text-center py-8 text-muted">暂无GitHub账号</div>';
      return;
    }

    const maxUsageLimit = 2500; // 与后端保持一致

    const tableHTML = `
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>账号信息</th>
              <th>使用状态</th>
              <th>使用情况</th>
              <th>使用统计</th>
              <th>Token状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${accounts.map(account => {
              const createdAt = formatDateTime(account.created_at);
              const lastUsed = account.last_used_at ? formatDateTime(account.last_used_at) : '从未使用';

              // GitHub Actions分钟数使用情况
              const totalUsage = account.total_usage_minutes || 0;
              const remainingMinutes = maxUsageLimit - totalUsage;
              const usagePercentage = Math.min((totalUsage / maxUsageLimit) * 100, 100);
              const isInUse = account.is_in_use || false;
              const hasToken = account.has_token !== false;

              // 工作流执行统计信息
              const usageCount = account.usage_count || 0;
              const totalUsageSeconds = account.total_usage_seconds || 0;
              const averageDuration = account.average_duration || 0;

              let statusClass = 'idle';
              let statusText = '空闲';
              if (isInUse) {
                statusClass = 'in-use';
                statusText = '使用中';
              } else if (remainingMinutes < 60) {
                statusClass = 'danger';
                statusText = '时间不足';
              } else if (remainingMinutes < 300) {
                statusClass = 'warning';
                statusText = '即将用完';
              }

              return `
                <tr>
                  <td>
                    <div class="account-info">
                      <strong>${account.username}</strong><br>
                      <small class="text-secondary">${account.email || ''}</small><br>
                      <span class="badge badge-${account.status === 'active' ? 'success' : 'error'}">${account.status}</span>
                      <br><small class="text-secondary">创建: ${new Date(account.created_at).toLocaleDateString()}</small>
                      ${account.last_used_at ? `<br><small class="text-secondary">最后使用: ${new Date(account.last_used_at).toLocaleString()}</small>` : ''}
                    </div>
                  </td>
                  <td>
                    <div class="status-indicator">
                      <span class="status-dot status-${statusClass}"></span>
                      ${statusText}
                    </div>
                  </td>
                  <td>
                    <div class="usage-info">
                      <div class="usage-bar">
                        <div class="usage-fill" style="width: ${usagePercentage}%"></div>
                      </div>
                      <div class="usage-text">
                        已使用: ${totalUsage} / ${maxUsageLimit} 分钟 (${usagePercentage.toFixed(1)}%)
                        <br><small>剩余: ${remainingMinutes} 分钟</small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div class="usage-stats">
                      <div><strong>使用次数:</strong> ${usageCount}次</div>
                      <div><strong>总时长:</strong> ${formatDuration(totalUsageSeconds)}</div>
                      <div><strong>平均时长:</strong> ${formatDuration(averageDuration)}</div>
                      <small class="text-secondary">${account.last_used_at ? `最后使用: ${new Date(account.last_used_at).toLocaleString('zh-CN')}` : '从未使用'}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge badge-${hasToken ? 'success' : 'error'}">
                      ${hasToken ? '✅ 正常' : '❌ 缺失'}
                    </span>
                  </td>
                  <td>
                    <div class="flex gap-1 flex-wrap">
                      <button class="btn btn-${account.status === 'active' ? 'warning' : 'success'} btn-sm"
                              onclick="window.adminPage.toggleGitHubAccountStatus('${account._id || account.id}', '${account.status}')"
                              title="${account.status === 'active' ? '禁用账号' : '启用账号'}">
                        ${account.status === 'active' ? '🚫 禁用' : '✅ 启用'}
                      </button>
                      <button class="btn btn-secondary btn-sm"
                              onclick="window.adminPage.resetGitHubAccountUsage('${account._id || account.id}', '${account.username}')"
                              title="重置使用统计">
                        🔄 重置使用
                      </button>
                      ${isInUse ? `
                        <button class="btn btn-warning btn-sm"
                                onclick="window.adminPage.releaseGitHubAccount('${account._id || account.id}', '${account.username}')"
                                title="释放账号">
                          🔓 释放
                        </button>
                      ` : ''}
                      <button class="btn btn-error btn-sm"
                              onclick="window.adminPage.deleteGitHubAccount('${account._id || account.id}', '${account.username}')"
                              title="删除账号">
                        🗑️ 删除
                      </button>
                    </div>
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;

    accountsList.innerHTML = tableHTML;
  }

  /**
   * 渲染GitHub统计信息
   */
  renderGitHubStats(accounts) {
    const statsContainer = document.getElementById('github-stats-container');
    if (!statsContainer) return;

    const maxUsageLimit = 2500;
    const totalAccounts = accounts.length;
    const activeAccounts = accounts.filter(acc => acc.status === 'active').length;
    const inUseAccounts = accounts.filter(acc => acc.is_in_use).length;
    const idleAccounts = activeAccounts - inUseAccounts;

    let totalUsage = 0;
    let warningAccounts = 0;
    let dangerAccounts = 0;

    accounts.forEach(acc => {
      const usage = acc.total_usage_minutes || 0;
      totalUsage += usage;
      const remaining = maxUsageLimit - usage;

      if (remaining < 60) {
        dangerAccounts++;
      } else if (remaining < 300) {
        warningAccounts++;
      }
    });

    const statsCards = [
      { label: '总账号数', value: totalAccounts, icon: '🐙', class: 'primary' },
      { label: '空闲账号', value: idleAccounts, icon: '✅', class: 'success' },
      { label: '使用中', value: inUseAccounts, icon: '⚡', class: 'warning' },
      { label: '需要关注', value: warningAccounts + dangerAccounts, icon: '⚠️', class: 'error' }
    ];

    const statsHTML = `
      <div class="github-stats-row">
        ${statsCards.map(stat => `
          <div class="github-stat-item stat-${stat.class}">
            <div class="stat-icon">${stat.icon}</div>
            <div class="stat-value">${stat.value}</div>
            <div class="stat-label">${stat.label}</div>
          </div>
        `).join('')}
      </div>
    `;

    statsContainer.innerHTML = statsHTML;
  }

  /**
   * 显示添加GitHub账号模态框
   */
  showAddGitHubAccountModal() {
    const content = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">添加GitHub账号</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <form id="add-github-account-form">
            <div class="form-group">
              <label class="form-label form-label-required">GitHub用户名</label>
              <input type="text" name="username" class="form-control" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">邮箱</label>
              <input type="email" name="email" class="form-control" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">Personal Access Token</label>
              <textarea name="token" class="form-control" rows="3" placeholder="请输入GitHub Personal Access Token" required></textarea>
              <small class="form-help">需要包含 repo 和 actions 权限</small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">取消</button>
          <button class="btn btn-primary" onclick="window.adminPage.submitAddGitHubAccount()">添加账号</button>
        </div>
      </div>
    `;

    const modalInfo = this.createModal(content);
    this.currentModalClose = modalInfo.close;
  }

  /**
   * 提交添加GitHub账号
   */
  async submitAddGitHubAccount() {
    const form = document.getElementById('add-github-account-form');
    const formData = new FormData(form);

    const accountData = {
      username: formData.get('username'),
      email: formData.get('email'),
      token: formData.get('token')
    };

    try {
      const response = await api.post('/api/admin/github-accounts', accountData);

      // 处理不同的响应格式
      let success = false;
      let message = '';

      if (response.data) {
        success = response.data.success;
        message = response.data.message;
      } else if (response.success !== false) {
        // 原API格式可能直接返回成功
        success = true;
      }

      if (success) {
        alert('✅ GitHub账号添加成功！');
        if (this.currentModalClose) {
          this.currentModalClose();
          this.currentModalClose = null;
        }
        this.loadGitHubAccounts();
      } else {
        alert('❌ 添加失败：' + (message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to add GitHub account:', error);
      alert('❌ 添加失败：' + error.message);
    }
  }

  /**
   * 刷新GitHub账号状态
   */
  async refreshGitHubAccounts() {
    if (confirm('确定要刷新所有GitHub账号状态吗？这可能需要一些时间。')) {
      try {
        const response = await api.post('/api/admin/github-accounts/refresh-all');

        if (response.data.success) {
          alert('✅ 账号状态刷新成功！');
          this.loadGitHubAccounts();
        } else {
          alert('❌ 刷新失败：' + (response.data.message || '未知错误'));
        }
      } catch (error) {
        console.error('Failed to refresh GitHub accounts:', error);
        alert('❌ 刷新失败：' + error.message);
      }
    }
  }

  /**
   * 切换GitHub账号状态
   */
  async toggleGitHubAccountStatus(accountId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? '启用' : '禁用';

    if (confirm(`确定要${action}该GitHub账号吗？`)) {
      try {
        const response = await api.put(`/api/admin/github-accounts/${accountId}`, {
          status: newStatus
        });

        if (response.data.success) {
          alert(`✅ 账号已${action}！`);
          this.loadGitHubAccounts();
        } else {
          alert(`❌ ${action}失败：` + (response.data.message || '未知错误'));
        }
      } catch (error) {
        console.error(`Failed to toggle GitHub account status:`, error);
        alert(`❌ ${action}失败：` + error.message);
      }
    }
  }

  /**
   * 重置GitHub账号使用统计
   */
  async resetGitHubAccountUsage(accountId, username) {
    const confirmed = await notifications.confirm(`确定要重置账号 "${username}" 的使用统计吗？`);
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.post(`/api/admin/github-accounts/${accountId}/reset-usage`);

      if (response.data.success) {
        notifications.success(`账号 "${username}" 使用统计已重置！`);
        this.loadGitHubAccounts();
      } else {
        notifications.error('重置失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to reset GitHub account usage:', error);
      notifications.error('重置失败：' + error.message);
    }
  }

  /**
   * 释放GitHub账号
   */
  async releaseGitHubAccount(accountId, username) {
    if (confirm(`确定要释放账号 "${username}" 吗？这将中断当前使用该账号的任务。`)) {
      try {
        const response = await api.post(`/api/admin/github-accounts/${accountId}/release`);

        if (response.data.success) {
          alert(`✅ 账号 "${username}" 已释放！`);
          this.loadGitHubAccounts();
        } else {
          alert('❌ 释放失败：' + (response.data.message || '未知错误'));
        }
      } catch (error) {
        console.error('Failed to release GitHub account:', error);
        alert('❌ 释放失败：' + error.message);
      }
    }
  }

  /**
   * 删除GitHub账号
   */
  async deleteGitHubAccount(accountId, username) {
    if (confirm(`确定要删除GitHub账号 "${username}" 吗？此操作不可恢复！`)) {
      try {
        const response = await api.delete(`/api/admin/github-accounts/${accountId}`);

        if (response.data.success) {
          alert('✅ GitHub账号已删除！');
          this.loadGitHubAccounts();
        } else {
          alert('❌ 删除失败：' + (response.data.message || '未知错误'));
        }
      } catch (error) {
        console.error('Failed to delete GitHub account:', error);
        alert('❌ 删除失败：' + error.message);
      }
    }
  }

  /**
   * 加载证书列表和证书配对
   */
  async loadCertificates() {
    const certificatesList = document.getElementById('certificates-list');
    const certificatePairs = document.getElementById('certificate-pairs');

    if (!certificatesList) return;

    try {
      certificatesList.innerHTML = '<div class="text-center py-4">加载中...</div>';
      if (certificatePairs) {
        certificatePairs.innerHTML = '<div class="text-center py-4">加载中...</div>';
      }

      // 并行加载证书列表和证书配对
      const [certificatesResponse, pairsResponse] = await Promise.all([
        api.get('/api/certificates'),
        api.get('/api/certificates/pairs')
      ]);

      console.log('证书API响应:', certificatesResponse);
      console.log('证书配对API响应:', pairsResponse);

      // 处理证书列表数据
      let certificates = null;
      if (certificatesResponse.data) {
        if (certificatesResponse.data.success && certificatesResponse.data.certificates) {
          certificates = certificatesResponse.data.certificates;
        } else if (certificatesResponse.data.certificates) {
          certificates = certificatesResponse.data.certificates;
        }
      } else if (certificatesResponse.certificates) {
        certificates = certificatesResponse.certificates;
      } else if (certificatesResponse.success && certificatesResponse.certificates) {
        certificates = certificatesResponse.certificates;
      }

      // 处理证书配对数据
      let pairs = null;
      if (pairsResponse.data) {
        if (pairsResponse.data.success && pairsResponse.data.certificate_pairs) {
          pairs = pairsResponse.data.certificate_pairs;
        } else if (pairsResponse.data.certificate_pairs) {
          pairs = pairsResponse.data.certificate_pairs;
        }
      } else if (pairsResponse.certificate_pairs) {
        pairs = pairsResponse.certificate_pairs;
      } else if (pairsResponse.success && pairsResponse.certificate_pairs) {
        pairs = pairsResponse.certificate_pairs;
      }

      // 渲染证书列表
      if (certificates && certificates.length > 0) {
        this.renderCertificates(certificates);
      } else {
        certificatesList.innerHTML = '<div class="text-center py-4 text-muted">暂无证书</div>';
      }

      // 渲染证书配对
      if (certificatePairs) {
        if (pairs && pairs.length > 0) {
          this.renderCertificatePairs(pairs);
        } else {
          certificatePairs.innerHTML = '<div class="text-center py-4 text-muted">暂无可用证书对</div>';
        }
      }
    } catch (error) {
      console.error('Failed to load certificates:', error);
      certificatesList.innerHTML = '<div class="text-center py-4 text-error">加载失败，请稍后重试</div>';
      if (certificatePairs) {
        certificatePairs.innerHTML = '<div class="text-center py-4 text-error">加载失败，请稍后重试</div>';
      }
    }
  }

  /**
   * 渲染证书列表
   */
  renderCertificates(certificates) {
    const certificatesList = document.getElementById('certificates-list');
    if (!certificatesList) return;

    if (!certificates || certificates.length === 0) {
      certificatesList.innerHTML = '<div class="text-center py-8 text-muted">暂无证书</div>';
      return;
    }

    const tableHTML = `
      <div class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>证书信息</th>
              <th>类型</th>
              <th>文件大小</th>
              <th>使用次数</th>
              <th>创建时间</th>
              <th>过期时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${certificates.map(cert => {
              const createdAt = formatDateTime(cert.created_at);
              const expiresAt = cert.expires_at ? formatDateTime(cert.expires_at) : '无';
              const fileSize = formatFileSize(cert.file_size || 0);

              let typeClass = 'primary';
              let typeText = cert.type;

              if (cert.type === 'p12') {
                typeClass = 'success';
                typeText = 'P12证书';
              } else if (cert.type === 'mobileprovision') {
                typeClass = 'warning';
                typeText = '描述文件';
              }

              return `
                <tr>
                  <td>
                    <div class="cert-info">
                      <strong>${cert.name}</strong>
                      ${cert.team_name ? `<br><small class="text-secondary">团队: ${cert.team_name}</small>` : ''}
                      ${cert.bundle_id ? `<br><small class="text-secondary">Bundle ID: ${cert.bundle_id}</small>` : ''}
                    </div>
                  </td>
                  <td><span class="badge badge-${typeClass}">${typeText}</span></td>
                  <td>${fileSize}</td>
                  <td>${cert.usage_count || 0}</td>
                  <td>${createdAt}</td>
                  <td>${expiresAt}</td>
                  <td>
                    <div class="flex gap-1 flex-wrap">
                      <button class="btn btn-error btn-sm" onclick="window.adminPage.deleteCertificate('${cert._id || cert.id}', '${cert.name}')" title="删除证书">
                        🗑️ 删除
                      </button>
                    </div>
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;

    certificatesList.innerHTML = tableHTML;
  }

  /**
   * 渲染证书配对列表
   */
  renderCertificatePairs(pairs) {
    const pairsContainer = document.getElementById('certificate-pairs');
    if (!pairsContainer) return;

    if (!pairs || pairs.length === 0) {
      pairsContainer.innerHTML = '<div class="text-center py-4 text-muted">暂无可用证书对</div>';
      return;
    }

    const pairsHTML = pairs.map(pair => {
      const expiresAt = formatDateTime(pair.expires_at);

      return `
        <div class="certificate-pair-card">
          <div class="pair-header">
            <div class="pair-title">
              <h5 class="font-semibold">${pair.team_name}</h5>
              <span class="badge badge-success">可用</span>
            </div>
          </div>
          <div class="pair-content">
            <div class="pair-info-grid">
              <div class="pair-info-item">
                <span class="info-label">Bundle ID:</span>
                <span class="info-value">${pair.bundle_id}</span>
              </div>
              <div class="pair-info-item">
                <span class="info-label">团队名称:</span>
                <span class="info-value">${pair.team_name}</span>
              </div>
              <div class="pair-info-item">
                <span class="info-label">过期时间:</span>
                <span class="info-value">${expiresAt}</span>
              </div>
              <div class="pair-info-item">
                <span class="info-label">P12证书:</span>
                <span class="info-value">${pair.p12.name}</span>
              </div>
              <div class="pair-info-item">
                <span class="info-label">描述文件:</span>
                <span class="info-value">${pair.mobileprovision.name}</span>
              </div>
            </div>
          </div>
        </div>
      `;
    }).join('');

    pairsContainer.innerHTML = pairsHTML;
  }

  /**
   * 显示上传P12证书模态框
   */
  showUploadP12Modal() {
    const content = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">上传P12证书</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <form id="upload-p12-form" enctype="multipart/form-data">
            <div class="form-group">
              <label class="form-label form-label-required">证书名称</label>
              <input type="text" name="name" class="form-control" required placeholder="请输入证书名称">
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">证书文件</label>
              <input type="file" name="certificate" class="form-control" accept=".p12,.pfx" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">证书密码</label>
              <input type="password" name="password" class="form-control" required placeholder="请输入证书密码">
            </div>
            <div class="form-group">
              <label class="form-label">证书类型</label>
              <select name="certificate_type" class="form-control">
                <option value="distribution">发布证书</option>
                <option value="development">开发证书</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">取消</button>
          <button class="btn btn-primary" onclick="window.adminPage.submitUploadP12()">上传证书</button>
        </div>
      </div>
    `;

    const modalInfo = this.createModal(content);
    this.currentModalClose = modalInfo.close;
  }

  /**
   * 加载上传记录
   */
  async loadUploads() {
    const uploadsList = document.getElementById('uploads-list');
    if (!uploadsList) return;
    
    try {
      uploadsList.innerHTML = '<div class="text-center py-8">加载中...</div>';
      
      const response = await api.get('/api/admin/uploads');
      
      if (response.data.success) {
        // 这里可以渲染上传记录
        uploadsList.innerHTML = '<div class="text-center py-8 text-muted">功能开发中...</div>';
      }
    } catch (error) {
      console.error('Failed to load uploads:', error);
      uploadsList.innerHTML = '<div class="text-center py-8 text-error">加载失败</div>';
    }
  }

  /**
   * 加载上传统计
   */
  async loadUploadStats() {
    const statsContainer = document.getElementById('upload-stats-container');

    if (!statsContainer) return;

    try {
      statsContainer.innerHTML = '<div class="text-center py-4">加载统计中...</div>';

      // 调用上传统计API
      const response = await api.get('/api/admin/upload/stats');

      console.log('上传统计API响应:', response);

      // 处理不同的响应格式
      let stats = null;
      if (response.data) {
        if (response.data.success) {
          stats = response.data.stats;
        }
      } else if (response.stats) {
        stats = response.stats;
      }

      if (stats) {
        this.renderUploadStats(stats);
      } else {
        statsContainer.innerHTML = '<div class="text-center py-4 text-error">加载统计失败</div>';
      }
    } catch (error) {
      console.error('Failed to load upload stats:', error);
      statsContainer.innerHTML = '<div class="text-center py-4 text-error">加载统计失败，请稍后重试</div>';
    }
  }

  /**
   * 渲染上传统计
   */
  renderUploadStats(stats) {
    const statsContainer = document.getElementById('upload-stats-container');
    if (!statsContainer) return;

    const statsCards = [
      { label: '总上传数', value: stats.total_uploads || 0, icon: '📤', color: 'primary' },
      { label: '成功上传', value: stats.success_uploads || 0, icon: '✅', color: 'success' },
      { label: '失败上传', value: stats.failed_uploads || 0, icon: '❌', color: 'error' },
      { label: '队列中', value: stats.queued_uploads || 0, icon: '⏳', color: 'warning' },
      { label: '上传中', value: stats.uploading_uploads || 0, icon: '📤', color: 'info' },
      { label: '今日上传', value: stats.today_uploads || 0, icon: '📅', color: 'secondary' },
      { label: '成功率', value: `${stats.success_rate || 0}%`, icon: '📊', color: 'primary' }
    ];

    const statsHTML = `
      <div class="stats-grid-horizontal">
        ${statsCards.map(stat => `
          <div class="stat-card stat-card-${stat.color}">
            <div class="stat-icon">${stat.icon}</div>
            <div class="stat-content">
              <div class="stat-value">${stat.value}</div>
              <div class="stat-label">${stat.label}</div>
            </div>
          </div>
        `).join('')}
      </div>
    `;

    statsContainer.innerHTML = statsHTML;
  }

  /**
   * 渲染系统统计
   */
  renderSystemStats(stats) {
    const statsContainer = document.getElementById('upload-stats-container');
    if (!statsContainer) return;

    const statsCards = [
      { label: '总用户数', value: stats.total_users || 0, icon: '👥' },
      { label: '活跃用户', value: stats.active_users || 0, icon: '🟢' },
      { label: '激活码总数', value: stats.total_activation_codes || 0, icon: '🎫' },
      { label: '有效激活码', value: stats.active_activation_codes || 0, icon: '✅' },
      { label: 'GitHub账号', value: stats.total_github_accounts || 0, icon: '🐙' },
      { label: '可用账号', value: stats.available_github_accounts || 0, icon: '🟢' },
      { label: '证书总数', value: stats.total_certificates || 0, icon: '📋' },
      { label: '证书配对', value: stats.certificate_pairs || 0, icon: '🔗' }
    ];

    const statsHTML = statsCards.map(stat => `
      <div class="card card-filled">
        <div class="card-body text-center">
          <div class="text-2xl mb-2">${stat.icon}</div>
          <div class="text-2xl font-bold text-primary">${stat.value}</div>
          <div class="text-sm text-secondary">${stat.label}</div>
        </div>
      </div>
    `).join('');

    statsContainer.innerHTML = statsHTML;
  }

  /**
   * 渲染上传记录
   */
  renderUploadRecords(records) {
    const recordsList = document.getElementById('upload-records-list');
    if (!recordsList) return;

    if (!records || records.length === 0) {
      recordsList.innerHTML = '<div class="text-center py-8 text-muted">暂无上传记录</div>';
      return;
    }

    const recordsHTML = records.map(record => {
      const createdAt = formatDateTime(record.created_at);
      const fileSize = formatFileSize(record.file_size || 0);

      let statusClass = 'success';
      let statusText = '成功';

      if (record.status === 'failed') {
        statusClass = 'error';
        statusText = '失败';
      } else if (record.status === 'processing') {
        statusClass = 'warning';
        statusText = '处理中';
      }

      return `
        <div class="card mb-4">
          <div class="card-body">
            <div class="flex justify-between items-start">
              <div>
                <h4 class="font-medium">${record.filename}</h4>
                <div class="flex gap-4 mt-2 text-sm">
                  <span><strong>状态:</strong> <span class="badge badge-${statusClass}">${statusText}</span></span>
                  <span><strong>文件大小:</strong> ${fileSize}</span>
                  <span><strong>用户:</strong> ${record.username || '未知'}</span>
                </div>
                <div class="flex gap-4 mt-1 text-sm text-secondary">
                  <span><strong>上传时间:</strong> ${createdAt}</span>
                  ${record.error_message ? `<span class="text-error"><strong>错误:</strong> ${record.error_message}</span>` : ''}
                </div>
              </div>
            </div>
          </div>
        </div>
      `;
    }).join('');

    recordsList.innerHTML = recordsHTML;
  }

  /**
   * 加载重签统计
   */
  async loadResignStats() {
    const statsContainer = document.getElementById('resign-stats-container');

    if (!statsContainer) return;

    try {
      statsContainer.innerHTML = '<div class="text-center py-4">加载统计中...</div>';

      // 调用重签统计API
      const response = await api.get('/api/admin/resign/stats');

      console.log('重签统计API响应:', response);

      // 处理不同的响应格式
      let stats = null;
      if (response.data) {
        if (response.data.success) {
          stats = response.data.stats;
        }
      } else if (response.stats) {
        stats = response.stats;
      }

      if (stats) {
        this.renderResignStats(stats);
      } else {
        statsContainer.innerHTML = '<div class="text-center py-4 text-error">加载统计失败</div>';
      }
    } catch (error) {
      console.error('Failed to load resign stats:', error);
      statsContainer.innerHTML = '<div class="text-center py-4 text-error">加载统计失败，请稍后重试</div>';
    }
  }

  /**
   * 渲染重签统计
   */
  renderResignStats(stats) {
    const statsContainer = document.getElementById('resign-stats-container');
    if (!statsContainer) return;

    const statsCards = [
      { label: '总记录数', value: stats.total_records || 0, icon: '📋', color: 'primary' },
      { label: '成功记录', value: stats.success_records || 0, icon: '✅', color: 'success' },
      { label: '失败记录', value: stats.failed_records || 0, icon: '❌', color: 'error' },
      { label: '等待中', value: stats.pending_records || 0, icon: '⏳', color: 'warning' },
      { label: '处理中', value: stats.processing_records || 0, icon: '⚙️', color: 'info' },
      { label: '今日记录', value: stats.today_records || 0, icon: '📅', color: 'secondary' },
      { label: '成功率', value: `${stats.success_rate || 0}%`, icon: '📊', color: 'primary' }
    ];

    const statsHTML = `
      <div class="stats-grid-horizontal">
        ${statsCards.map(stat => `
          <div class="stat-card stat-card-${stat.color}">
            <div class="stat-icon">${stat.icon}</div>
            <div class="stat-content">
              <div class="stat-value">${stat.value}</div>
              <div class="stat-label">${stat.label}</div>
            </div>
          </div>
        `).join('')}
      </div>
    `;

    statsContainer.innerHTML = statsHTML;
  }

  /**
   * 生成重签统计数据
   */
  generateResignStats(records) {
    const total = records.length;
    const success = records.filter(r => r.status === 'success').length;
    const failed = records.filter(r => r.status === 'failed').length;
    const pending = records.filter(r => r.status === 'pending').length;
    const processing = records.filter(r => r.status === 'processing').length;

    // 计算今日记录
    const today = new Date().toDateString();
    const todayRecords = records.filter(r => new Date(r.created_at).toDateString() === today);

    // 计算成功率
    const successRate = total > 0 ? Math.round((success / total) * 100) : 0;

    return {
      total_records: total,
      success_records: success,
      failed_records: failed,
      pending_records: pending,
      processing_records: processing,
      today_records: todayRecords.length,
      success_rate: successRate
    };
  }

  /**
   * 渲染重签记录
   */
  renderResignRecords(records) {
    const recordsList = document.getElementById('resign-records-list');
    if (!recordsList) return;

    if (!records || records.length === 0) {
      recordsList.innerHTML = '<div class="text-center py-8 text-muted">暂无重签记录</div>';
      return;
    }

    const recordsHTML = records.map(record => {
      const createdAt = formatDateTime(record.created_at);
      const fileSize = formatFileSize(record.original_file_size || 0);
      const duration = record.processing_duration ? formatDuration(record.processing_duration) : '未知';

      let statusClass = 'success';
      let statusText = '成功';

      if (record.status === 'failed') {
        statusClass = 'error';
        statusText = '失败';
      } else if (record.status === 'processing') {
        statusClass = 'warning';
        statusText = '处理中';
      } else if (record.status === 'pending') {
        statusClass = 'secondary';
        statusText = '等待中';
      }

      return `
        <div class="card mb-4">
          <div class="card-body">
            <div class="flex justify-between items-start">
              <div>
                <h4 class="font-medium">${record.original_filename}</h4>
                <div class="flex gap-4 mt-2 text-sm">
                  <span><strong>状态:</strong> <span class="badge badge-${statusClass}">${statusText}</span></span>
                  <span><strong>文件大小:</strong> ${fileSize}</span>
                  <span><strong>处理时长:</strong> ${duration}</span>
                </div>
                <div class="flex gap-4 mt-1 text-sm text-secondary">
                  <span><strong>应用名称:</strong> ${record.app_name || '未知'}</span>
                  <span><strong>Bundle ID:</strong> ${record.bundle_id || '未知'}</span>
                  <span><strong>版本:</strong> ${record.version || '未知'}</span>
                </div>
                <div class="flex gap-4 mt-1 text-sm text-secondary">
                  <span><strong>创建时间:</strong> ${createdAt}</span>
                  <span><strong>用户:</strong> ${record.username || '未知'}</span>
                  ${record.error_message ? `<span class="text-error"><strong>错误:</strong> ${record.error_message}</span>` : ''}
                </div>
              </div>
              <div class="flex gap-2">
                ${record.status === 'success' && record.download_url ? `
                  <button class="btn btn-secondary btn-sm" onclick="window.open('${record.download_url}', '_blank')">
                    📥 下载
                  </button>
                ` : ''}
                <button class="btn btn-error btn-sm" onclick="window.adminPage.deleteResignRecord('${record._id || record.id}')">
                  🗑️ 删除
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
    }).join('');

    recordsList.innerHTML = recordsHTML;
  }

  /**
   * 删除重签记录
   */
  async deleteResignRecord(recordId) {
    if (confirm('确定要删除这个重签记录吗？此操作不可恢复！')) {
      try {
        const response = await api.delete(`/api/admin/resign/records/${recordId}`);

        if (response.data.success) {
          alert('✅ 重签记录已删除！');
          this.loadResignStats();
        } else {
          alert('❌ 删除失败：' + (response.data.message || '未知错误'));
        }
      } catch (error) {
        console.error('Failed to delete resign record:', error);
        alert('❌ 删除失败：' + error.message);
      }
    }
  }

  /**
   * 加载系统信息
   */
  async loadSystemInfo() {
    const systemInfo = document.getElementById('system-info');
    if (!systemInfo) return;

    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 600));

      // 模拟系统信息
      const mockInfo = {
        server_time: new Date().toISOString(),
        uptime: '15天 8小时 32分钟',
        version: '2.1.0',
        database: '正常运行'
      };

      systemInfo.innerHTML = `
        <div class="space-y-2 text-sm">
          <div><strong>服务器时间:</strong> ${formatDateTime(mockInfo.server_time)}</div>
          <div><strong>运行时间:</strong> ${mockInfo.uptime}</div>
          <div><strong>版本:</strong> ${mockInfo.version}</div>
          <div><strong>数据库:</strong> ${mockInfo.database}</div>
          <div><strong>CPU使用率:</strong> 15.2%</div>
          <div><strong>内存使用:</strong> 2.1GB / 8GB</div>
          <div><strong>磁盘空间:</strong> 45GB / 100GB</div>
        </div>
      `;
    } catch (error) {
      console.error('Failed to load system info:', error);
      systemInfo.innerHTML = '<div class="text-error">加载失败</div>';
    }
  }

  /**
   * 显示添加用户模态框
   */
  showAddUserModal() {
    const currentUser = auth.getUser();

    // 根据当前用户角色生成可选的角色选项
    let roleOptions = '';
    if (currentUser.role === 'super_admin') {
      // 超级管理员可以创建管理员和普通用户
      roleOptions = `
        <option value="user">普通用户</option>
        <option value="admin">管理员</option>
      `;
    } else if (currentUser.role === 'admin') {
      // 普通管理员只能创建普通用户
      roleOptions = `
        <option value="user">普通用户</option>
      `;
    }

    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-backdrop active"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">添加新用户</h3>
          <button class="modal-close" onclick="this.closest('.modal').remove()">×</button>
        </div>
        <div class="modal-body">
          <form id="add-user-form">
            <div class="form-group">
              <label class="form-label form-label-required">用户名</label>
              <input type="text" name="username" class="form-control" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">邮箱</label>
              <input type="email" name="email" class="form-control" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">密码</label>
              <input type="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
              <label class="form-label">角色</label>
              <select name="role" class="form-control">
                ${roleOptions}
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
          <button class="btn btn-primary" onclick="window.adminPage.submitAddUser()">添加用户</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭
    modal.querySelector('.modal-backdrop').addEventListener('click', () => {
      modal.remove();
    });
  }

  /**
   * 提交添加用户
   */
  async submitAddUser() {
    const form = document.getElementById('add-user-form');
    const formData = new FormData(form);

    const userData = {
      username: formData.get('username'),
      email: formData.get('email'),
      password: formData.get('password'),
      role: formData.get('role')
    };

    try {
      const response = await api.post('/api/admin/users', userData);

      if (response.data.success) {
        alert('✅ 用户添加成功！');
        document.querySelector('.modal').remove();
        this.loadUsers();
      } else {
        alert('❌ 添加失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to add user:', error);
      alert('❌ 添加失败：' + error.message);
    }
  }

  /**
   * 编辑用户
   */
  async editUser(userId) {
    try {
      const response = await api.get(`/api/admin/users/${userId}`);

      if (response.data.success) {
        const user = response.data.user;
        this.showEditUserModal(user);
      } else {
        alert('❌ 获取用户信息失败');
      }
    } catch (error) {
      console.error('Failed to get user:', error);
      alert('❌ 获取用户信息失败：' + error.message);
    }
  }

  /**
   * 显示编辑用户模态框
   */
  showEditUserModal(user) {
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-backdrop active"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">编辑用户</h3>
          <button class="modal-close" onclick="this.closest('.modal').remove()">×</button>
        </div>
        <div class="modal-body">
          <form id="edit-user-form">
            <input type="hidden" name="user_id" value="${user._id || user.id}">
            <div class="form-group">
              <label class="form-label form-label-required">用户名</label>
              <input type="text" name="username" class="form-control" value="${user.username}" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">邮箱</label>
              <input type="email" name="email" class="form-control" value="${user.email || ''}" required>
            </div>
            <div class="form-group">
              <label class="form-label">新密码（留空则不修改）</label>
              <input type="password" name="password" class="form-control">
            </div>
            <div class="form-group">
              <label class="form-label">角色</label>
              <select name="role" class="form-control">
                <option value="user" ${user.role === 'user' ? 'selected' : ''}>普通用户</option>
                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>管理员</option>
              </select>
            </div>
            <div class="form-group">
              <div class="form-checkbox-item">
                <input type="checkbox" name="is_expired" ${user.is_expired ? 'checked' : ''}>
                <label>账号已过期</label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
          <button class="btn btn-primary" onclick="window.adminPage.submitEditUser()">保存修改</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭
    modal.querySelector('.modal-backdrop').addEventListener('click', () => {
      modal.remove();
    });
  }

  /**
   * 提交编辑用户
   */
  async submitEditUser() {
    const form = document.getElementById('edit-user-form');
    const formData = new FormData(form);

    const userData = {
      username: formData.get('username'),
      email: formData.get('email'),
      role: formData.get('role'),
      is_expired: formData.get('is_expired') === 'on'
    };

    // 只有填写了密码才包含密码字段
    const password = formData.get('password');
    if (password) {
      userData.password = password;
    }

    const userId = formData.get('user_id');

    try {
      const response = await api.put(`/api/admin/users/${userId}`, userData);

      if (response.data.success) {
        alert('✅ 用户信息更新成功！');
        document.querySelector('.modal').remove();
        this.loadUsers();
      } else {
        alert('❌ 更新失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to update user:', error);
      alert('❌ 更新失败：' + error.message);
    }
  }

  /**
   * 切换用户状态
   */
  async toggleUserStatus(userId, currentExpired) {
    const action = currentExpired ? '激活' : '禁用';

    const confirmed = await notifications.confirm(`确定要${action}该用户吗？`);
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.put(`/api/admin/users/${userId}`, {
        is_expired: !currentExpired
      });

      if (response.data.success) {
        notifications.success(`用户已${action}！`);
        this.loadUsers();
      } else {
        notifications.error(`${action}失败：` + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error(`Failed to toggle user status:`, error);
      notifications.error(`${action}失败：` + error.message);
    }
  }

  /**
   * 删除用户
   */
  async deleteUser(userId, username) {
    const confirmed = await notifications.confirmDelete(`用户 "${username}"`, {
      message: `确定要删除用户 "${username}" 吗？此操作不可恢复！`
    });
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.delete(`/api/admin/users/${userId}`);

      if (response.data.success) {
        notifications.success('用户已删除！');
        this.loadUsers();
      } else {
        notifications.error('删除失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to delete user:', error);
      notifications.error('删除失败：' + error.message);
    }
  }

  // 系统管理方法已删除（后端未实现）
  // clearCache, backupDatabase, restartService 方法已删除

  /**
   * 显示上传描述文件模态框
   */
  showUploadProvisionModal() {
    const content = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">上传描述文件</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <form id="upload-provision-form" enctype="multipart/form-data">
            <div class="form-group">
              <label class="form-label form-label-required">描述文件</label>
              <input type="file" name="provision" class="form-control" accept=".mobileprovision" required>
              <small class="form-help">请选择.mobileprovision文件</small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">取消</button>
          <button class="btn btn-primary" onclick="window.adminPage.submitUploadProvision()">上传文件</button>
        </div>
      </div>
    `;

    const modalInfo = this.createModal(content);
    this.currentModalClose = modalInfo.close;
  }

  /**
   * 提交上传P12证书
   */
  async submitUploadP12() {
    const form = document.getElementById('upload-p12-form');
    const formData = new FormData(form);

    try {
      // 验证表单数据
      console.log('P12上传表单数据:');
      for (let [key, value] of formData.entries()) {
        console.log(key, ':', value);
      }

      // 不设置Content-Type，让浏览器自动设置multipart/form-data边界
      const response = await api.post('/api/certificates/p12', formData);

      console.log('P12上传响应:', response);

      // 处理不同的响应格式
      let success = false;
      let message = '';
      let error = '';

      if (response.data) {
        success = response.data.success;
        message = response.data.message;
        error = response.data.error;
      } else if (response.success !== false) {
        success = response.success !== false;
        error = response.error;
      }

      if (success) {
        alert('✅ P12证书上传成功！');
        if (this.currentModalClose) {
          this.currentModalClose();
          this.currentModalClose = null;
        }
        this.loadCertificates();
      } else {
        alert('❌ 上传失败：' + (error || message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to upload P12 certificate:', error);
      console.error('Error details:', error);
      alert('❌ 上传失败：' + (error.message || JSON.stringify(error)));
    }
  }

  /**
   * 提交上传描述文件
   */
  async submitUploadProvision() {
    const form = document.getElementById('upload-provision-form');
    const formData = new FormData(form);

    try {
      // 验证表单数据
      console.log('描述文件上传表单数据:');
      for (let [key, value] of formData.entries()) {
        console.log(key, ':', value);
      }

      // 不设置Content-Type，让浏览器自动设置multipart/form-data边界
      const response = await api.post('/api/certificates/mobileprovision', formData);

      console.log('描述文件上传响应:', response);

      // 处理不同的响应格式
      let success = false;
      let message = '';
      let error = '';

      if (response.data) {
        success = response.data.success;
        message = response.data.message;
        error = response.data.error;
      } else if (response.success !== false) {
        success = response.success !== false;
        error = response.error;
      }

      if (success) {
        alert('✅ 描述文件上传成功！');
        if (this.currentModalClose) {
          this.currentModalClose();
          this.currentModalClose = null;
        }
        this.loadCertificates();
      } else {
        alert('❌ 上传失败：' + (error || message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to upload provision file:', error);
      console.error('Error details:', error);
      alert('❌ 上传失败：' + (error.message || JSON.stringify(error)));
    }
  }



  /**
   * 删除证书
   */
  async deleteCertificate(certId, certName) {
    const confirmed = await notifications.confirmDelete(`证书 "${certName}"`, {
      message: `确定要删除证书 "${certName}" 吗？此操作不可恢复！`
    });
    if (!confirmed) {
      return;
    }

    try {
      const response = await api.delete(`/api/certificates/${certId}`);

      console.log('删除证书响应:', response);

      // 处理不同的响应格式
      let success = false;
      let message = '';
      let error = '';

      if (response.data) {
        success = response.data.success;
        message = response.data.message;
        error = response.data.error;
      } else if (response.success !== false) {
        success = response.success !== false;
        error = response.error;
      }

      if (success) {
        notifications.success('证书已删除！');
        this.loadCertificates();
      } else {
        notifications.error('删除失败：' + (error || message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to delete certificate:', error);
      notifications.error('删除失败：' + (error.message || '未知错误'));
    }
  }

  /**
   * 显示上传记录批量删除模态框
   */
  showBatchDeleteUploadModal() {
    const content = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">批量删除上传记录</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">删除条件</label>
            <select id="upload-delete-condition" class="form-control">
              <option value="oldest">删除最早的记录</option>
              <option value="failed">删除失败的记录</option>
              <option value="completed">删除已完成的记录</option>
              <option value="date-range">删除指定时间段的记录</option>
              <option value="status">按状态删除</option>
            </select>
          </div>

          <div id="upload-delete-options" class="mt-4">
            <!-- 动态选项区域 -->
          </div>

          <div class="form-group">
            <label class="form-label">删除数量限制</label>
            <input type="number" id="upload-delete-limit" class="form-control" value="10" min="1" max="100">
            <small class="form-help">最多删除100条记录</small>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">取消</button>
          <button class="btn btn-error" onclick="window.adminPage.executeBatchDeleteUpload()">确认删除</button>
        </div>
      </div>
    `;

    const modalInfo = this.createModal(content);
    this.currentModalClose = modalInfo.close;

    // 添加条件变化监听器
    const conditionSelect = document.getElementById('upload-delete-condition');
    if (conditionSelect) {
      conditionSelect.addEventListener('change', () => this.updateUploadDeleteOptions());
      this.updateUploadDeleteOptions(); // 初始化选项
    }
  }

  /**
   * 更新上传删除选项
   */
  updateUploadDeleteOptions() {
    const condition = document.getElementById('upload-delete-condition').value;
    const optionsContainer = document.getElementById('upload-delete-options');

    let optionsHTML = '';

    switch (condition) {
      case 'oldest':
        optionsHTML = '<p class="text-muted">将删除创建时间最早的记录</p>';
        break;
      case 'failed':
        optionsHTML = '<p class="text-muted">将删除状态为"失败"的记录</p>';
        break;
      case 'completed':
        optionsHTML = '<p class="text-muted">将删除状态为"已完成"的记录</p>';
        break;
      case 'date-range':
        optionsHTML = `
          <div class="form-group">
            <label class="form-label">开始日期</label>
            <input type="date" id="upload-start-date" class="form-control" required>
          </div>
          <div class="form-group">
            <label class="form-label">结束日期</label>
            <input type="date" id="upload-end-date" class="form-control" required>
          </div>
        `;
        break;
      case 'status':
        optionsHTML = `
          <div class="form-group">
            <label class="form-label">选择状态</label>
            <select id="upload-target-status" class="form-control">
              <option value="queued">队列中</option>
              <option value="uploading">上传中</option>
              <option value="completed">已完成</option>
              <option value="failed">失败</option>
            </select>
          </div>
        `;
        break;
    }

    optionsContainer.innerHTML = optionsHTML;
  }

  /**
   * 执行上传记录批量删除
   */
  async executeBatchDeleteUpload() {
    const condition = document.getElementById('upload-delete-condition').value;
    const limit = parseInt(document.getElementById('upload-delete-limit').value) || 10;

    let params = { limit };

    // 根据条件添加参数
    switch (condition) {
      case 'oldest':
        params.sort = 'oldest';
        break;
      case 'failed':
        params.status = 'failed';
        break;
      case 'completed':
        params.status = 'completed';
        break;
      case 'date-range':
        const startDate = document.getElementById('upload-start-date').value;
        const endDate = document.getElementById('upload-end-date').value;
        if (!startDate || !endDate) {
          alert('请选择开始和结束日期');
          return;
        }
        params.start_date = startDate;
        params.end_date = endDate;
        break;
      case 'status':
        params.status = document.getElementById('upload-target-status').value;
        break;
    }

    if (!confirm(`确定要删除符合条件的上传记录吗？最多删除 ${limit} 条记录。此操作不可恢复！`)) {
      return;
    }

    try {
      const queryString = new URLSearchParams(params).toString();
      const response = await api.delete(`/api/admin/upload/records/batch?${queryString}`);

      console.log('批量删除上传记录响应:', response);

      // 处理响应
      let success = false;
      let message = '';
      let deletedCount = 0;

      if (response.data) {
        success = response.data.success;
        message = response.data.message;
        deletedCount = response.data.deleted_count || 0;
      } else if (response.success !== false) {
        success = response.success !== false;
        deletedCount = response.deleted_count || 0;
      }

      if (success) {
        alert(`✅ 成功删除 ${deletedCount} 条上传记录！`);
        if (this.currentModalClose) {
          this.currentModalClose();
          this.currentModalClose = null;
        }
        this.loadUploadStats();
      } else {
        alert('❌ 批量删除失败：' + (message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to batch delete upload records:', error);
      alert('❌ 批量删除失败：' + error.message);
    }
  }

  /**
   * 显示重签记录批量删除模态框
   */
  showBatchDeleteModal() {
    const content = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">批量删除重签记录</h3>
          <button class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">删除条件</label>
            <select id="delete-condition" class="form-control">
              <option value="oldest">删除最早的记录</option>
              <option value="failed">删除失败的记录</option>
              <option value="date-range">删除指定时间段的记录</option>
              <option value="status">按状态删除</option>
            </select>
          </div>

          <div id="delete-options" class="mt-4">
            <!-- 动态选项区域 -->
          </div>

          <div class="form-group">
            <label class="form-label">删除数量限制</label>
            <input type="number" id="delete-limit" class="form-control" value="10" min="1" max="100">
            <small class="form-help">最多删除100条记录</small>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">取消</button>
          <button class="btn btn-error" onclick="window.adminPage.executeBatchDelete()">确认删除</button>
        </div>
      </div>
    `;

    const modalInfo = this.createModal(content);
    this.currentModalClose = modalInfo.close;

    // 添加条件变化监听器
    const conditionSelect = document.getElementById('delete-condition');
    if (conditionSelect) {
      conditionSelect.addEventListener('change', () => this.updateDeleteOptions());
      this.updateDeleteOptions(); // 初始化选项
    }
  }

  /**
   * 更新删除选项
   */
  updateDeleteOptions() {
    const condition = document.getElementById('delete-condition').value;
    const optionsContainer = document.getElementById('delete-options');

    let optionsHTML = '';

    switch (condition) {
      case 'oldest':
        optionsHTML = '<p class="text-muted">将删除创建时间最早的记录</p>';
        break;
      case 'failed':
        optionsHTML = '<p class="text-muted">将删除状态为"失败"的记录</p>';
        break;
      case 'date-range':
        optionsHTML = `
          <div class="form-group">
            <label class="form-label">开始日期</label>
            <input type="date" id="start-date" class="form-control" required>
          </div>
          <div class="form-group">
            <label class="form-label">结束日期</label>
            <input type="date" id="end-date" class="form-control" required>
          </div>
        `;
        break;
      case 'status':
        optionsHTML = `
          <div class="form-group">
            <label class="form-label">选择状态</label>
            <select id="target-status" class="form-control">
              <option value="pending">等待中</option>
              <option value="processing">处理中</option>
              <option value="success">成功</option>
              <option value="failed">失败</option>
            </select>
          </div>
        `;
        break;
    }

    optionsContainer.innerHTML = optionsHTML;
  }

  /**
   * 执行批量删除
   */
  async executeBatchDelete() {
    const condition = document.getElementById('delete-condition').value;
    const limit = parseInt(document.getElementById('delete-limit').value) || 10;

    let params = { limit };

    // 根据条件添加参数
    switch (condition) {
      case 'oldest':
        params.sort = 'oldest';
        break;
      case 'failed':
        params.status = 'failed';
        break;
      case 'date-range':
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        if (!startDate || !endDate) {
          alert('请选择开始和结束日期');
          return;
        }
        params.start_date = startDate;
        params.end_date = endDate;
        break;
      case 'status':
        params.status = document.getElementById('target-status').value;
        break;
    }

    if (!confirm(`确定要删除符合条件的记录吗？最多删除 ${limit} 条记录。此操作不可恢复！`)) {
      return;
    }

    try {
      const queryString = new URLSearchParams(params).toString();
      const response = await api.delete(`/api/admin/resign/records/batch?${queryString}`);

      console.log('批量删除响应:', response);

      // 处理响应
      let success = false;
      let message = '';
      let deletedCount = 0;

      if (response.data) {
        success = response.data.success;
        message = response.data.message;
        deletedCount = response.data.deleted_count || 0;
      } else if (response.success !== false) {
        success = response.success !== false;
        deletedCount = response.deleted_count || 0;
      }

      if (success) {
        alert(`✅ 成功删除 ${deletedCount} 条记录！`);
        if (this.currentModalClose) {
          this.currentModalClose();
          this.currentModalClose = null;
        }
        this.loadResignStats();
      } else {
        alert('❌ 批量删除失败：' + (message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to batch delete records:', error);
      alert('❌ 批量删除失败：' + error.message);
    }
  }
}

// 创建页面实例
new AdminPage();
