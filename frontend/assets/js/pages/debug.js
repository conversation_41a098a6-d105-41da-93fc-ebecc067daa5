/**
 * 调试页面
 * 用于测试各个组件和功能模块
 */

import { createFormField } from '../components/Form.js';
import { createButton } from '../components/Button.js';
import { createCard } from '../components/Card.js';
import api from '../core/api.js';
import router from '../core/router.js';

class DebugPage {
  constructor() {
    this.init();
  }
  
  /**
   * 初始化调试页面
   */
  init() {
    // 绑定全局测试函数
    window.testComponents = () => this.testComponents();
    window.testFormComponents = () => this.testFormComponents();
    window.testAPI = () => this.testAPI();
    window.testRouter = () => this.testRouter();
    
    // 页面加载完成提示
    this.addResult('🚀 调试页面加载完成', 'success');
    console.log('🔧 XIOS 调试页面已准备就绪');
  }
  
  /**
   * 测试基础组件
   */
  testComponents() {
    const results = document.getElementById('component-test-results');
    results.innerHTML = '';
    
    try {
      // 测试按钮组件
      const button = createButton({
        text: '测试按钮',
        type: 'primary',
        onClick: () => this.addResult('按钮点击成功！', 'success')
      });
      
      this.addResult('✅ 按钮组件创建成功', 'success');
      
      // 测试卡片组件
      const card = createCard({
        title: '测试卡片',
        content: '这是一个测试卡片'
      });
      
      this.addResult('✅ 卡片组件创建成功', 'success');
      
      // 测试组件挂载
      const testContainer = document.createElement('div');
      testContainer.style.cssText = 'margin-top: 1rem; padding: 1rem; border: 1px solid #ddd; border-radius: 4px;';
      
      if (button.mount) {
        button.mount(testContainer);
        this.addResult('✅ 按钮组件挂载成功', 'success');
      }
      
      results.appendChild(testContainer);
      
    } catch (error) {
      this.addResult('❌ 组件测试失败: ' + error.message, 'error');
      console.error('Component test error:', error);
    }
  }
  
  /**
   * 测试表单组件
   */
  testFormComponents() {
    const container = document.getElementById('form-test-container');
    const results = document.getElementById('form-test-results');
    results.innerHTML = '';
    container.innerHTML = '';
    
    try {
      // 测试文本输入
      const textField = createFormField({
        type: 'text',
        name: 'test_text',
        label: '测试文本输入',
        placeholder: '请输入文本'
      });
      
      textField.mount(container);
      this.addResult('✅ 文本输入组件创建并挂载成功', 'success', results);
      
      // 测试选择框
      const selectField = createFormField({
        type: 'select',
        name: 'test_select',
        label: '测试选择框',
        options: [
          { value: '1', label: '选项1' },
          { value: '2', label: '选项2' }
        ]
      });
      
      selectField.mount(container);
      this.addResult('✅ 选择框组件创建并挂载成功', 'success', results);
      
      // 测试文件上传
      const fileField = createFormField({
        type: 'file',
        name: 'test_file',
        label: '测试文件上传',
        accept: '.txt,.pdf'
      });
      
      fileField.mount(container);
      this.addResult('✅ 文件上传组件创建并挂载成功', 'success', results);
      
      // 测试单选框
      const radioField = createFormField({
        type: 'radio',
        name: 'test_radio',
        label: '测试单选框',
        options: [
          { value: 'option1', label: '选项1' },
          { value: 'option2', label: '选项2' }
        ]
      });
      
      radioField.mount(container);
      this.addResult('✅ 单选框组件创建并挂载成功', 'success', results);
      
      // 测试复选框
      const checkboxField = createFormField({
        type: 'checkbox',
        name: 'test_checkbox',
        label: '测试复选框',
        options: [
          { value: 'check1', label: '复选项1' },
          { value: 'check2', label: '复选项2' }
        ]
      });
      
      checkboxField.mount(container);
      this.addResult('✅ 复选框组件创建并挂载成功', 'success', results);
      
    } catch (error) {
      this.addResult('❌ 表单组件测试失败: ' + error.message, 'error', results);
      console.error('Form component test error:', error);
    }
  }
  
  /**
   * 测试API模块
   */
  async testAPI() {
    const results = document.getElementById('api-test-results');
    results.innerHTML = '';
    
    try {
      this.addResult('✅ API模块导入成功', 'success', results);
      this.addResult('API基础URL: ' + api.baseURL, 'info', results);
      
      // 测试API配置
      this.addResult('🔄 正在测试API配置...', 'warning', results);
      
      // 测试请求拦截器
      if (api.interceptors) {
        this.addResult('✅ 请求拦截器配置正常', 'success', results);
      }
      
      // 测试模拟请求
      this.addResult('🔄 正在测试模拟请求...', 'warning', results);
      
      setTimeout(() => {
        this.addResult('✅ 模拟请求测试完成', 'success', results);
      }, 1000);
      
      // 测试错误处理
      try {
        await api.get('/api/test/nonexistent');
      } catch (error) {
        this.addResult('✅ API错误处理正常: ' + error.message, 'success', results);
      }
      
    } catch (error) {
      this.addResult('❌ API测试失败: ' + error.message, 'error', results);
      console.error('API test error:', error);
    }
  }
  
  /**
   * 测试路由模块
   */
  testRouter() {
    const results = document.getElementById('router-test-results');
    results.innerHTML = '';
    
    try {
      this.addResult('✅ 路由模块导入成功', 'success', results);
      this.addResult('当前路径: ' + router.getCurrentPath(), 'info', results);
      this.addResult('路由数量: ' + router.routes.size, 'info', results);
      
      // 测试路由方法
      if (typeof router.navigateTo === 'function') {
        this.addResult('✅ 路由导航方法可用', 'success', results);
      }
      
      if (typeof router.addRoute === 'function') {
        this.addResult('✅ 路由添加方法可用', 'success', results);
      }
      
      // 测试路由事件
      if (router.addEventListener) {
        this.addResult('✅ 路由事件系统可用', 'success', results);
      }
      
    } catch (error) {
      this.addResult('❌ 路由测试失败: ' + error.message, 'error', results);
      console.error('Router test error:', error);
    }
  }
  
  /**
   * 添加测试结果
   */
  addResult(message, type = 'info', container = null) {
    if (!container) {
      container = document.getElementById('component-test-results');
    }
    
    const div = document.createElement('div');
    div.className = `debug-info debug-${type}`;
    div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
    container.appendChild(div);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new DebugPage();
});

export default DebugPage;
