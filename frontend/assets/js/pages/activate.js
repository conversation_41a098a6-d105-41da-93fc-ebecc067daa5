/**
 * 账号激活页面
 * 处理用户账号激活功能
 */

import api from '../core/api.js';

class ActivatePage {
  constructor() {
    this.form = document.getElementById('activate-form');
    this.statusMessage = document.getElementById('status-message');
    this.loadingOverlay = document.getElementById('loading-overlay');
    this.activateBtn = document.getElementById('activate-btn');
    
    this.init();
  }
  
  /**
   * 初始化页面
   */
  init() {
    // 绑定表单提交事件
    this.form.addEventListener('submit', (e) => this.handleSubmit(e));
    
    // 绑定输入框事件
    const inputs = this.form.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('input', () => this.clearStatus());
    });
    
    // 检查URL参数中的激活码
    this.checkActivationCodeFromURL();
    
    // 检查sessionStorage中的用户名（从登录页面跳转过来）
    this.checkUsernameFromSession();
  }
  
  /**
   * 检查URL参数中的激活码
   */
  checkActivationCodeFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    
    if (code) {
      document.getElementById('activation-code').value = code;
    }
  }
  
  /**
   * 检查sessionStorage中的用户名
   */
  checkUsernameFromSession() {
    const savedUsername = sessionStorage.getItem('activation_username');
    if (savedUsername) {
      const usernameInput = document.getElementById('username');
      if (usernameInput) {
        usernameInput.value = savedUsername;
        // 清除sessionStorage中的用户名
        sessionStorage.removeItem('activation_username');
        // 自动聚焦到激活码输入框
        const activationCodeInput = document.getElementById('activation-code');
        if (activationCodeInput) {
          activationCodeInput.focus();
        }
      }
    }
  }
  
  /**
   * 处理表单提交
   */
  async handleSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(this.form);
    const activationData = {
      activation_code: formData.get('activation_code'),
      username: formData.get('username'),
      password: formData.get('password'),
      confirm_password: formData.get('confirm_password'),
      email: formData.get('email')
    };
    
    // 基本验证
    if (!this.validateForm(activationData)) {
      return;
    }
    
    try {
      this.showLoading(true);
      this.clearStatus();
      
      const response = await api.post('/api/auth/activate', activationData);
      
      if (response.data.success) {
        let successMessage = '✅ 账号激活成功！';
        
        // 显示有效期信息
        if (response.data.expires_at) {
          successMessage += `\n有效期至: ${response.data.expires_at}`;
        }
        if (response.data.validity_days) {
          successMessage += `\n有效期: ${response.data.validity_days} 天`;
        }
        
        successMessage += '\n正在跳转到登录页面...';
        
        this.showStatus(successMessage, 'success');
        
        // 3秒后跳转到登录页面
        setTimeout(() => {
          window.location.href = 'login.html';
        }, 3000);
      } else {
        this.showStatus('❌ 激活失败：' + (response.data.message || '未知错误'), 'error');
      }
      
    } catch (error) {
      console.error('Activation failed:', error);
      this.handleActivationError(error);
    } finally {
      this.showLoading(false);
    }
  }
  
  /**
   * 表单验证
   */
  validateForm(data) {
    if (!data.activation_code || !data.username || !data.password) {
      this.showStatus('请填写所有必填字段', 'error');
      return false;
    }
    
    if (data.password !== data.confirm_password) {
      this.showStatus('两次输入的密码不一致', 'error');
      return false;
    }
    
    if (data.password.length < 6) {
      this.showStatus('密码长度至少6位', 'error');
      return false;
    }
    
    // 验证用户名格式
    if (!/^[a-zA-Z0-9_]{3,20}$/.test(data.username)) {
      this.showStatus('用户名只能包含字母、数字和下划线，长度3-20位', 'error');
      return false;
    }
    
    // 验证邮箱格式（如果填写了）
    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      this.showStatus('邮箱格式不正确', 'error');
      return false;
    }
    
    return true;
  }
  
  /**
   * 处理激活错误
   */
  handleActivationError(error) {
    let errorMessage = '激活失败';
    
    if (error.status === 400) {
      errorMessage = '激活码无效或已过期';
    } else if (error.status === 409) {
      errorMessage = '用户名已存在，请选择其他用户名';
    } else if (error.status === 422) {
      errorMessage = '输入信息格式不正确';
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    this.showStatus('❌ ' + errorMessage, 'error');
  }
  
  /**
   * 显示状态消息
   */
  showStatus(message, type = 'info') {
    this.statusMessage.innerHTML = message.replace(/\n/g, '<br>');
    this.statusMessage.className = `status-message status-${type}`;
    this.statusMessage.style.display = 'block';
  }
  
  /**
   * 清除状态消息
   */
  clearStatus() {
    this.statusMessage.style.display = 'none';
  }
  
  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    if (show) {
      this.loadingOverlay.style.display = 'flex';
      this.activateBtn.disabled = true;
    } else {
      this.loadingOverlay.style.display = 'none';
      this.activateBtn.disabled = false;
    }
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new ActivatePage();
});

export default ActivatePage;
