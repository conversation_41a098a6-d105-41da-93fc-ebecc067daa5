/**
 * 路由管理模块
 * 处理单页应用的路由切换和页面管理
 */

import auth from './auth.js';

class Router {
  constructor() {
    this.routes = new Map();
    this.currentRoute = null;
    this.currentPage = null;
    this.isInitialized = false;
    
    // 绑定事件监听器
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听浏览器前进后退
    window.addEventListener('popstate', (e) => {
      this.handlePopState(e);
    });

    // 监听认证状态变化
    window.addEventListener('auth:login', () => {
      this.handleAuthChange();
    });

    window.addEventListener('auth:logout', () => {
      this.handleAuthChange();
    });

    window.addEventListener('auth:unauthorized', () => {
      this.navigateTo('/login');
    });
  }

  /**
   * 注册路由
   */
  register(path, config) {
    this.routes.set(path, {
      path,
      component: config.component,
      title: config.title || 'XIOS',
      requireAuth: config.requireAuth !== false, // 默认需要认证
      requireAdmin: config.requireAdmin || false,
      beforeEnter: config.beforeEnter,
      afterEnter: config.afterEnter,
      ...config
    });
  }

  /**
   * 批量注册路由
   */
  registerRoutes(routes) {
    routes.forEach(route => {
      this.register(route.path, route);
    });
  }

  /**
   * 初始化路由器
   */
  async init() {
    try {
      // 等待认证初始化完成
      await auth.waitForInit();

      // 获取当前路径
      const currentPath = this.getCurrentPath();
      console.log('🔄 初始化路由器，当前路径:', currentPath);

      // 导航到当前路径
      await this.navigateTo(currentPath, { replace: true });

      this.isInitialized = true;
      this.dispatchEvent('router:initialized');
      console.log('✅ 路由器初始化完成');
    } catch (error) {
      console.error('❌ 路由器初始化失败:', error);
      // 如果初始化失败，至少显示默认页面
      this.showDefaultPage();
    }
  }

  /**
   * 显示默认页面
   */
  showDefaultPage() {
    const defaultPage = document.getElementById('upload-page');
    if (defaultPage) {
      // 隐藏所有页面
      const allPages = document.querySelectorAll('.section');
      allPages.forEach(page => page.style.display = 'none');

      // 显示默认页面
      defaultPage.style.display = 'block';
      this.currentPage = defaultPage;
      console.log('📄 显示默认页面: upload-page');
    }
  }

  /**
   * 获取当前路径
   */
  getCurrentPath() {
    const hash = window.location.hash;
    if (hash.startsWith('#/')) {
      return hash.substring(1);
    }
    return '/';
  }

  /**
   * 导航到指定路径
   */
  async navigateTo(path, options = {}) {
    const { replace = false, state = null } = options;
    
    try {
      // 查找匹配的路由
      const route = this.findRoute(path);
      
      if (!route) {
        console.warn(`Route not found: ${path}`);
        return this.navigateTo('/404', { replace: true });
      }

      // 检查权限
      const authCheck = await this.checkAuth(route);
      if (!authCheck.allowed) {
        return this.navigateTo(authCheck.redirectTo, { replace: true });
      }

      // 执行路由前置守卫
      if (route.beforeEnter) {
        const guardResult = await route.beforeEnter(route, this.currentRoute);
        if (guardResult === false) {
          return; // 阻止导航
        }
        if (typeof guardResult === 'string') {
          return this.navigateTo(guardResult, { replace: true });
        }
      }

      // 更新浏览器历史
      this.updateHistory(path, replace, state);

      // 切换页面
      await this.switchPage(route);

      // 更新当前路由
      this.currentRoute = route;

      // 执行路由后置守卫
      if (route.afterEnter) {
        await route.afterEnter(route);
      }

      // 触发路由变化事件
      this.dispatchEvent('router:navigated', { route, path });

    } catch (error) {
      console.error('Navigation failed:', error);
      this.dispatchEvent('router:error', { error, path });
    }
  }

  /**
   * 查找匹配的路由
   */
  findRoute(path) {
    // 精确匹配
    if (this.routes.has(path)) {
      return this.routes.get(path);
    }

    // 参数匹配（简单实现）
    for (const [routePath, route] of this.routes) {
      if (this.matchPath(routePath, path)) {
        return { ...route, params: this.extractParams(routePath, path) };
      }
    }

    return null;
  }

  /**
   * 路径匹配
   */
  matchPath(routePath, actualPath) {
    const routeParts = routePath.split('/');
    const actualParts = actualPath.split('/');

    if (routeParts.length !== actualParts.length) {
      return false;
    }

    return routeParts.every((part, index) => {
      return part.startsWith(':') || part === actualParts[index];
    });
  }

  /**
   * 提取路径参数
   */
  extractParams(routePath, actualPath) {
    const routeParts = routePath.split('/');
    const actualParts = actualPath.split('/');
    const params = {};

    routeParts.forEach((part, index) => {
      if (part.startsWith(':')) {
        const paramName = part.substring(1);
        params[paramName] = actualParts[index];
      }
    });

    return params;
  }

  /**
   * 检查认证权限
   */
  async checkAuth(route) {
    // 不需要认证的路由
    if (!route.requireAuth) {
      return { allowed: true };
    }

    // 检查是否已登录
    if (!auth.isAuthenticated()) {
      return { allowed: false, redirectTo: '/login' };
    }

    // 检查管理员权限
    if (route.requireAdmin && !auth.isAdmin()) {
      return { allowed: false, redirectTo: '/' };
    }

    // 检查用户是否过期
    if (auth.isExpired() && route.path !== '/expired') {
      return { allowed: false, redirectTo: '/expired' };
    }

    return { allowed: true };
  }

  /**
   * 更新浏览器历史
   */
  updateHistory(path, replace, state) {
    const url = `#${path}`;
    
    if (replace) {
      window.history.replaceState(state, '', url);
    } else {
      window.history.pushState(state, '', url);
    }
  }

  /**
   * 切换页面
   */
  async switchPage(route) {
    try {
      // 隐藏所有页面
      const allPages = document.querySelectorAll('.section');
      allPages.forEach(page => {
        page.style.display = 'none';
      });

      // 查找目标页面元素
      const pageElement = document.getElementById(route.component);

      if (!pageElement) {
        console.error(`Page component not found: ${route.component}`);
        // 显示404页面
        const notFoundPage = document.getElementById('404-page');
        if (notFoundPage) {
          notFoundPage.style.display = 'block';
          this.currentPage = notFoundPage;
        }
        return;
      }

      // 显示目标页面
      pageElement.style.display = 'block';
      this.currentPage = pageElement;

      // 更新页面标题
      document.title = route.title;

      // 更新导航状态
      this.updateNavigation(route);

      // 触发页面切换事件
      this.dispatchEvent('router:page-changed', { route, element: pageElement });

      console.log(`✅ 页面切换成功: ${route.path} -> ${route.component}`);

    } catch (error) {
      console.error('Page switch failed:', error);
      throw error;
    }
  }

  /**
   * 更新导航状态
   */
  updateNavigation(route) {
    // 更新导航栏激活状态
    const navLinks = document.querySelectorAll('[data-nav-link]');
    navLinks.forEach(link => {
      const linkPath = link.getAttribute('data-nav-link');
      if (linkPath === route.path) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });

    // 更新面包屑
    this.updateBreadcrumb(route);
  }

  /**
   * 更新面包屑
   */
  updateBreadcrumb(route) {
    const breadcrumbContainer = document.querySelector('.navbar-breadcrumb-nav');
    if (!breadcrumbContainer) return;

    // 清空现有面包屑
    breadcrumbContainer.innerHTML = '';

    // 生成面包屑项目
    const breadcrumbs = this.generateBreadcrumbs(route);
    
    breadcrumbs.forEach((item, index) => {
      const li = document.createElement('li');
      li.className = 'navbar-breadcrumb-item';
      
      if (index === breadcrumbs.length - 1) {
        // 最后一项（当前页面）
        li.innerHTML = `<span class="navbar-breadcrumb-current">${item.title}</span>`;
      } else {
        // 可点击的链接
        li.innerHTML = `<a href="#${item.path}" class="navbar-breadcrumb-link">${item.title}</a>`;
      }
      
      breadcrumbContainer.appendChild(li);
    });
  }

  /**
   * 生成面包屑数据
   */
  generateBreadcrumbs(route) {
    const breadcrumbs = [{ path: '/', title: '首页' }];
    
    // 根据路径生成面包屑
    const pathParts = route.path.split('/').filter(part => part);
    let currentPath = '';
    
    pathParts.forEach(part => {
      currentPath += `/${part}`;
      const matchedRoute = this.findRoute(currentPath);
      
      if (matchedRoute && matchedRoute.title) {
        breadcrumbs.push({
          path: currentPath,
          title: matchedRoute.title
        });
      }
    });

    return breadcrumbs;
  }

  /**
   * 处理浏览器前进后退
   */
  async handlePopState(e) {
    const path = this.getCurrentPath();
    await this.navigateTo(path, { replace: true });
  }

  /**
   * 处理认证状态变化
   */
  handleAuthChange() {
    // 重新检查当前路由的权限
    if (this.currentRoute) {
      this.navigateTo(this.currentRoute.path, { replace: true });
    }
  }

  /**
   * 触发自定义事件
   */
  dispatchEvent(eventName, detail = {}) {
    window.dispatchEvent(new CustomEvent(eventName, { detail }));
  }

  /**
   * 返回上一页
   */
  back() {
    window.history.back();
  }

  /**
   * 前进到下一页
   */
  forward() {
    window.history.forward();
  }

  /**
   * 刷新当前页面
   */
  refresh() {
    if (this.currentRoute) {
      this.navigateTo(this.currentRoute.path, { replace: true });
    }
  }
}

// 创建全局路由器实例
const router = new Router();

// 导出路由器
export default router;
