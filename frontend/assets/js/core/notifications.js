/**
 * 通知系统核心模块
 * 统一管理 Toast 和 Modal 通知
 */

import toast from '../components/Toast.js';
import ModalFactory from '../components/Modal.js';

/**
 * 通知管理器
 */
class NotificationManager {
  constructor() {
    this.toast = toast;
    this.modal = ModalFactory;
    
    // 初始化配置
    this.init();
  }

  /**
   * 初始化通知系统
   */
  init() {
    // 设置默认配置
    this.toast.updateConfig({
      position: 'top-right',
      duration: 4000,
      maxToasts: 5
    });
  }

  /**
   * 显示成功消息
   */
  success(message, options = {}) {
    const config = {
      duration: 3000,
      ...options
    };
    return this.toast.success(message, config);
  }

  /**
   * 显示错误消息
   */
  error(message, options = {}) {
    const config = {
      duration: 5000,
      ...options
    };
    return this.toast.error(message, config);
  }

  /**
   * 显示警告消息
   */
  warning(message, options = {}) {
    const config = {
      duration: 4000,
      ...options
    };
    return this.toast.warning(message, config);
  }

  /**
   * 显示信息消息
   */
  info(message, options = {}) {
    const config = {
      duration: 4000,
      ...options
    };
    return this.toast.info(message, config);
  }

  /**
   * 显示确认对话框
   */
  async confirm(message, options = {}) {
    const config = {
      title: '确认操作',
      message: message,
      confirmText: '确认',
      cancelText: '取消',
      type: 'warning',
      ...options
    };
    
    return await this.modal.confirm(config);
  }

  /**
   * 显示警告对话框
   */
  async alert(message, options = {}) {
    const config = {
      title: '提示',
      message: message,
      buttonText: '确定',
      type: 'info',
      ...options
    };
    
    return await this.modal.alert(config);
  }

  /**
   * 显示成功对话框
   */
  async alertSuccess(message, options = {}) {
    const config = {
      title: '成功',
      message: message,
      buttonText: '确定',
      type: 'success',
      ...options
    };
    
    return await this.modal.alert(config);
  }

  /**
   * 显示错误对话框
   */
  async alertError(message, options = {}) {
    const config = {
      title: '错误',
      message: message,
      buttonText: '确定',
      type: 'error',
      ...options
    };
    
    return await this.modal.alert(config);
  }

  /**
   * 显示警告对话框
   */
  async alertWarning(message, options = {}) {
    const config = {
      title: '警告',
      message: message,
      buttonText: '确定',
      type: 'warning',
      ...options
    };
    
    return await this.modal.alert(config);
  }

  /**
   * 显示删除确认对话框
   */
  async confirmDelete(itemName = '此项目', options = {}) {
    const config = {
      title: '确认删除',
      message: `您确定要删除${itemName}吗？此操作无法撤销。`,
      confirmText: '删除',
      cancelText: '取消',
      type: 'error',
      ...options
    };
    
    return await this.modal.confirm(config);
  }

  /**
   * 显示复制成功提示
   */
  copySuccess(content = '内容') {
    return this.success(`✅ ${content}已复制到剪贴板`);
  }

  /**
   * 显示加载提示
   */
  loading(message = '加载中...', options = {}) {
    const config = {
      duration: 0, // 不自动关闭
      closable: false,
      ...options
    };
    return this.info(message, config);
  }

  /**
   * 清除所有通知
   */
  clearAll() {
    this.toast.clearAll();
  }

  /**
   * 隐藏指定的通知
   */
  hide(toastId) {
    this.toast.hide(toastId);
  }

  /**
   * 替代原生 alert 的方法
   */
  async nativeAlert(message) {
    // 检测消息类型并选择合适的显示方式
    if (message.includes('✅') || message.includes('成功')) {
      // 成功消息使用 toast
      return this.success(message.replace(/^✅\s*/, ''));
    } else if (message.includes('❌') || message.includes('错误') || message.includes('失败')) {
      // 错误消息使用 toast
      return this.error(message.replace(/^❌\s*/, ''));
    } else if (message.includes('⚠') || message.includes('警告')) {
      // 警告消息使用 toast
      return this.warning(message.replace(/^⚠\s*/, ''));
    } else {
      // 其他消息使用 toast
      return this.info(message);
    }
  }

  /**
   * 替代原生 confirm 的方法
   */
  async nativeConfirm(message) {
    return await this.confirm(message);
  }

  /**
   * 显示网络错误提示
   */
  networkError(error = null) {
    let message = '网络连接失败，请检查网络设置后重试';
    
    if (error) {
      if (error.status === 401) {
        message = '登录已过期，请重新登录';
      } else if (error.status === 403) {
        message = '权限不足，无法执行此操作';
      } else if (error.status === 404) {
        message = '请求的资源不存在';
      } else if (error.status === 500) {
        message = '服务器内部错误，请稍后重试';
      } else if (error.message) {
        message = error.message;
      }
    }
    
    return this.error(message);
  }

  /**
   * 显示表单验证错误
   */
  validationError(message) {
    return this.warning(message);
  }

  /**
   * 显示操作成功提示
   */
  operationSuccess(operation = '操作') {
    return this.success(`${operation}成功`);
  }

  /**
   * 显示操作失败提示
   */
  operationError(operation = '操作', error = null) {
    let message = `${operation}失败`;
    if (error && error.message) {
      message += `：${error.message}`;
    }
    return this.error(message);
  }

  /**
   * 显示文件上传成功提示
   */
  uploadSuccess(fileName = '文件') {
    return this.success(`${fileName}上传成功`);
  }

  /**
   * 显示文件上传失败提示
   */
  uploadError(fileName = '文件', error = null) {
    let message = `${fileName}上传失败`;
    if (error && error.message) {
      message += `：${error.message}`;
    }
    return this.error(message);
  }

  /**
   * 显示保存成功提示
   */
  saveSuccess() {
    return this.success('保存成功');
  }

  /**
   * 显示保存失败提示
   */
  saveError(error = null) {
    let message = '保存失败';
    if (error && error.message) {
      message += `：${error.message}`;
    }
    return this.error(message);
  }
}

// 创建全局实例
const notifications = new NotificationManager();

// 全局方法，用于替代原生 alert 和 confirm
window.alert = (message) => {
  notifications.nativeAlert(message);
};

window.confirm = async (message) => {
  return await notifications.nativeConfirm(message);
};

// 导出实例和类
export { NotificationManager };
export default notifications;
