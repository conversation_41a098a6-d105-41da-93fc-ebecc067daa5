/**
 * 认证管理模块
 * 处理用户登录、登出、权限验证等功能
 */

import api from './api.js';

class AuthManager {
  constructor() {
    this.user = null;
    this.token = null;
    this.isInitialized = false;
    
    // 绑定事件监听器
    this.setupEventListeners();
    
    // 初始化认证状态
    this.init();
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 监听未授权事件
    window.addEventListener('auth:unauthorized', () => {
      this.handleUnauthorized();
    });

    // 监听存储变化（多标签页同步）
    window.addEventListener('storage', (e) => {
      if (e.key === 'token' || e.key === 'user') {
        this.syncAuthState();
      }
    });
  }

  /**
   * 初始化认证状态
   */
  async init() {
    try {
      // 从本地存储恢复认证状态
      this.token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (userStr) {
        try {
          this.user = JSON.parse(userStr);
        } catch (error) {
          console.error('Failed to parse user data:', error);
          this.clearAuthData();
        }
      }

      // 如果有token，验证其有效性
      if (this.token) {
        await this.verifyToken();
      }

      this.isInitialized = true;
      this.dispatchAuthEvent('auth:initialized', { user: this.user, token: this.token });
      
    } catch (error) {
      console.error('Auth initialization failed:', error);
      this.clearAuthData();
      this.isInitialized = true;
    }
  }

  /**
   * 验证token有效性
   */
  async verifyToken() {
    try {
      const response = await api.get('/api/auth/verify');
      
      if (response.data.success) {
        // 更新用户信息（可能有变化）
        this.user = response.data.user;
        this.saveUserData(this.user);
        return true;
      } else {
        this.clearAuthData();
        return false;
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      this.clearAuthData();
      return false;
    }
  }

  /**
   * 用户登录
   */
  async login(credentials) {
    try {
      const response = await api.post('/api/auth/login', credentials);

      if (response.data.success) {
        this.token = response.data.token;
        this.user = response.data.user;

        // 保存到本地存储
        this.saveAuthData(this.token, this.user);

        // 触发登录成功事件
        this.dispatchAuthEvent('auth:login', { user: this.user, token: this.token });

        return { success: true, user: this.user };
      } else {
        return {
          success: false,
          message: response.data.error || response.data.message || '登录失败',
          status: response.status
        };
      }
    } catch (error) {
      console.error('Login failed:', error);

      // 完全依赖后端返回的错误信息和状态码
      const message = (error.response && error.response.data && error.response.data.error)
        ? error.response.data.error
        : (error.message || '网络连接失败，请检查网络');

      return {
        success: false,
        message,
        status: error.status || 0
      };
    }
  }

  /**
   * 用户登出
   */
  async logout() {
    try {
      // 调用服务器登出接口
      if (this.token) {
        await api.post('/api/auth/logout');
      }
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      // 清除本地认证数据
      this.clearAuthData();
      
      // 触发登出事件
      this.dispatchAuthEvent('auth:logout');
      
      // 重定向到登录页
      this.redirectToLogin();
    }
  }

  /**
   * 处理未授权错误
   */
  handleUnauthorized() {
    this.clearAuthData();
    this.dispatchAuthEvent('auth:unauthorized');
    this.redirectToLogin();
  }

  /**
   * 同步认证状态（多标签页）
   */
  syncAuthState() {
    const newToken = localStorage.getItem('token');
    const newUserStr = localStorage.getItem('user');
    
    if (newToken !== this.token) {
      this.token = newToken;
      
      if (newUserStr) {
        try {
          this.user = JSON.parse(newUserStr);
        } catch (error) {
          this.user = null;
        }
      } else {
        this.user = null;
      }
      
      // 触发状态同步事件
      this.dispatchAuthEvent('auth:sync', { user: this.user, token: this.token });
    }
  }

  /**
   * 保存认证数据
   */
  saveAuthData(token, user) {
    localStorage.setItem('token', token);
    this.saveUserData(user);
    api.setToken(token);
  }

  /**
   * 保存用户数据
   */
  saveUserData(user) {
    localStorage.setItem('user', JSON.stringify(user));
  }

  /**
   * 清除认证数据
   */
  clearAuthData() {
    this.token = null;
    this.user = null;
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    api.clearToken();
  }

  /**
   * 重定向到登录页
   */
  redirectToLogin() {
    if (window.location.pathname !== '/login.html') {
      window.location.href = '/login.html';
    }
  }

  /**
   * 触发认证事件
   */
  dispatchAuthEvent(eventName, detail = {}) {
    window.dispatchEvent(new CustomEvent(eventName, { detail }));
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated() {
    return !!(this.token && this.user);
  }

  /**
   * 检查是否为管理员（包括超级管理员）
   */
  isAdmin() {
    return this.user && (this.user.role === 'admin' || this.user.role === 'super_admin');
  }

  /**
   * 检查是否为超级管理员
   */
  isSuperAdmin() {
    return this.user && this.user.role === 'super_admin';
  }

  /**
   * 检查用户权限
   */
  hasPermission(permission) {
    if (!this.user) return false;
    
    // 管理员拥有所有权限
    if (this.isAdmin()) return true;
    
    // 检查用户权限列表
    return this.user.permissions && this.user.permissions.includes(permission);
  }

  /**
   * 检查用户是否过期
   */
  isExpired() {
    return this.user && this.user.is_expired;
  }

  /**
   * 获取用户信息
   */
  getUser() {
    return this.user;
  }

  /**
   * 获取token
   */
  getToken() {
    return this.token;
  }

  /**
   * 获取用户显示名称
   */
  getUserDisplayName() {
    if (!this.user) return '';
    return this.user.display_name || this.user.username || this.user.email || '';
  }

  /**
   * 获取用户角色显示文本
   */
  getUserRoleText() {
    if (!this.user) return '';

    switch (this.user.role) {
      case 'super_admin':
        return '超级管理员';
      case 'admin':
        return '管理员';
      case 'user':
        return this.isExpired() ? '普通用户（已过期）' : '普通用户';
      default:
        return '未知角色';
    }
  }

  /**
   * 刷新用户信息
   */
  async refreshUser() {
    if (!this.token) return false;
    
    try {
      const response = await api.get('/api/auth/user');
      
      if (response.data.success) {
        this.user = response.data.user;
        this.saveUserData(this.user);
        this.dispatchAuthEvent('auth:user-updated', { user: this.user });
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to refresh user info:', error);
      return false;
    }
  }

  /**
   * 等待认证初始化完成
   */
  async waitForInit() {
    if (this.isInitialized) return;
    
    return new Promise((resolve) => {
      const checkInit = () => {
        if (this.isInitialized) {
          resolve();
        } else {
          setTimeout(checkInit, 50);
        }
      };
      checkInit();
    });
  }
}

// 创建全局认证管理器实例
const auth = new AuthManager();

// 导出认证管理器
export default auth;
